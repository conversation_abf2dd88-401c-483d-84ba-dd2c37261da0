
package com.bogo.messaging.coder.protobuf;

import com.bogo.messaging.constant.ChannelAttr;
import com.bogo.messaging.constant.DataType;
import com.bogo.messaging.exception.ReadInvalidTypeException;
import com.bogo.messaging.model.Pong;
import com.bogo.messaging.model.SentBody;
import com.bogo.messaging.model.proto.GoogleProtobuf;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufInputStream;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public class WebMessageDecoder extends MessageToMessageDecoder<BinaryWebSocketFrame> {

    @Override
    protected void decode(ChannelHandlerContext context, BinaryWebSocketFrame frame, List<Object> list) throws Exception {

        context.channel().attr(ChannelAttr.PING_COUNT).set(null);

        ByteBuf buffer = frame.content();

        byte type = buffer.readByte();

        if (DataType.PONG.getValue() == type) {
            list.add(Pong.getInstance());
            return;
        }

        if (DataType.SENT.getValue() == type) {
            list.add(getBody(buffer));
            return;
        }

        throw new ReadInvalidTypeException(type);

    }

    protected SentBody getBody(ByteBuf buffer) throws IOException {

        InputStream inputStream = new ByteBufInputStream(buffer);
        GoogleProtobuf.SentBody proto = GoogleProtobuf.SentBody.parseFrom(inputStream);

        SentBody body = new SentBody();
        body.setData(proto.getDataMap());
        body.setKey(proto.getKey());
        body.setTimestamp(proto.getTimestamp());

        return body;
    }
}
