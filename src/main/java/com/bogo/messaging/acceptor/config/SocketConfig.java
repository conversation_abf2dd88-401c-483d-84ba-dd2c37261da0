
package com.bogo.messaging.acceptor.config;

import com.bogo.messaging.handler.RequestHandler;
import java.time.Duration;
import lombok.Getter;
import lombok.Setter;


/**
 * TLV协议socket端口配置
 */
@Setter
public class SocketConfig {

    private static final int DEFAULT_PORT = 23456;

    /**
    TVL协议socket端口
     */
    private Integer port;

    /**
     * socket消息处理器
     */
    @Getter
    private RequestHandler outerRequestHandler;

    /**
    是否启用TVL协议socket
     */
    @Getter
    private boolean enable;

    /**
     长链接写空闲时间触发时间(s)
     心跳发送定时，每当x秒无数据下发写入，触发 服务端-->客户端 心跳事件
     */
    @Getter
    private Duration writeIdle = Duration.ofSeconds(45);

    /**
     长链接读空闲时间触发时间(s)
     心跳响应定时，每当readIdle - writeIdle秒无数据接收，触发心跳超时计数
     */
    @Getter
    private Duration readIdle = Duration.ofSeconds(60);


    /**
     长链接最大允许心跳响应超时次数
     达到该次数则 服务端断开链接
     */
    @Getter
    private int maxPongTimeout = 1;

    public Integer getPort() {
        return port == null || port <= 0 ? DEFAULT_PORT : port;
    }

}
