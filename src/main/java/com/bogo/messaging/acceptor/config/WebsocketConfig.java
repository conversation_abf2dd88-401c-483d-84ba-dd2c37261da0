
package com.bogo.messaging.acceptor.config;

import com.bogo.messaging.auth.AuthPredicateInfo;
import com.bogo.messaging.constant.WebsocketProtocol;
import java.util.function.Predicate;
import lombok.Getter;
import lombok.Setter;

/**
 * 基于websocket的服务配置
 */
@Setter
public class WebsocketConfig extends SocketConfig{

    private static final int DEFAULT_PORT = 34567;

    private static final String DEFAULT_PATH = "/";

    private static final WebsocketProtocol DEFAULT_PROTOCOL = WebsocketProtocol.PROTOBUF;

    /**
     * websocket端点地址
     */
    private String path;

    /**
     * 消息体协议，JSON 或者 Protobuf
     */
    private WebsocketProtocol protocol;

    /**
     * 鉴权实现
     */
    @Getter
    private Predicate<AuthPredicateInfo> authPredicate;


    @Override
    public Integer getPort() {
        return super.getPort() == null || super.getPort() <= 0 ? DEFAULT_PORT : super.getPort();
    }

    public String getPath() {
        return path == null ? DEFAULT_PATH : path;
    }

    public WebsocketProtocol getProtocol() {
        return protocol == null ? DEFAULT_PROTOCOL : protocol;
    }

}
