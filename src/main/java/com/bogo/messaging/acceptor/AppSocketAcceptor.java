
package com.bogo.messaging.acceptor;

import com.bogo.messaging.acceptor.config.SocketConfig;
import com.bogo.messaging.coder.protobuf.AppMessageDecoder;
import com.bogo.messaging.coder.protobuf.AppMessageEncoder;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import java.util.concurrent.TimeUnit;

/**
 * tlv协议端口
 * 可针对原生app使用
 */

@ChannelHandler.Sharable
public class AppSocketAcceptor extends NioSocketAcceptor {


	public AppSocketAcceptor(SocketConfig config){
		super(config);
	}


	/**
	 * bind基于tlv协议的socket端口
	 */
	@Override
	public void bind(){

		if (!socketConfig.isEnable()){
			return;
		}

		ServerBootstrap bootstrap = createServerBootstrap();
		bootstrap.childHandler(new ChannelInitializer<SocketChannel>() {
			@Override
			public void initChannel(SocketChannel ch){
				ch.pipeline().addLast(new AppMessageDecoder());
				ch.pipeline().addLast(new AppMessageEncoder());
				ch.pipeline().addLast(loggingHandler);
				ch.pipeline().addLast(new IdleStateHandler(socketConfig.getReadIdle().getSeconds(), socketConfig.getWriteIdle().getSeconds(), 0, TimeUnit.SECONDS));
				ch.pipeline().addLast(AppSocketAcceptor.this);
			}
		});

		ChannelFuture channelFuture = bootstrap.bind(socketConfig.getPort()).syncUninterruptibly();
		channelFuture.channel().newSucceededFuture().addListener(future -> {
			String logBanner = "\n\n" +
					"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\n" +
					"*                                                                                   *\n" +
					"*                                                                                   *\n" +
					"*                   App Socket Server started on port {}.                        *\n" +
					"*                                                                                   *\n" +
					"*                                                                                   *\n" +
					"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\n";
			logger.info(logBanner, socketConfig.getPort());
		});
		channelFuture.channel().closeFuture().addListener(future -> this.destroy());
	}

}
