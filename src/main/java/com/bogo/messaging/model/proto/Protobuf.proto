syntax = "proto3";
package com.bogo.messaging.model.proto;
option java_outer_classname="GoogleProtobuf";

message ReplyBody {
   string key = 1;
   string code = 2;
   string message = 3;
   int64 timestamp =4;
   map<string,string> data = 5;
   
}

message SentBody {
    string key = 1;
    int64 timestamp = 2;
    map<string, string> data = 3;
}
    

message Message {
   int64 id = 1;
   string action = 2;
   string content = 3;
   string sender = 4;
   string receiver = 5;
   string extra = 6;
   string title = 7;
   string format = 8;
   int64 timestamp = 9;
}
    