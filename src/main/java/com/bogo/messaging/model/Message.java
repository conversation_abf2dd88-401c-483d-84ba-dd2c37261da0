
package com.bogo.messaging.model;

import com.bogo.messaging.constant.DataType;
import com.bogo.messaging.model.proto.GoogleProtobuf;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * 消息对象
 */
@Setter
@Getter
public class Message implements Serializable, Transportable,Cloneable {

	private static final long serialVersionUID = 1L;

	/**
	 * 消息id
	 */
	private long id;

	/**
	 * 消息类型，用户自定义消息类别
	 */
	private String action;
	/**
	 * 消息标题
	 */
	private String title;
	/**
	 * 消息类容，于action 组合为任何类型消息，content 根据 format 可表示为 text,json ,xml数据格式
	 */
	private String content;

	/**
	 * 消息发送者账号
	 */
	private String sender;
	/**
	 * 消息发送者接收者
	 */
	private String receiver;

	/**
	 * content 内容格式
	 */
	private String format;

	/**
	 * 附加内容 内容
	 */
	private String extra;

	private long timestamp;

	public Message() {
		timestamp = System.currentTimeMillis();
	}


    @Override
	public String toString() {
		StringBuilder buffer = new StringBuilder();
		buffer.append("#Message#").append("\n");
		buffer.append("id       :").append(id).append("\n");
		buffer.append("sender   :").append(sender).append("\n");
		buffer.append("receiver :").append(receiver).append("\n");
		buffer.append("action   :").append(action).append("\n");
		buffer.append("content  :").append(content).append("\n");
		buffer.append("format   :").append(format).append("\n");
		buffer.append("extra    :").append(extra).append("\n");
		buffer.append("title    :").append(title).append("\n");
		buffer.append("timestamp:").append(timestamp);
		return buffer.toString();
	}

	@Override
	public Message clone(){
		Message message = new Message();
		message.id = id;
		message.action = action;
		message.title = title;
		message.content = content;
		message.sender = sender;
		message.receiver = receiver;
		message.extra = extra;
		message.format = format;
		message.timestamp = timestamp;
		return message;
	}

	@JsonIgnore
	@Override
	public byte[] getBody() {
		GoogleProtobuf.Message.Builder builder = GoogleProtobuf.Message.newBuilder();
		builder.setId(id);
		builder.setAction(action);
		builder.setSender(sender);
		builder.setTimestamp(timestamp);

		/*
		 * 下面字段可能为空
		 */

		if (receiver != null){
			builder.setReceiver(receiver);
		}

		if (content != null) {
			builder.setContent(content);
		}

		if (title != null) {
			builder.setTitle(title);
		}

		if (extra != null) {
			builder.setExtra(extra);
		}

		if (format != null) {
			builder.setFormat(format);
		}

		return builder.build().toByteArray();
	}

	@JsonIgnore
	@Override
	public DataType getType() {
		return DataType.MESSAGE;
	}
}
