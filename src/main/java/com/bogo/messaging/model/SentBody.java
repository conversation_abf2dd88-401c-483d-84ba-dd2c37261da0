
package com.bogo.messaging.model;


import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * java |android 客户端请求结构
 *
 */
@Getter
public class SentBody implements Serializable {

	private static final long serialVersionUID = 1L;

	@Setter
    private String key;

	private final Map<String, String> data = new HashMap<>();

	@Setter
    private long timestamp;

    public void setData(Map<String, String> data) {
		this.data.clear();
		this.data.putAll(data);
	}

    public String get(String key) {
		return data.get(key);
	}

	public String get(String key,String defaultValue) {
		return data.getOrDefault(key,defaultValue);
	}

    @Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("[SentBody]").append("\n");
		builder.append("key       :").append(this.getKey()).append("\n");
		builder.append("timestamp :").append(timestamp).append("\n");
		builder.append("data      :").append("\n{");
		data.forEach((k, v) -> builder.append("\n").append(k).append(":").append(v));
		builder.append(data.isEmpty() ? "" : "\n").append("}");
		return builder.toString();
	}

}
