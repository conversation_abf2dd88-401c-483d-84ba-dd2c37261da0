
package com.bogo.messaging.model;

import com.bogo.messaging.constant.DataType;
import com.bogo.messaging.model.proto.GoogleProtobuf;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 请求应答对象
 *
 */
@Getter
public class ReplyBody implements Serializable, Transportable {

	private static final long serialVersionUID = 1L;

	/**
	 * 请求key
	 */
	@Setter
    private String key;

	/**
	 * 返回码
	 */
	private String code;

	/**
	 * 返回说明
	 */
	@Setter
    private String message;

	/**
	 * 返回数据集合
	 */
	private final Map<String, String> data = new HashMap<>();

	@Setter
    private long timestamp;

	public ReplyBody() {
		timestamp = System.currentTimeMillis();
	}

    public void put(String k, String v) {
		data.put(k, v);
	}

	public String get(String k) {
		return data.get(k);
	}

	public void remove(String k) {
		data.remove(k);
	}

    public void setCode(String code) {
		this.code = code;
	}

	public void setCode(Number code) {
		this.code = code.toString();
	}

    @Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("[ReplyBody]").append("\n");
		builder.append("key       :").append(this.getKey()).append("\n");
		builder.append("code      :").append(code).append("\n");
		builder.append("message   :").append(message).append("\n");
		builder.append("timestamp :").append(timestamp).append("\n");
		builder.append("data      :").append("\n{");
		data.forEach((k, v) -> builder.append("\n").append(k).append(":").append(v));
		builder.append(data.isEmpty() ? "" : "\n").append("}");

		return builder.toString();
	}

	@JsonIgnore
	@Override
	public byte[] getBody() {
		GoogleProtobuf.ReplyBody.Builder builder = GoogleProtobuf.ReplyBody.newBuilder();
		builder.setCode(code);
		if (message != null) {
			builder.setMessage(message);
		}
		if (!data.isEmpty()) {
			builder.putAllData(data);
		}
		builder.setKey(key);
		builder.setTimestamp(timestamp);

		return builder.build().toByteArray();
	}

	@JsonIgnore
	@Override
	public DataType getType() {
		return DataType.REPLY;
	}

	public static ReplyBody make(String key,int code,String message){
		ReplyBody body = new ReplyBody();
		body.key = key;
		body.code = String.valueOf(code);
		body.message = message;
		return body;
	}
}
