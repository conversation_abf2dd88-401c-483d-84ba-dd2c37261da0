
package com.bogo.messaging.handler;


import com.bogo.messaging.constant.ChannelAttr;
import com.bogo.messaging.model.Ping;
import com.bogo.messaging.model.Pong;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import io.netty.handler.logging.LogLevel;

@ChannelHandler.Sharable
public class LoggingHandler extends io.netty.handler.logging.LoggingHandler {

	public LoggingHandler() {
		super(LogLevel.INFO);
	}

	@Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
		/* 不打印 Pong日志 */
		if (msg instanceof Pong) {
			ctx.fireChannelRead(msg);
			return;
		}

		String name = Thread.currentThread().getName();
		setThreadName(ctx);
		super.channelRead(ctx,msg);
		Thread.currentThread().setName(name);
	}

	@Override
	public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
		/* 不打印 Ping日志 */
		if (msg instanceof Ping) {
			ctx.write(msg, promise);
			return;
		}
		String name = Thread.currentThread().getName();
		setThreadName(ctx);
		super.write(ctx,msg,promise);
		Thread.currentThread().setName(name);
	}

	@Override
	public void close(ChannelHandlerContext ctx, ChannelPromise promise) {
		ctx.close(promise);
	}

	@Override
	public void channelActive(ChannelHandlerContext ctx) {
		ctx.fireChannelActive();
	}

	@Override
	public void channelInactive(ChannelHandlerContext ctx) {
		ctx.fireChannelInactive();
	}

	@Override
	public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
		ctx.fireUserEventTriggered(evt);
	}

	@Override
	public void channelRegistered(ChannelHandlerContext ctx) {
		ctx.fireChannelRegistered();
	}

	@Override
	public void channelUnregistered(ChannelHandlerContext ctx) {
		ctx.fireChannelUnregistered();
	}

	@Override
	public void deregister(ChannelHandlerContext ctx, ChannelPromise promise) {
		ctx.deregister(promise);
	}

	@Override
	public void channelReadComplete(ChannelHandlerContext ctx) {
		ctx.fireChannelReadComplete();
	}

	@Override
	public void flush(ChannelHandlerContext ctx) {
		ctx.flush();
	}

	@Override
	public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
		String name = Thread.currentThread().getName();
		setThreadName(ctx);
		logger.warn(this.format(ctx, "EXCEPTION", cause), cause);
		Thread.currentThread().setName(name);
	}

	private void setThreadName(ChannelHandlerContext context){
		String uid = context.channel().attr(ChannelAttr.UID).get();
		if (uid != null){
			Thread.currentThread().setName("nio-uid-" + uid);
		}
	}

}
