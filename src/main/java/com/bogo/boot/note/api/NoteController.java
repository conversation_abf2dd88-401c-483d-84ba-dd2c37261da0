
package com.bogo.boot.note.api;

import com.bogo.boot.group.api.request.NoteUpdateRequest;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.annotation.UpdateAction;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.note.api.request.NoteAddRequest;
import com.bogo.boot.note.api.request.NoteCopyRequest;
import com.bogo.boot.note.api.vo.NoteVO;
import com.bogo.boot.note.service.NoteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/note")
@Tag(name = "笔记收藏相关")
public class NoteController {

	@Resource
	private NoteService noteService;

	@Operation( summary = "新增笔记")
	@PostMapping(value = "/add")
	public ResponseEntity<Long> add(@Validated(CreateAction.class) @RequestBody NoteAddRequest request) {
		long id = noteService.add(request);
		return ResponseEntity.ok(id);
	}

	@Operation( summary = "收藏为笔记",description = "如聊天场景中附件内容另存为到笔记")
	@PostMapping(value = "/copy")
	public ResponseEntity<Long> copy(@Validated(CreateAction.class) @RequestBody NoteCopyRequest request) {
		long id = noteService.copy(request);
		return ResponseEntity.ok(id);
	}

	@Operation( summary = "修改笔记")
	@PostMapping(value = "/update")
	public ResponseEntity<Void> update(@Validated(UpdateAction.class) @RequestBody NoteUpdateRequest request) {
		noteService.update(request);
		return ResponseEntity.ok();
	}

	@Operation( summary = "删除笔记")
	@Parameter(name = "id", description = "笔记ID", in = ParameterIn.PATH,  example = "0")
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> delete(@PathVariable long id) {
		noteService.delete(id);
		return ResponseEntity.ok();
	}


	@Operation( summary = "获取所有笔记")
	@GetMapping(value = "/list")
	public ResponseEntity<List<NoteVO>> list(@Parameter(hidden = true) @UID Long uid) {
		return ResponseEntity.ok(noteService.findList(uid).stream().map(NoteVO::of).collect(Collectors.toList()));
	}

	@Operation( summary = "查看笔记")
	@GetMapping(value = "/{id}")
	public ResponseEntity<NoteVO> get(@PathVariable long id) {
		return ResponseEntity.ok(NoteVO.of(noteService.findOne(id)));
	}
}
