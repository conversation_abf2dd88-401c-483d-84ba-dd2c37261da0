
package com.bogo.boot.note.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "创建笔记请求体")
@Getter
@Setter
public class NoteAddRequest implements Serializable {

	@Schema(title = "文字描述内容")
	private String text;

	@Schema(title = "附件、多媒体、位置等内容")
	private String content;

	@Schema(title = "内容格式 0:文字 1:图片 2:视频 3:附件 4:位置 5:链接")
	@NotNull(message = "内容格式不能为空",groups = CreateAction.class)
	private Byte format;

	@Schema(title = "扩展内容")
	private String extra;

}
