package com.bogo.boot.note.api.vo;

import com.bogo.boot.note.entity.Note;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "笔记信息")
@Getter
@Setter
public class NoteVO {

    private Long id;

    @Schema(title = "文字描述内容")
    private String text;

    @Schema(title = "附件、多媒体等内容")
    private String content;

    @Schema(title = "内容格式 0:文字 1:图片 2:视频 3:附件 4:位置 5:链接")
    private Byte format;

    @Schema(title = "扩展信息")
    private String extra;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "修改时间")
    private Date updateTime;

    public static NoteVO of(Note note){
        if (note == null){
            return null;
        }
        NoteVO vo = new NoteVO();
        vo.id = note.getId();
        vo.content = note.getContent();
        vo.extra = note.getExtra();
        vo.format = note.getFormat();
        vo.text = note.getText();
        vo.createTime = note.getCreateTime();
        vo.updateTime = note.getUpdateTime();
        return vo;
    }
}
