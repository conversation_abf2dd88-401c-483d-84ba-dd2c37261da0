
package com.bogo.boot.note.repository;

import com.bogo.boot.note.entity.Note;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface NoteRepository extends JpaRepository<Note, Long> {
	@Query("from Note where uid =?1 order by id desc")
	List<Note> findList(Long uid);
}
