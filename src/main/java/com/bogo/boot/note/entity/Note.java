
package com.bogo.boot.note.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 收藏、笔记信息
 */
@Entity
@Table(name = "t_bogo_note")
@Getter
@Setter
public class Note {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;
	
	@Column(name = "uid",nullable = false)
	private Long uid;

	@Column(name = "text", length = 1000)
	private String text;

	@Column(name = "content",nullable = false)
	private String content;

	/**
	 * 内容格式
	 * @see com.bogo.boot.note.constant.NoteFormat
	 */
	@Column(name = "format", length = 1,nullable = false)
	private Byte format;

	@Column(name = "extra",nullable = false)
	private String extra;

	@Column(name = "create_time",nullable = false)
	private Date createTime;

	@Column(name = "update_time",nullable = false)
	private Date updateTime;

}
