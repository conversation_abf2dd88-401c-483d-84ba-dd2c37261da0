
package com.bogo.boot.note.service.impl;

import com.bogo.boot.group.api.request.NoteUpdateRequest;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.model.EventMessageCompat;
import com.bogo.boot.message.pusher.DefaultMessagePusher;
import com.bogo.boot.note.api.request.NoteAddRequest;
import com.bogo.boot.note.api.request.NoteCopyRequest;
import com.bogo.boot.note.entity.Note;
import com.bogo.boot.note.event.NoteCopyEvent;
import com.bogo.boot.note.event.NoteDeleteEvent;
import com.bogo.boot.note.repository.NoteRepository;
import com.bogo.boot.note.service.NoteService;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class NoteServiceImpl implements NoteService {

	@Resource
	private NoteRepository noteRepository;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private DefaultMessagePusher defaultMessagePusher;

	@Override
	public long add(NoteAddRequest request) {

		Note note = new Note();
		note.setContent(request.getContent());
		note.setFormat(request.getFormat());
		note.setExtra(request.getExtra());
		note.setText(request.getText());
		note.setUid(UidHolder.getUid());
		note.setCreateTime(new Date());
		note.setUpdateTime(new Date());

		noteRepository.saveAndFlush(note);

		defaultMessagePusher.push(
				EventMessageCompat.from()
				.setAction(MessageAction.ACTION_115)
				.setContent(String.valueOf(note.getId()))
				.setReceiver(note.getUid())
				.setIgnoreCurrentChannel(true)
		);

		return note.getId();
	}

	@Override
	public long copy(NoteCopyRequest request) {
		Note note = new Note();
		note.setContent(request.getContent());
		note.setFormat(request.getFormat());
		note.setExtra(request.getExtra());
		note.setUid(UidHolder.getUid());
		note.setCreateTime(new Date());
		note.setUpdateTime(new Date());
		noteRepository.saveAndFlush(note);

		applicationEventProducer.publishAsync(new NoteCopyEvent(note));

		defaultMessagePusher.push(
				EventMessageCompat.from()
						.setAction(MessageAction.ACTION_115)
						.setContent(String.valueOf(note.getId()))
						.setReceiver(note.getUid())
						.setIgnoreCurrentChannel(true)
		);


		return note.getId();
	}

	@Override
	public void update(NoteUpdateRequest request) {

		Note target = noteRepository.findById(request.getId()).orElse(null);

		if (target == null || UidHolder.isNotCurrentUid(target.getUid())){
			return;
		}

		target.setUpdateTime(new Date());
		target.setText(request.getText());
		target.setContent(request.getContent());
		target.setFormat(request.getFormat());
		target.setExtra(request.getExtra());

		noteRepository.saveAndFlush(target);

		defaultMessagePusher.push(
				EventMessageCompat.from()
						.setAction(MessageAction.ACTION_116)
						.setContent(String.valueOf(target.getId()))
						.setReceiver(target.getUid())
						.setIgnoreCurrentChannel(true)
		);

	}

	@Override
	public void delete(long id) {

		Note note = noteRepository.findById(id).orElse(null);
		if (note == null || UidHolder.isNotCurrentUid(note.getUid())){
			return;
		}

		noteRepository.delete(note);

		applicationEventProducer.publishAsync(new NoteDeleteEvent(note));

		defaultMessagePusher.push(
				EventMessageCompat.from()
						.setAction(MessageAction.ACTION_117)
						.setContent(String.valueOf(note.getId()))
						.setReceiver(note.getUid())
						.setIgnoreCurrentChannel(true)
		);

	}

	@Override
	public List<Note> findList(long uid) {
		return noteRepository.findList(uid);
	}

	@Override
	public Note findOne(long id) {
		Note note = noteRepository.findById(id).orElse(null);
		if (note == null || UidHolder.isNotCurrentUid(note.getUid())){
			return null;
		}
		return note;
	}

}
