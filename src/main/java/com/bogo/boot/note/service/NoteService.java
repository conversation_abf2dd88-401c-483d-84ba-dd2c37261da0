
package com.bogo.boot.note.service;

import com.bogo.boot.group.api.request.NoteUpdateRequest;
import com.bogo.boot.note.api.request.NoteAddRequest;
import com.bogo.boot.note.api.request.NoteCopyRequest;
import com.bogo.boot.note.entity.Note;
import java.util.List;

public interface NoteService {

	long add(NoteAddRequest request);

	long copy(NoteCopyRequest request);

	void update(NoteUpdateRequest request);

	void delete(long id);

	List<Note> findList(long uid);

	Note findOne(long id);

}
