
package com.bogo.boot.account.repository;

import com.bogo.boot.account.entity.User;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface UserRepository extends JpaRepository<User, Long> {

	@Query("select u.id from User as u")
	List<Long> findIdList();

	@Query("from User where telephone = ?1")
	User findOne(String telephone);

	@Query("select count(*) from User where telephone = ?1")
	long count(String telephone);

	@Query("select id from User where telephone = ?1")
	Long findId(String telephone);

	@Query("select name from User where id = ?1")
	String findName(long id);

	@Query("select organizationId from User where id = ?1")
	Long getOrganizationId(Long id);

	@Modifying
	@Query("UPDATE User set name = ?2 where id = ?1")
	void updateName(long id , String name);

	@Modifying
	@Query("UPDATE User set motto = ?2 where id = ?1")
	void updateMotto(long id , String motto);

	@Modifying
	@Query("UPDATE User set state = ?2 where id = ?1")
	void updateState(long id , byte state);

	@Modifying
	@Query("UPDATE User set password = ?2 where id = ?1")
	void updatePassword(long id , String password);

	@Query("select password from User where id = ?1")
	String findPassword(long id);

	@Query("from User where organizationId = ?1 order by id")
	List<User> findList(Long organizationId);

	@Query("select id from User where organizationId = ?1")
	List<Long> findIdList(Long organizationId);

	@Query("from User where id in (?1)")
	List<User> findList(Collection<Long> idList);

	@Modifying
	@Query("UPDATE User set email = ?2 where id = ?1")
	void updateEmail(long id , String email);

	@Modifying
	@Query("UPDATE User set gender = ?2 where id = ?1")
	void updateGender(long id , byte gender);
}
