
package com.bogo.boot.account.repository;

import com.bogo.boot.account.constant.SessionState;
import com.bogo.boot.account.entity.Session;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface SessionRepository extends JpaRepository<Session, Long> {

	@Query("SELECT uid from Session where state in (0,1)")
	List<Long> findUidList();

	@Query("SELECT count(*) from Session where uid = ?1")
	Long count(long uid);

	@Modifying
	@Query("delete from Session where id = ?1")
	void delete(Long id);

	@Modifying
	@Query("delete from Session where uid = :uid and channel = :channel")
	void delete(Long uid,String channel);

	@Query("select id from Session where uid = ?1 and deviceId = ?2")
	Long findId(Long uid,String deviceId);

	@Modifying
	@Query("delete from Session where host = ?1")
	void delete(String host);

	@Modifying
	@Query("update Session set state = ?2 where id = ?1")
	void updateState(Long id,byte state);

	@Modifying
	@Query("update Session set state = " + SessionState.APNS + " where uid = ?1 and channel = ?2")
	void openApns(Long uid,String channel);

	@Modifying
	@Query("update Session set state = " + SessionState.ACTIVE + " where uid = ?1 and channel = ?2")
	void closeApns(Long uid,String channel);

}
