
package com.bogo.boot.account.repository;

import com.bogo.boot.account.entity.SilentNotification;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface SilentNotificationRepository extends JpaRepository<SilentNotification, String> {

	@Query("from SilentNotification where uid =:uid")
	List<SilentNotification> findList(Long uid);

	@Query("select count(*) from SilentNotification where uid =:uid and targetId =:targetId and type = :type")
	long count(Long uid, Long targetId,byte type);

	@Modifying
	@Query("delete from SilentNotification where uid =:uid and targetId=:targetId and type = :type")
	void delete(Long uid, Long targetId, byte type);
}
