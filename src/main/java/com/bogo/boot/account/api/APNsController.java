
package com.bogo.boot.account.api;

import com.bogo.boot.account.service.SessionService;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apns")
@Tag(name = "APNs推送相关")
public class APNsController {

	@Resource
	private SessionService sessionService;

	@Operation( summary = "开启APNs")
	@Parameter(name = "deviceToken", description = "APNs的deviceToken", in = ParameterIn.QUERY, required = true)
	@PostMapping(value = "/open")
	public ResponseEntity<Void> open(@Parameter(hidden = true) @UID Long uid , @RequestParam String deviceToken) {

		sessionService.openApns(uid,deviceToken);

		return ResponseEntity.ok();
	}

	@Operation( summary = "关闭APNs")
	@PostMapping(value = "/close")
	public ResponseEntity<Void> close(@Parameter(hidden = true) @UID Long uid) {

		sessionService.closeApns(uid);

		return ResponseEntity.ok();
	}
}
