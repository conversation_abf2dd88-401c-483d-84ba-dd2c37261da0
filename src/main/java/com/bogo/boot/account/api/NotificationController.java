
package com.bogo.boot.account.api;

import com.bogo.boot.account.service.SilentNotificationService;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.message.api.vo.SilentNotificationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/notification/silent")
@Tag(name = "消息免打扰接口" )
public class NotificationController {

	@Resource
	private SilentNotificationService silentNotificationService;

	@Operation( summary = "取消免打扰")
	@Parameters({
			@Parameter(name = "targetId", description = "目标ID,系统传:0", in = ParameterIn.QUERY,example = "0"),
			@Parameter(name = "type", description = "目标类型 1:联系人 2:群 3:系统 4:公众号", in = ParameterIn.QUERY,example = "0")
	})
	@DeleteMapping(value = "")
	public ResponseEntity<Void> cancel(@Parameter(hidden = true) @UID Long uid , @RequestParam Long targetId, @RequestParam byte type) {

		silentNotificationService.cancel(uid,targetId,type);
		
		return ResponseEntity.ok();
	}

	@Operation( summary = "设置免打扰")
	@Parameters({
			@Parameter(name = "targetId", description = "目标ID,系统传:0", in = ParameterIn.QUERY,example = "0"),
			@Parameter(name = "type", description = "目标类型 1:联系人 2:群 3:系统 4:公众号", in = ParameterIn.QUERY,example = "0")
	})
	@PostMapping(value = "")
	public ResponseEntity<Void> add(@Parameter(hidden = true) @UID Long uid,@RequestParam Long targetId, @RequestParam byte type) {

		silentNotificationService.add(uid,targetId,type);

		return ResponseEntity.ok();
	}

	@Operation(summary = "获取免打扰列表")
	@GetMapping(value = "/list")
	public ResponseEntity<List<SilentNotificationVO>> findList(@Parameter(hidden = true) @UID Long uid) {
		return ResponseEntity.ok(silentNotificationService.findList(uid).stream().map(SilentNotificationVO::of).collect(Collectors.toList()));
	}

}
