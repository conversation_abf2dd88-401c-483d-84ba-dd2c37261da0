
package com.bogo.boot.account.api;

import com.bogo.boot.account.api.vo.PrimaryDataVO;
import com.bogo.boot.account.api.vo.SecondDataVO;
import com.bogo.boot.account.api.vo.UserVO;
import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.service.SilentNotificationService;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.app.service.MicroServerService;
import com.bogo.boot.contact.api.vo.DepartmentMemberVO;
import com.bogo.boot.contact.api.vo.DepartmentVO;
import com.bogo.boot.contact.api.vo.OrganizationVO;
import com.bogo.boot.contact.service.FriendService;
import com.bogo.boot.contact.service.OrganizationService;
import com.bogo.boot.emoji.service.EmoticonService;
import com.bogo.boot.group.bo.GroupBO;
import com.bogo.boot.group.service.GroupService;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.message.api.vo.SilentNotificationVO;
import com.bogo.boot.moment.service.MomentRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/account")
@Tag(name = "基础数据相关接口")
public class AccountController {
 
	@Resource
	private GroupService groupService;

	@Resource
	private FriendService friendService;

	@Resource
	private MicroServerService microServerService;

	@Resource
	private MomentRuleService momentRuleService;

	@Resource
	private EmoticonService emoticonService;

	@Resource
	private UserService userService;

	@Resource
	private OrganizationService organizationService;

	@Resource
	private SilentNotificationService silentNotificationService;

	@GetMapping(value = "/data")
	@Operation( summary = "登录后获取主要初始数据",description = "[好友列表，群组列表，公众号列表，组织联系人]")
	public ResponseEntity<PrimaryDataVO> getPrimaryData(@Parameter(hidden = true) @UID Long uid) {

		PrimaryDataVO vo = new PrimaryDataVO();

		vo.setGroupList(groupService.findList(uid).stream().map(GroupBO::toVO).toList());

		vo.setMicroServerList(microServerService.findList(uid));

		User user = userService.findOne(uid);

		Map<Long,String> friendAliasMap = friendService.findAliasMap(user.getId());

		if (user.getOrganizationId() == null){
			vo.setFriendList(userService.findList(friendAliasMap.keySet()).stream().map(UserVO::of).toList(),friendAliasMap);
			return ResponseEntity.ok(vo);
		}


		OrganizationVO organization = OrganizationVO.of(organizationService.findOne(user.getOrganizationId()));
		organization.setDepartmentList(organizationService.findDepartments(organization.getId()).stream().map(DepartmentVO::of).toList());
		organization.setMemberList(organizationService.findMembers(organization.getId()).stream().map(DepartmentMemberVO::of).toList());
		vo.setOrganization(organization);

		List<Long> orgUidList = organization.getMemberList().stream().map(DepartmentMemberVO::getUid).toList();

		Collection<Long> friendUidList = friendAliasMap.keySet();

		Collection<Long> bothUidList = CollectionUtils.intersection(orgUidList,friendUidList);

		Collection<Long> onlyFriendUidList = CollectionUtils.subtract(friendUidList,orgUidList);

		Collection<Long> onlyOrgUidList = CollectionUtils.subtract(orgUidList,friendUidList);

		vo.setContactList(userService.findList(onlyOrgUidList).stream().map(UserVO::of).toList(),friendAliasMap);

		vo.setFriendList(userService.findList(onlyFriendUidList).stream().map(UserVO::of).toList(),friendAliasMap);

		vo.setBothList(userService.findList(bothUidList).stream().map(UserVO::of).toList(),friendAliasMap);

		return ResponseEntity.ok(vo);
	}

	@GetMapping(value = "/data/second")
	@Operation( summary = "登录后获取次要初始数据",description = "[订阅表情包，朋友圈权限，免打扰通知]")
	public ResponseEntity<SecondDataVO> getSecondData(@Parameter(hidden = true) @UID Long uid) {

		SecondDataVO vo = new SecondDataVO();

		vo.setEmoticonList(emoticonService.findList(uid));

		vo.setMomentIgnoredList(momentRuleService.findMeIgnoreList(uid));

		vo.setMomentBlackedList(momentRuleService.findMeBlockedList(uid));

		vo.setSilentNotificationList(silentNotificationService.findList(uid).stream().map(SilentNotificationVO::of).collect(Collectors.toList()));

		return ResponseEntity.ok(vo);
	}

}
