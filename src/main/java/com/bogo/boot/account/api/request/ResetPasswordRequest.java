
package com.bogo.boot.account.api.request;

import com.bogo.boot.infra.annotation.UpdateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Schema
@Getter
@Setter
public class ResetPasswordRequest implements Serializable {

	@Schema(title = "手机号码")
	@NotBlank(message = "telephone不能为空",groups = UpdateAction.class)
	private String telephone;

	@Schema(title = "验证码")
	@NotBlank(message = "code不能为空",groups = UpdateAction.class)
	private String code;

	@Schema(title = "密码密文")
	@NotBlank(message = "password不能为空",groups = UpdateAction.class)
	private String password;

}
