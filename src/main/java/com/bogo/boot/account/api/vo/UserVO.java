package com.bogo.boot.account.api.vo;


import com.bogo.boot.account.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "用户信息")
@Getter
@Setter
public class UserVO {

    @Schema(title = "用户ID")
    private Long id;

    @Schema(title = "用户名称")
    private String name;

    @Schema(title = "备注名称")
    private String alias;

    @Schema(title = "手机号")
    private String telephone;

    @Schema(title = "组织ID")
    private Long organizationId;

    @Schema(title = "邮箱")
    private String email;

    @Schema(title = "性别 0:女 1:男")
    private byte gender;

    @Schema(title = "格言信息")
    private String motto;

    public static UserVO of(User user){
        UserVO vo = new UserVO();
        vo.setId(user.getId());
        vo.setName(user.getName());
        vo.setEmail(user.getEmail());
        vo.setTelephone(user.getTelephone());
        vo.setOrganizationId(user.getOrganizationId());
        vo.setGender(user.getGender());
        vo.setMotto(user.getMotto());
        return vo;
    }

    public static UserVO of(User user, String alias){
        UserVO vo = of(user);
        vo.setAlias(alias);
        return vo;
    }
}
