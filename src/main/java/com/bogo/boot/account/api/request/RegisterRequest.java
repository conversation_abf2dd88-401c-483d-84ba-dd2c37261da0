
package com.bogo.boot.account.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.NameFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Schema
@Getter
@Setter
public class RegisterRequest implements Serializable {

	@Schema(title = "名称")
	@NotBlank(message = "name不能为空",groups = CreateAction.class)
	@NameFormat(message = "名称不能存在特殊字符", groups = CreateAction.class)
	private String name;

	@Schema(title = "手机号码")
	@NotBlank(message = "telephone不能为空",groups = CreateAction.class)
	private String telephone;

	@Schema(title = "验证码")
	@NotBlank(message = "code不能为空",groups = CreateAction.class)
	private String code;

	@Schema(title = "密码(md5)")
	@NotBlank(message = "password不能为空",groups = CreateAction.class)
	private String password;

	@Schema(title = "性别 0女 1男")
	private Byte gender;

}
