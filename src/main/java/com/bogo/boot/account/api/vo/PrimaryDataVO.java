package com.bogo.boot.account.api.vo;

import com.bogo.boot.app.api.vo.MicroServerVO;
import com.bogo.boot.contact.api.vo.OrganizationVO;
import com.bogo.boot.group.api.vo.GroupVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "主要初始数据")
@Getter
@Setter
public class PrimaryDataVO {

    @Schema(title = "我相关的群列表")
    private List<GroupVO> groupList;

    @Schema(title = "仅仅是好友列表")
    private List<UserVO> friendList;

    @Schema(title = "仅仅是组织成员列表")
    private List<UserVO> contactList;

    @Schema(title = "组织成员又是好友列表")
    private List<UserVO> bothList;

    @Schema(title = "关注的公众号列表")
    private List<MicroServerVO> microServerList;

    @Schema(title = "我的组织")
    private OrganizationVO organization;


    public void setFriendList(List<UserVO> friendList, Map<Long,String> aliasMap) {
        this.mappingAliasName(friendList,aliasMap);
        this.friendList = friendList;
    }

    public void setContactList(List<UserVO> contactList, Map<Long,String> aliasMap) {
        this.mappingAliasName(contactList,aliasMap);
        this.contactList = contactList;
    }


    public void setBothList(List<UserVO> bothList, Map<Long,String> aliasMap) {
        this.mappingAliasName(bothList,aliasMap);
        this.bothList = bothList;
    }

    private void mappingAliasName(List<UserVO> vos, Map<Long,String> aliasMap){
        for (UserVO vo : vos){
            vo.setAlias(aliasMap.get(vo.getId()));
        }
    }
}
