
package com.bogo.boot.account.api;

import com.bogo.boot.account.api.request.RegisterRequest;
import com.bogo.boot.account.api.request.ResetPasswordRequest;
import com.bogo.boot.account.api.vo.UserVO;
import com.bogo.boot.account.constant.UserState;
import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.event.LogoutEvent;
import com.bogo.boot.account.service.AccessTokenService;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.infra.annotation.AccessToken;
import com.bogo.boot.infra.annotation.ClientOrigin;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.GreenFunction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.annotation.UpdateAction;
import com.bogo.boot.infra.constant.ValidationCodeAction;
import com.bogo.boot.infra.holder.EnvironmentHolder;
import com.bogo.boot.infra.model.Origin;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.service.ValidationCodeService;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.Password;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Email;
import java.util.List;
import java.util.Map;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user")
@Tag(name = "用户相关接口" )
public class UserController {

	@Resource
	private UserService userService;

	@Resource
	private AccessTokenService accessTokenService;

	@Resource
	private ValidationCodeService validationCodeService;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private EnvironmentHolder environmentHolder;

	@Operation( summary = "通过手机号查询用户ID")
	@Parameter(name = "telephone", description = "手机号", in = ParameterIn.PATH, required = true)
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "404" ,description = "手机号码不存在")
	})
	@GreenFunction
	@GetMapping(value = "/id/{telephone}")
	public ResponseEntity<Long> findId(@PathVariable String telephone) {

		Long id = userService.findId(telephone);

		if (id == null) {
			return ResponseEntity.make(HttpStatus.NOT_FOUND, "手机号码不存在");
		}

		return ResponseEntity.ok(id);
	}

	@Operation( summary = "通过手机号查询用户")
	@Parameter(name = "telephone", description = "手机号", in = ParameterIn.PATH, required = true)
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "404" ,description = "手机号码不存在")
	})
	@GetMapping(value = "/find/{telephone}")
	public ResponseEntity<UserVO> get(@PathVariable String telephone) {
		User target = userService.findOne(telephone);
		if (target == null){
			return ResponseEntity.make(HttpStatus.NOT_FOUND, "手机号码不存在");
		}
		return ResponseEntity.ok(UserVO.of(target));
	}

	@Operation( summary = "查询自己用户信息")
	@GetMapping(value = "")
	public ResponseEntity<UserVO> getSelf(@Parameter(hidden = true) @UID long uid) {
		User target = userService.findOne(uid);
		return ResponseEntity.ok(UserVO.of(target));
	}

	@Operation( summary = "查询单个用户信息")
	@Parameter(name = "id", description =  "ID", in = ParameterIn.PATH, required = true)
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "404" ,description = "UID不存在")
	})
	@GetMapping(value = "/{id}")
	public ResponseEntity<UserVO> get(@PathVariable long id) {
		User target = userService.findOne(id);
		if (target == null){
			return ResponseEntity.make(HttpStatus.NOT_FOUND, "UID不存在");
		}
		return ResponseEntity.ok(UserVO.of(target));
	}

	@Operation( summary = "查询多个用户信息")
	@Parameter(name = "id",description = "ID列表", in = ParameterIn.QUERY, required = true)
	@GetMapping(value = "/list")
	public ResponseEntity<List<UserVO>> get(@RequestParam("id") List<Long> idList) {
		return ResponseEntity.ok(userService.findList(idList).stream().map(UserVO::of).toList());
	}

	@Operation( summary = "批量查询用户名称")
	@Parameter(name = "id",description = "ID列表", in = ParameterIn.QUERY, required = true)
	@GetMapping(value = "/name")
	public ResponseEntity<Map<Long,String>> getNameList(@RequestParam("id") List<Long> idList) {
		return ResponseEntity.ok(userService.findName(idList));
	}

	@Operation( summary = "登录")
	@Parameters({
			@Parameter(name = "telephone", description = "手机号码", in = ParameterIn.QUERY),
			@Parameter(name = "password", description = "密码(MD5)", in = ParameterIn.QUERY),
	})
	@ApiResponses(value = {@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "404" ,description = "账号不存在"),
			@ApiResponse(responseCode = "423" ,description = "账号被禁用"),
			@ApiResponse(responseCode = "403" ,description = "密码不存在")})
	@GreenFunction
	@PostMapping(value = "/login")
	public ResponseEntity<UserVO> login(@Parameter(hidden = true)  @ClientOrigin Origin origin,
								   @RequestParam String telephone,
								   @RequestParam String password) {

		User user = userService.findOne(telephone);

		if (user == null) {
			return ResponseEntity.make(HttpStatus.NOT_FOUND.value(),"手机号码不存在");
		}

		if (user.getState() == UserState.DISABLED.getValue()) {
			return ResponseEntity.make(HttpStatus.LOCKED.value(),"该账号已经被禁用");
		}

		if (user.getState() == UserState.DESTROY.getValue()) {
			return ResponseEntity.make(HttpStatus.LOCKED.value(),"该账号已经被注销");
		}

		if (!Password.check(password,user.getPassword())) {
			return ResponseEntity.make(HttpStatus.FORBIDDEN.value(),"密码不正确");
		}

		ResponseEntity<UserVO> responseEntity = ResponseEntity.ok(UserVO.of(user));

		responseEntity.setToken(accessTokenService.generate(origin,user.getId()));

		return responseEntity;
	}


	@Operation( summary = "注册")
	@ApiResponses(value = {@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "409" ,description = "手机号码已经注册"),
			@ApiResponse(responseCode = "403" ,description = "验证码不正确")})
	@GreenFunction
	@PostMapping(value = "/register")
	public ResponseEntity<UserVO> register(@Parameter(hidden = true) @ClientOrigin Origin origin,
										   @Validated(CreateAction.class) @RequestBody RegisterRequest request) {

		if (userService.isExist(request.getTelephone())) {
			return ResponseEntity.make(HttpStatus.CONFLICT.value(),"该手机号码已经注册");
		}

		if (!validationCodeService.check(request.getTelephone(),request.getCode(), ValidationCodeAction.REGISTER)){
			return ResponseEntity.make(HttpStatus.FORBIDDEN.value(),"验证码不正确");
		}

		User user = new User();
		user.setName(request.getName());
		user.setTelephone(request.getTelephone());
		user.setPassword(request.getPassword());
		user.setState(UserState.NORMAL.getValue());

		userService.save(user);

		ResponseEntity<UserVO> responseEntity = ResponseEntity.ok(UserVO.of(user));

		responseEntity.setToken(accessTokenService.generate(origin,user.getId()));

		return responseEntity;
	}

	@Operation( summary = "注销账号")
	@PostMapping(value = "/unregister")
	public ResponseEntity<Void> unregister(@Parameter(hidden = true) @UID long uid) {
		
		userService.destroy(uid);

		return ResponseEntity.ok();
	}

	@Operation( summary = "退出登录")
	@GetMapping(value = "/logout")
	public ResponseEntity<Void> logout(@Parameter(hidden = true) @ClientOrigin Origin origin,
									   @Parameter(hidden = true) @UID Long uid,
									   @Parameter(hidden = true) @AccessToken String token) {
		accessTokenService.delete(origin,token);
		applicationEventProducer.publish(new LogoutEvent(uid));
		return ResponseEntity.ok();
	}


	@Operation( summary = "修改名称")
	@Parameter(name = "name", description = "名称", in = ParameterIn.QUERY, required = true)
	@PatchMapping(value = "/name")
	public ResponseEntity<Void> updateName(
			@Validated @Length(max = 10, message = "名称不错超过10个字符") @RequestParam String name,
			@Parameter(hidden = true) @UID Long uid) {

		userService.updateName(uid,name);
		return ResponseEntity.ok();
	}

	@Operation( summary = "个性签名")
	@Parameter(name = "motto", description = "个性签名", in = ParameterIn.QUERY, required = true)
	@PatchMapping(value = "/motto")
	public ResponseEntity<Void> updateMotto(@RequestParam String motto, @Parameter(hidden = true) @UID Long uid) {
		userService.updateMotto(uid,motto);
		return ResponseEntity.ok();
	}

	@Operation( summary = "修改密码")
	@Parameters({
			@Parameter(name = "oldPassword", description = "旧密码(MD5)", in = ParameterIn.QUERY),
			@Parameter(name = "newPassword", description = "新密码(MD5)", in = ParameterIn.QUERY),
	})
	@ApiResponses(value = {@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "403" ,description = "密码不正确")})
	@PatchMapping(value = "/password")
	public ResponseEntity<Void> password(@RequestParam String oldPassword, @RequestParam String newPassword, @Parameter(hidden = true) @UID Long uid) {


		if (!userService.checkPassword(uid,oldPassword.toUpperCase())) {
			return ResponseEntity.make(HttpStatus.FORBIDDEN.value(),"密码不正确");
		}

		userService.updatePassword(uid,newPassword.toUpperCase());
		return ResponseEntity.ok();
	}


	@Operation( summary = "重新设置密码")
	@ApiResponses(value = {@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "403" ,description = "验证码不正确"),
			@ApiResponse(responseCode = "404" ,description = "手机号码未注册")})
	@GreenFunction
	@PostMapping(value = "/forgot")
	public ResponseEntity<Void> forgot(	@Validated(UpdateAction.class) @RequestBody ResetPasswordRequest request) {

		if (!validationCodeService.check(request.getTelephone(),request.getCode(), ValidationCodeAction.FORGOT)){
			return ResponseEntity.make(HttpStatus.FORBIDDEN.value(),"验证码不正确");
		}

		Long id = userService.findId(request.getTelephone());

		if (id == null){
			return ResponseEntity.make(HttpStatus.NOT_FOUND.value(),"手机号码未注册");
		}

		userService.updatePassword(id,request.getPassword());

		return ResponseEntity.ok();
	}

	@Operation( summary = "修改邮箱")
	@PatchMapping(value = "/email")
	public ResponseEntity<Void> updateEmail(@Validated @Email(message = "请输入正确的邮箱地址") @RequestParam String email, @Parameter(hidden = true) @UID Long uid) {
		userService.updateEmail(uid,email);
		return ResponseEntity.ok();
	}

	@Operation( summary = "修改性别")
	@Parameter(name = "gender", description = "性别,0:女 1:男", in = ParameterIn.QUERY, required = true)
	@PatchMapping(value = "/gender")
	public ResponseEntity<Void> updateGender(@Validated @Range(min = 0,max = 1, message = "性别取值为0或者1") @RequestParam byte gender, @Parameter(hidden = true) @UID Long uid) {
		userService.updateGender(uid,gender);
		return ResponseEntity.ok();
	}

}
