package com.bogo.boot.account.api.vo;

import com.bogo.boot.emoji.api.vo.EmoticonVO;
import com.bogo.boot.message.api.vo.SilentNotificationVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "次要初始数据")
@Getter
@Setter
public class SecondDataVO {

    @Schema(title = "[朋友圈]我不看他们的")
    private List<Long> momentIgnoredList;

    @Schema(title = "[朋友圈]我不让他们看")
    private List<Long> momentBlackedList;

    @Schema(title = "订阅的表情包列表")
    private List<EmoticonVO> emoticonList;

    @Schema(title = "免打扰配置")
    private List<SilentNotificationVO> silentNotificationList;

}
