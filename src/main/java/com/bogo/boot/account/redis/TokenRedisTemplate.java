
package com.bogo.boot.account.redis;

import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class TokenRedisTemplate extends StringRedisTemplate {

	private static final String TOKEN_CACHE_PREFIX = "APP_%d_TOKEN_%s";

	private static final String UID_CACHE_PREFIX = "UID_%d_TOKEN_%d";

	public TokenRedisTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void save(int terminalId,String token, Long uid,long expireAt) {

		String key = String.format(TOKEN_CACHE_PREFIX,terminalId,token);

		BoundValueOperations<String,String> operations = super.boundValueOps(key);
		operations.set(uid.toString());
		if (expireAt > 0){
			operations.expire(expireAt, TimeUnit.MILLISECONDS);
		}
	}

	public Long get(int terminalId,String token) {

		String key = String.format(TOKEN_CACHE_PREFIX,terminalId,token);

		String value = super.boundValueOps(key).get();

		return value == null ? null : Long.parseLong(value);
	}

	public void remove(int terminalId,String token) {

		String key = String.format(TOKEN_CACHE_PREFIX,terminalId,token);

		super.delete(key);
	}

	public void save(int terminalId,Long uid,String token,long expireAt) {

		String key = String.format(UID_CACHE_PREFIX,terminalId,uid);

		BoundValueOperations<String,String> operations = super.boundValueOps(key);
		operations.set(token);
		if (expireAt > 0){
			operations.expire(expireAt, TimeUnit.MILLISECONDS);
		}
	}

	public String get(int terminalId,Long uid) {

		String key = String.format(UID_CACHE_PREFIX,terminalId,uid);

		return super.boundValueOps(key).get();
	}

	public void remove(int terminalId,Long uid) {

		String key = String.format(UID_CACHE_PREFIX,terminalId,uid);

		super.delete(key);
	}
}
