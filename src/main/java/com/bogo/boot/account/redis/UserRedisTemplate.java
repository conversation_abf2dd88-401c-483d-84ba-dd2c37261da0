package com.bogo.boot.account.redis;

import com.bogo.boot.account.entity.User;
import com.bogo.boot.infra.util.JSON;
import java.nio.charset.StandardCharsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

@Component
public class UserRedisTemplate extends RedisTemplate<String, User> implements RedisSerializer<User> {

	private static final String CACHE_PREFIX = "USER_";

	@Autowired
	public UserRedisTemplate(RedisConnectionFactory connectionFactory) {
		StringRedisSerializer stringSerializer = new StringRedisSerializer();

		setKeySerializer(stringSerializer);
		setHashKeySerializer(stringSerializer);

		setValueSerializer(this);
		setHashValueSerializer(this);

		setConnectionFactory(connectionFactory);
		afterPropertiesSet();
	}

	public void save(User user) {
		String key = CACHE_PREFIX + user.getId();
		super.boundValueOps(key).set(user);
	}

	public User get(long uid) {
		String key = CACHE_PREFIX + uid;
		return super.boundValueOps(key).get();
	}

	public void remove(long uid) {
		String key = CACHE_PREFIX + uid;
		super.delete(key);
	}


	@Override
	public byte[] serialize(User user) throws SerializationException {
		return JSON.toJSONString(user).getBytes(StandardCharsets.UTF_8);
	}

	@Override
	public User deserialize(byte[] bytes) throws SerializationException {
		return JSON.parseNullable(bytes,User.class);
	}

}
