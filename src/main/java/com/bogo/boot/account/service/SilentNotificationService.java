
package com.bogo.boot.account.service;

import com.bogo.boot.account.entity.SilentNotification;
import java.util.List;

public interface SilentNotificationService {

	/**
	取消消息免打扰
	 */
	void cancel(long uid,Long targetId,byte type);

	/**
	设置消息免打扰
	 */
	void add(long uid,Long targetId,byte type);

	/**
	 * 是否设置了免打扰
	 */
	boolean isKeepQuiet(long uid,Long targetId,byte type);

	/**
	 * 获取关闭提醒的对象列表
	 * @param uid
	 */
	List<SilentNotification> findList(Long uid);

}
