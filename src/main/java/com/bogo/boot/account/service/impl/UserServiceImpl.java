
package com.bogo.boot.account.service.impl;

import com.bogo.boot.account.constant.UserState;
import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.redis.UserRedisTemplate;
import com.bogo.boot.account.repository.UserRepository;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.contact.event.FriendActionEvent;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.Password;
import com.bogo.boot.message.constant.MessageAction;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserServiceImpl implements UserService {
	@Resource
	private UserRepository userRepository;

	@Resource
	private UserRedisTemplate userRedisTemplate;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	private final LoadingCache<Long, Optional<String>> nameCache = CacheBuilder.newBuilder()
			.maximumSize(1000)
			.expireAfterAccess(10, TimeUnit.MINUTES)
			.build(CacheLoader.from(id -> Optional.ofNullable(userRepository.findName(id))));

	@Override
	public User findOne(Long id) {

		User user = userRedisTemplate.get(id);

		if (user != null) {
			return user;
		}

		User newUser = userRepository.findById(id).orElse(null);

		if (newUser != null){
			userRedisTemplate.save(newUser);
		}

		return newUser;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateName(Long id, String name) {

		userRepository.updateName(id,name);
		userRedisTemplate.remove(id);

		nameCache.invalidate(id);

		FriendActionEvent friendEvent = new FriendActionEvent();
		friendEvent.setSender(id);
		friendEvent.setContent(name);
		friendEvent.setAction(MessageAction.ACTION_103);
		applicationEventProducer.publish(friendEvent);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMotto(Long id, String motto) {
		userRepository.updateMotto(id,motto);
		userRedisTemplate.remove(id);

		FriendActionEvent friendEvent = new FriendActionEvent();
		friendEvent.setSender(id);
		friendEvent.setContent(motto);
		friendEvent.setAction(MessageAction.ACTION_104);

		applicationEventProducer.publish(friendEvent);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updatePassword(Long id, String password) {
		userRepository.updatePassword(id, Password.create(password));
		userRedisTemplate.remove(id);
	}


	@Override
	public boolean checkPassword(Long id, String password) {
		return Password.check(password, userRepository.findPassword(id));
	}


	@Override
	public User findOne(String telephone) {
		return userRepository.findOne(telephone);
	}

	@Override
	public Long findId(String telephone) {
		return userRepository.findId(telephone);
	}

	@Override
	public String findName(long id) {
		return nameCache.getUnchecked(id).orElse(null);
	}

	@Override
	public Map<Long, String> findName(List<Long> idList) {
		return userRepository.findList(idList).stream().collect(Collectors.toMap(User::getId, User::getName));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void save(User user) {

		user.setCreateTime(new Date());

		user.setPassword(Password.create(user.getPassword()));

		userRepository.saveAndFlush(user);

		userRedisTemplate.save(user);

	}

	@Override
	public boolean isExist(String telephone) {
		return userRepository.count(telephone) > 0;
	}

	@Override
	public List<User> findList(Collection<Long> idList) {
		return userRepository.findList(idList);
	}

	@Override
	public void destroy(long uid) {
		userRepository.updateState(uid, UserState.DESTROY.getValue());
	}

	@Override
	public void updateEmail(Long id, String email) {
		userRepository.updateEmail(id,email);
		userRedisTemplate.remove(id);

		FriendActionEvent friendEvent = new FriendActionEvent();
		friendEvent.setSender(id);
		friendEvent.setContent(email);
		friendEvent.setAction(MessageAction.ACTION_110);
		applicationEventProducer.publish(friendEvent);
	}

	@Override
	public void updateGender(Long id, byte gender) {

		userRepository.updateGender(id,gender);
		userRedisTemplate.remove(id);

		FriendActionEvent friendEvent = new FriendActionEvent();
		friendEvent.setSender(id);
		friendEvent.setContent(String.valueOf(gender));
		friendEvent.setAction(MessageAction.ACTION_111);
		applicationEventProducer.publish(friendEvent);
	}

}
