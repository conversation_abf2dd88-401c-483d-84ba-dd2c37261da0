
package com.bogo.boot.account.service.impl;

import com.bogo.boot.account.entity.SilentNotification;
import com.bogo.boot.account.repository.SilentNotificationRepository;
import com.bogo.boot.account.service.SilentNotificationService;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class SilentNotificationServiceImpl implements SilentNotificationService {
	@Resource
	private SilentNotificationRepository silentNotificationRepository;

	@Override
	public void add(long uid, Long targetId, byte type) {

		SilentNotification reminder = new SilentNotification();
		reminder.setUid(uid);
		reminder.setTargetId(targetId);
		reminder.setType(type);
		reminder.setCreateTime(new Date());

		silentNotificationRepository.delete(uid,targetId,type);

		silentNotificationRepository.saveAndFlush(reminder);

	}

	@Override
	public void cancel(long uid, Long targetId, byte type) {
		silentNotificationRepository.delete(uid,targetId,type);
	}

	@Override
	public boolean isKeepQuiet(long uid, Long targetId, byte type) {
		return silentNotificationRepository.count(uid,targetId,type) > 0;
	}

	@Override
	public List<SilentNotification> findList(Long uid) {
		return silentNotificationRepository.findList(uid);
	}
}
