
package com.bogo.boot.account.service.impl;

import com.bogo.boot.account.redis.TokenRedisTemplate;
import com.bogo.boot.account.service.AccessTokenService;
import com.bogo.boot.infra.model.Origin;
import jakarta.annotation.Resource;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class AccessTokenServiceImpl implements AccessTokenService {
	@Resource
	private TokenRedisTemplate tokenRedisTemplate;

	@Override
	public Long getUid(Origin origin,String token) {
		if (StringUtils.isBlank(token)){
			return null;
		}
		return tokenRedisTemplate.get(origin.getType(),token);
	}

	@Override
	public void delete(Origin origin,String token) {

		if (StringUtils.isBlank(token)){
			return;
		}

		Long uid = tokenRedisTemplate.get(origin.getType(),token);
		if (uid != null){
			tokenRedisTemplate.remove(origin.getType(),uid);
		}

		tokenRedisTemplate.remove(origin.getType(),token);
	}

	@Override
	public String generate(Origin origin,Long uid) {
		int terminalId = origin.getType();

		String oldToken =  tokenRedisTemplate.get(terminalId,uid);
		this.delete(origin,oldToken);

		String newToken = UUID.randomUUID().toString().replace("-","");
		tokenRedisTemplate.save(terminalId,newToken, uid,-1);
		tokenRedisTemplate.save(terminalId,uid, newToken,-1);
		return newToken;
	}

	@Override
	public void generate(Origin origin, Long uid, String token, long expireAt) {

		int terminalId = origin.getType();

		String oldToken =  tokenRedisTemplate.get(terminalId,uid);
		this.delete(origin,oldToken);

		tokenRedisTemplate.save(terminalId,token, uid,expireAt);
		tokenRedisTemplate.save(terminalId,uid, token,expireAt);
	}

}
