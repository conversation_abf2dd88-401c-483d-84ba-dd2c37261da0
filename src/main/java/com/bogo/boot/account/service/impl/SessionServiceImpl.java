
package com.bogo.boot.account.service.impl;

import com.bogo.boot.account.constant.SessionState;
import com.bogo.boot.account.entity.Session;
import com.bogo.boot.account.repository.SessionRepository;
import com.bogo.boot.account.service.SessionService;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.HostX;
import com.bogo.boot.message.event.APNsCloseEvent;
import com.bogo.boot.message.event.APNsOpenEvent;
import jakarta.annotation.Resource;
import java.util.Date;
import org.springframework.stereotype.Service;

@Service
public class SessionServiceImpl implements SessionService {

    @Resource
    private SessionRepository sessionRepository;

    @Resource
    private ApplicationEventProducer applicationEventProducer;

    private final String host = HostX.getLocalHost();

    @Override
    public void add(Session session) {

        session.setState(SessionState.ACTIVE);
        session.setHost(host);
        session.setCreateTime(new Date());

        /*
        删除当前类型客户端上的旧的记录
         */
        sessionRepository.delete(session.getUid(),session.getChannel());

        try {
            sessionRepository.saveAndFlush(session);
        }catch (Exception ignore){
            /*
             * 如多次调用，可能主键(uid_deviceId唯一)冲突，修改最新的记录
             */
            session.setId(sessionRepository.findId(session.getUid(),session.getDeviceId()));
            sessionRepository.saveAndFlush(session);
        }
    }

    @Override
    public void delete(Long id) {
        sessionRepository.delete(id);
    }

    @Override
    public void updateState(Long id, byte state) {
        sessionRepository.updateState(id,state);
    }

    @Override
    public void openApns(Long uid,String deviceToken) {
        applicationEventProducer.publish(new APNsOpenEvent(uid,deviceToken));
        sessionRepository.openApns(uid,Session.CHANNEL_IOS);
    }

    @Override
    public void closeApns(Long uid) {
        applicationEventProducer.publish(new APNsCloseEvent(uid));
        sessionRepository.closeApns(uid,Session.CHANNEL_IOS);
    }

    @Override
    public long count(Long uid) {
        return sessionRepository.count(uid);
    }

    @Override
    public void delete() {
        sessionRepository.delete(host);
    }
}
