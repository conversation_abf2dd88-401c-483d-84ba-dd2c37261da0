
package com.bogo.boot.account.service;

import com.bogo.boot.infra.model.Origin;

public interface AccessTokenService {


	/**
	 * 登录生成Token
	 * @param origin
	 * @param uid
	 * @return
	 */
	String generate(Origin origin,Long uid);


	/**
	 * 内部设置共享Token
	 * @param origin
	 * @param uid
	 * @param token
	 * @param expireAt
	 */
	void generate(Origin origin,Long uid,String token,long expireAt);

	/**
	 * 根据Token获取用户ID
	 * @param origin
	 * @param token
	 * @return
	 */
	Long getUid(Origin origin,String token);

	/**
	 * 清除Token
	 * @param origin
	 * @param value
	 */
	void delete(Origin origin,String value);


}
