
package com.bogo.boot.account.service;

import com.bogo.boot.account.entity.User;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface UserService {

	void updateName(Long id,String name);

	void updateMotto(Long id,String motto);

	void updatePassword(Long id,String password);

	boolean checkPassword(Long id,String password);

	User findOne(Long id);

	User findOne(String telephone);

	Long findId(String telephone);

	String findName(long id);

	Map<Long,String> findName(List<Long> idList);

	void save(User user);

	boolean isExist(String telephone);

	List<User> findList(Collection<Long> idList);

	void destroy(long uid);

	void updateEmail(Long id,String email);

	void updateGender(Long id,byte gender);

}
