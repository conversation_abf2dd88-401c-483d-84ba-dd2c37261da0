
package com.bogo.boot.account.entity;

import com.bogo.boot.account.constant.UserGender;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_user")
@Getter
@Setter
public class User{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "password", length = 64,nullable = false)
    private String password;

    @Column(name = "name", length = 16,nullable = false)
    private String name;

    @Column(name = "telephone", length = 15,nullable = false)
    private String telephone;

    @Column(name = "organization_id")
    private Long organizationId;

    @Column(name = "email", length = 50)
    private String email;

    /**
     * 性别
     * @see UserGender
     */
    @Column(name = "gender", length = 1)
    private byte gender = UserGender.MAN.getValue();

    @Column(name = "motto", length = 200)
    private String motto;

    /**
     * 状态
     * @see com.bogo.boot.account.constant.UserState
     */
    @Column(name = "state", length = 1)
    private Byte state;

    @Column(name = "create_time",nullable = false)
    private Date createTime;
}
