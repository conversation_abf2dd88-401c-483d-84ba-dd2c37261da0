
package com.bogo.boot.account.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_session")
@Getter
@Setter
public class Session {

    public static final String CHANNEL_IOS         = "ios";
    public static final String CHANNEL_ANDROID     = "android";
    public static final String CHANNEL_WINDOWS     = "windows";
    public static final String CHANNEL_MAC         = "mac";
    public static final String CHANNEL_WEB         = "web";
    public static final String CHANNEL_UNI_IOS     = "uni-ios";
    public static final String CHANNEL_UNI_ANDROID = "uni-android";
    public static final String CHANNEL_UNI_H5      = "uni-h5";


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * session绑定的用户账号
     */
    @Column(name = "uid")
    private Long uid;

    /**
     * session在本台服务器上的ID
     */
    @Column(name = "nid",length = 32,nullable = false)
    private String nid;

    /**
     * 客户端ID (设备号码+应用包名),ios为deviceToken
     */

    @Column(name = "device_id",length = 64,nullable = false)
    private String deviceId;

    /**
     * 终端设备型号
     */
    @Column(name = "device_name")
    private String deviceName;

    /**
     * 客户端语言
     */
    @Column(name = "language",length = 16)
    private String language;

    /**
     * session绑定的服务器IP
     */
    @Column(name = "host",length = 15,nullable = false)
    private String host;

    /**
     * 终端设备类型
     */
    @Column(name = "channel",length = 10,nullable = false)
    private String channel;

    /**
     * 终端应用版本
     */
    @Column(name = "app_version",length = 16)
    private String appVersion;

    /**
     * 终端系统版本
     */
    @Column(name = "os_version",length = 32)
    private String osVersion;

    /**
     * 状态
     * @see com.bogo.boot.account.constant.SessionState
     */
    @Column(name = "state")
    private Byte state;

    /**
     * 登录时间
     */
    @Column(name = "create_time")
    private Date createTime;
}
