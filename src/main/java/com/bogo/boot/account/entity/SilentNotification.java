
package com.bogo.boot.account.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 消息免提醒
 */
@Entity
@Table(name = "t_bogo_silent_notification")
@Getter
@Setter
public class SilentNotification {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;
	
	@Column(name = "uid",nullable = false)
	private Long uid;

	/**
	 * 被拉黑的好友用户ID
	 */
	@Column(name = "target_id",nullable = false)
	private Long targetId;

	/**
	 * 对象类型
	 * @see com.bogo.boot.account.constant.NotificationType
	 */
	@Column(name = "type", length = 1,nullable = false)
	private Byte type;

	@Column(name = "create_time", updatable = false)
	private Date createTime;
}
