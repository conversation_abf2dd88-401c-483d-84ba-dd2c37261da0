package com.bogo.boot.moment.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.model.CloudVideo;
import com.bogo.boot.moment.annotation.MomentVideoFormat;
import com.bogo.boot.moment.api.request.MomentRequest;
import com.bogo.boot.moment.constant.MomentType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MomentVideoValidator implements ConstraintValidator<MomentVideoFormat, MomentRequest> {

    @Override
    public boolean isValid(MomentRequest momentRequest, ConstraintValidatorContext context) {
        if (momentRequest.getType() != MomentType.VIDEO.getValue()) {
            return true;
        }
        CloudVideo video = JSON.parseNullable(momentRequest.getContent(), CloudVideo.class);

        return video != null
                && video.getVideo() != null
                && video.getImage() != null
                && video.getBucket() != null;
    }
}
