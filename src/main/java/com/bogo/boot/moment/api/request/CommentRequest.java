
package com.bogo.boot.moment.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.moment.entity.Comment;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
public class CommentRequest implements Serializable {

	@NotNull(message = "朋友圈文章ID不能为空",groups = CreateAction.class)
	@Schema(title = "朋友圈文章ID")
	private Long momentId;

	@Schema(title = "父评论ID")
	private Long parentId;

	@Schema(title = "内容")
	@Length(max = 320,message = "内容不能超过320个字符",groups = CreateAction.class)
	private String content;

	@NotNull(message = "类型不能为空",groups = CreateAction.class)
	@Schema(title = "类型 0:回复文章 1:回复评论 2:点赞")
	private Integer type;

	public Comment ofComment(){

		Comment comment = new Comment();
		comment.setContent(content);
		comment.setType(type.byteValue());
		comment.setMomentId(momentId);
		comment.setParentId(parentId);

		return comment;
	}
}
