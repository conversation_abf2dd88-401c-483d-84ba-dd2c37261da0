
package com.bogo.boot.moment.api;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.model.PaginationEntity;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.moment.api.request.MomentRequest;
import com.bogo.boot.moment.api.vo.MomentVO;
import com.bogo.boot.moment.entity.Moment;
import com.bogo.boot.moment.service.MomentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/moment")
@Tag(name = "朋友圈内容接口" )
public class MomentController {

	@Resource
	private MomentService momentService;

	@Operation( summary = "发布朋友圈")
	@PostMapping(value = "")
	public ResponseEntity<Long> publish(@Validated(value = CreateAction.class) @RequestBody MomentRequest request, @Parameter(hidden = true) @UID Long uid) {
		Moment moment = request.ofMoment();
		moment.setUid(uid);
		momentService.add(moment);
		return ResponseEntity.ok(moment.getId());
	}

	@Operation( summary = "滚动获取朋友圈内容列表")
	@Parameter(name = "minId", description = "前端缓存最小文章ID", in = ParameterIn.QUERY, example = "0")
	@GetMapping(value = "/timeline")
	public PaginationEntity<MomentVO> timeline(@RequestParam Long minId) {

		Pageable pageable = PageRequest.of(0, Common.API_PAGE_SIZE);

		Page<MomentVO> page = momentService.queryTimelinePage(minId, pageable);

		return PaginationEntity.make(page);
	}

	@Operation( summary = "滚到获取自己的内容列表")
	@Parameter(name = "minId", description = "前端当前列表最小文章ID", in = ParameterIn.QUERY, example = "0")
	@GetMapping(value = "/list")
	public PaginationEntity<MomentVO> list(@Parameter(hidden = true) @UID Long uid, @RequestParam Long minId) {

		Pageable pageable = PageRequest.of(0, Common.API_PAGE_SIZE);

		Page<MomentVO> page = momentService.querySelfPage(uid,minId, pageable);

		return PaginationEntity.make(page);
	}

	@Operation( summary = "滚到获取别人的内容列表")
	@Parameter(name = "minId", description = "前端当前列表最小文章ID", in = ParameterIn.QUERY, example = "0")
	@ApiResponses(value = {@ApiResponse(responseCode = "200" ,description = "OK"),@ApiResponse(responseCode = "403" ,description = "禁止访问")})
	@GetMapping(value = "/list/{makerId}")
	public ResponseEntity<?> list(@Parameter(hidden = true) @UID Long uid, @PathVariable Long makerId, @RequestParam Long minId) {
		Pageable pageable = PageRequest.of(0, Common.API_PAGE_SIZE);
		Page<MomentVO> page = this.momentService.queryOtherPage(uid,makerId,minId, pageable);
		return PaginationEntity.make(page);
	}

	@Operation( summary = "获取朋友圈最近5条图片，视频内容")
	@Parameter(name = "uid", description = "用户ID", in = ParameterIn.PATH,example = "0")
	@GetMapping(value = "/gallery/{makerId}")
	public ResponseEntity<List<MomentVO>> gallery(@PathVariable long makerId) {

		List<MomentVO> dataList = momentService.queryListGallery(makerId);

		return ResponseEntity.ok(dataList);
	}

	@Operation( summary = "删除朋友圈内容")
	@Parameter(name = "id", description = "朋友圈内容ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> delete(@PathVariable long id) {

		momentService.delete(id);

		return ResponseEntity.ok();
	}

	@Operation( summary = "获取朋友圈内容详情")
	@Parameter(name = "id", description = "朋友圈内容ID", in = ParameterIn.PATH,example = "0")
	@GetMapping(value = "/{id}")
	public ResponseEntity<MomentVO> get(@PathVariable long id, @Parameter(hidden = true) @UID Long uid) {
		return ResponseEntity.ok(momentService.findOne(id,uid));
	}

}
