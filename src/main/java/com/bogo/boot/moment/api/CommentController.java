
package com.bogo.boot.moment.api;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.moment.api.request.CommentRequest;
import com.bogo.boot.moment.api.vo.CommentVO;
import com.bogo.boot.moment.constant.CommentType;
import com.bogo.boot.moment.entity.Comment;
import com.bogo.boot.moment.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/comment")
@Tag(name = "朋友圈评论接口" )
public class CommentController {

	@Resource
	private CommentService commentService;

	@Operation( summary = "添加评论")
	@PostMapping(value = "")
	public ResponseEntity<Long> publish(@Parameter(hidden = true) @UID Long uid, @Validated(CreateAction.class) @RequestBody CommentRequest request) {

		Comment comment = request.ofComment();

		comment.setUid(uid);

		long id = commentService.add(comment);

		return ResponseEntity.ok(id);
	}

	@Operation( summary = "点赞")
	@Parameter(name = "momentId", description = "朋友圈内容ID", in = ParameterIn.PATH,example = "0",required = true)
	@PostMapping(value = "/praise/{momentId}")
	public ResponseEntity<Long> praise(@Parameter(hidden = true) @UID Long uid, @PathVariable long momentId) {

		Comment comment = new Comment();
		comment.setUid(uid);
		comment.setMomentId(momentId);
		comment.setType(CommentType.PRAISE.getValue());

		long id = commentService.add(comment);

		return ResponseEntity.ok(id);
	}



	@Operation( summary = "获取评论详情")
	@Parameter(name = "id", description = "评论ID", in = ParameterIn.PATH,example = "0",required = true)
	@GetMapping(value = "/{id}")
	public ResponseEntity<CommentVO> get(@PathVariable long id) {
		return ResponseEntity.ok(CommentVO.of(commentService.findOne(id)));
	}

	@Operation( summary = "删除评论/取消点赞")
	@Parameter(name = "id", description = "评论ID", in = ParameterIn.PATH,example = "0",required = true)
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> delete(@Parameter(hidden = true) @UID Long uid, @PathVariable long id) {

		commentService.delete(uid,id);

		return ResponseEntity.ok();
	}

}
