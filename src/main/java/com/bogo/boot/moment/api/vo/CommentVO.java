package com.bogo.boot.moment.api.vo;

import com.bogo.boot.moment.entity.Comment;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "评论内容")
@Getter
@Setter
public class CommentVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "朋友圈文章ID")
    private Long momentId;

    @Schema(title = "评论者UID")
    private Long uid;

    @Schema(title = "被回复的评论ID")
    private Long parentId;

    @Schema(title = "内容")
    private String content;

    @Schema(title = "类型 0:回复文章 1:回复评论 2:点赞")
    private Byte type;

    @Schema(title = "评论是否已经删除")
    private boolean deleted;

    @Schema(title = "创建时间")
    private Date createTime;

    public static CommentVO of(Comment comment) {
        if (comment == null){
            return null;
        }
        CommentVO vo = new CommentVO();
        vo.id = comment.getId();
        vo.uid = comment.getUid();
        vo.content = comment.getContent();
        vo.type = comment.getType();
        vo.createTime = comment.getCreateTime();
        vo.deleted = comment.isDeleted();
        vo.parentId = comment.getParentId();
        vo.momentId = comment.getMomentId();
        return vo;
    }

}