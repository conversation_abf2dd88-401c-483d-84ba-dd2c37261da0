
package com.bogo.boot.moment.api;

import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.moment.api.vo.MomentRuleBatchVO;
import com.bogo.boot.moment.api.vo.MomentRuleVO;
import com.bogo.boot.moment.entity.MomentRule;
import com.bogo.boot.moment.service.MomentRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/moment")
@Tag(name = "朋友圈权限接口" )
public class MomentRuleController {

	@Resource
	private MomentRuleService momentRuleService;

	@Operation( summary = "设置朋友圈权限")
	@Parameters({
			@Parameter(name = "targetId", description = "用户ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "type", description = "类型(1:我不看TA的 0:不给TA看我的)", in = ParameterIn.PATH,example = "0"),
	})
	@PostMapping(value = "/rule/{targetId}/{type}")
	public ResponseEntity<Void> save(@PathVariable Long targetId, @PathVariable byte type, @Parameter(hidden = true) @UID Long uid) {
		MomentRule rule = new MomentRule();
		rule.setTargetId(targetId);
		rule.setType(type);
		rule.setUid(uid);
		momentRuleService.save(rule);
		return ResponseEntity.ok();
	}

	@Operation( summary = "解除朋友圈权限")
	@Parameters({
			@Parameter(name = "targetId", description = "用户ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "type", description = "类型(1:我不看TA的 0:不给TA看我的)", in = ParameterIn.PATH,example = "0"),
	})
	@DeleteMapping(value = "/rule/{targetId}/{type}")
	public ResponseEntity<Void> delete(@PathVariable Long targetId, @PathVariable byte type, @Parameter(hidden = true) @UID Long uid) {
		MomentRule rule = new MomentRule();
		rule.setTargetId(targetId);
		rule.setType(type);
		rule.setUid(uid);
		momentRuleService.delete(rule);
		return ResponseEntity.ok();
	}

	@Operation( summary = "获取我限制的好友列表")
	@GetMapping(value = "/rule/list")
	public ResponseEntity<MomentRuleBatchVO> findIgnoredList(@Parameter(hidden = true) @UID Long uid) {
		MomentRuleBatchVO vo = new MomentRuleBatchVO();
		vo.setIgnoredList(momentRuleService.findMeIgnoreList(uid));
		vo.setBlackedList(momentRuleService.findMeBlockedList(uid));
		return ResponseEntity.ok(vo);
	}

	@Operation( summary = "获取好友的朋友圈权限")
	@GetMapping(value = "/rule/{targetId}")
	public ResponseEntity<MomentRuleVO> findOne(@Parameter(hidden = true) @UID Long uid, @PathVariable long targetId) {
		MomentRuleVO vo = new MomentRuleVO();
		vo.setBlacked(momentRuleService.isBlacked(uid,targetId));
		vo.setIgnored(momentRuleService.isIgnored(uid,targetId));
		return ResponseEntity.ok(vo);
	}
}
