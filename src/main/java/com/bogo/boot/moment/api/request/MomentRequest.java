
package com.bogo.boot.moment.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.moment.annotation.MomentImageFormat;
import com.bogo.boot.moment.annotation.MomentVideoFormat;
import com.bogo.boot.moment.constant.MomentVisible;
import com.bogo.boot.moment.entity.Moment;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@MomentVideoFormat(message = "视频参数不完整",groups = CreateAction.class)
@MomentImageFormat(message = "图片参数不完整",groups = CreateAction.class)
@Getter
@Setter
public class MomentRequest implements Serializable {

	@Length(max = 1000,message = "text不能超过1000个字符",groups = CreateAction.class)
	@Schema(title = "文字内容")
	private String text;

	@Length(max = 2000,message = "content不能超过2000个字符",groups = CreateAction.class)
	@Schema(title = "主内容")
	private String content;

	@NotNull(message = "type不能为空",groups = CreateAction.class)
	@Schema(title = "类型,0:单图 1:链接 2:视频 3:纯文字 4:多图")
	private Byte type;

	@Schema(title = "可见类型 0:公开 1:仅自己可见 2:部分可见 3:部分不可见")
	private Byte visibleType = MomentVisible.PUBLIC.getValue();

	@Length(max = 10000,message = "扩展信息不能超过10000个字符",groups = CreateAction.class)
	@Schema(title = "扩展信息")
	private String metadata;

	public Moment ofMoment(){

		Moment moment = new Moment();
		moment.setContent(content);
		moment.setType(type);
		moment.setVisibleType(visibleType);
		moment.setText(text);
		moment.setMetadata(metadata);

		return moment;
	}
}
