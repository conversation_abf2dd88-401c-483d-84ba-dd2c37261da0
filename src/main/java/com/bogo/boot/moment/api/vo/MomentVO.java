package com.bogo.boot.moment.api.vo;

import com.bogo.boot.infra.model.Sortable;
import com.bogo.boot.infra.util.PageX;
import com.bogo.boot.moment.entity.Comment;
import com.bogo.boot.moment.entity.Moment;
import com.bogo.boot.moment.predicate.CommentPredicate;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "朋友圈内容")
@Getter
@Setter
public class MomentVO implements Sortable {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "发布者UID")
    private Long uid;

    @Schema(title = "文字内容")
    private String text;

    @Schema(title = "可见类型")
    private Byte visibleType;

    @Schema(title = "主要内容，参见格式对应的json结构")
    private String content;

    @Schema(title = "类型,0:单图 1:链接 2:视频 3:纯文字 4:多图")
    private Byte type;

    @Schema(title = "扩展内容，存位置，设备信息等")
    private String metadata;

    @Schema(title = "发布时间戳")
    private Date createTime;

    @Schema(title = "评论列表")
    private List<CommentVO> commentList;


    public static MomentVO of(Moment moment, List<Comment> commentList) {
        if (moment == null) {
            return null;
        }
        MomentVO vo = new MomentVO();
        vo.uid = moment.getUid();
        vo.content = moment.getContent();
        vo.id = moment.getId();
        vo.metadata = moment.getMetadata();
        vo.type = moment.getType();
        vo.visibleType = moment.getVisibleType();
        vo.text = moment.getText();
        vo.createTime = moment.getCreateTime();
        vo.commentList = PageX.filter(new CommentPredicate(moment.getId()), commentList,CommentVO::of);
        return vo;
    }

    public static MomentVO of(Moment moment) {
        return of(moment, Collections.emptyList());
    }
}