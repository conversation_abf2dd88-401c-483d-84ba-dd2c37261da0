package com.bogo.boot.moment.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.model.CloudImage;
import com.bogo.boot.moment.annotation.MomentImageFormat;
import com.bogo.boot.moment.api.request.MomentRequest;
import com.bogo.boot.moment.constant.MomentType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class MomentImageValidator implements ConstraintValidator<MomentImageFormat, MomentRequest>, Predicate<CloudImage> {

    @Override
    public boolean isValid(MomentRequest request, ConstraintValidatorContext context) {
        if (request.getType() != MomentType.IMAGE.getValue() && request.getType() != MomentType.IMAGE_GRID.getValue()) {
            return true;
        }

        List<CloudImage> imageList = new ArrayList<>();

        if (request.getType() == MomentType.IMAGE.getValue()) {
            CollectionUtils.addIgnoreNull(imageList,JSON.parseNullable(request.getContent(),CloudImage.class));
        }
        if (request.getType() == MomentType.IMAGE_GRID.getValue()) {
            imageList.addAll(JSON.parseListNullable(request.getContent(),CloudImage.class));
        }

        return !imageList.isEmpty() && imageList.stream().noneMatch(this);
    }

    /**
     * 匹配不正确的数据格式
     * @param image the input argument
     * @return
     */
    @Override
    public boolean test(CloudImage image) {
        return StringUtils.isAnyEmpty(image.getImage(),image.getThumb(),image.getBucket())
                || image.getTh() == 0
                || image.getOh() == 0
                || image.getTw() == 0
                || image.getOw() == 0;
    }
}
