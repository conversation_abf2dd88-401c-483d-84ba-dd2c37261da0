package com.bogo.boot.moment.event;

import com.bogo.boot.infra.constant.ChangeType;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.moment.entity.Moment;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
public class MomentActionEvent extends ApplicationEvent {
    private EventMessage message = new EventMessage();
    private final ChangeType type;
    private List<Long> uidList;
    public MomentActionEvent(Moment moment, ChangeType type) {
        super(moment);
        this.type = type;
    }


    @Override
    public Moment getSource() {
        return (Moment) source;
    }

    public void setAction(String action) {
        message.setAction(action);
    }

    public void setContent(String content) {
        message.setContent(content);
    }

    public void setSender(Long sender) {
        message.setSender(sender);
    }
}
