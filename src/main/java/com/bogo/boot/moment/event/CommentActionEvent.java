package com.bogo.boot.moment.event;

import com.bogo.boot.infra.constant.ChangeType;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.moment.entity.Comment;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
public class CommentActionEvent extends ApplicationEvent {
    private final ChangeType type;
    private final Message message = new EventMessage();
    private List<Long> uidList;
    public CommentActionEvent(Comment comment, ChangeType type) {
        super(comment);
        this.type = type;
    }

    @Override
    public Comment getSource() {
        return (Comment) source;
    }

    public void setAction(String action) {
        message.setAction(action);
    }

    public void setContent(String content) {
        message.setContent(content);
    }

    public void setSender(Long sender) {
        message.setSender(sender);
    }

    public void setExtra(String extra) {
        message.setExtra(extra);
    }

    public void setTitle(String title) {
        message.setTitle(title);
    }

}
