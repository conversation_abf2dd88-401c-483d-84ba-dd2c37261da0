package com.bogo.boot.moment.model;

import com.bogo.boot.infra.util.JSON;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class MomentMetadata {

    private List<Long> visibleList;

    public boolean isVisible(long uid){
        return visibleList.contains(uid);
    }

    public static MomentMetadata of(String metadata){
        if (StringUtils.isBlank(metadata)){
            return new MomentMetadata();
        }
        return JSON.parse(metadata,MomentMetadata.class);
    }
}
