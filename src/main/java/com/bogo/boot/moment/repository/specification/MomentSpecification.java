
package com.bogo.boot.moment.repository.specification;


import com.bogo.boot.moment.entity.Moment;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.domain.Specification;

public class MomentSpecification implements Specification<Moment> {


    private final List<Long> idList;

    private final Long minId;

    private MomentSpecification(Long minId,List<Long> idList) {
        this.minId = minId;
        this.idList = idList;
    }


    @Override
    public Predicate toPredicate(@NotNull Root<Moment> root, @NotNull CriteriaQuery<?> query, @NotNull CriteriaBuilder builder) {

        List<Predicate> predicatesList = new ArrayList<>();

        if (minId != null && minId > 0){
            predicatesList.add(builder.lt(root.get("id").as(Long.class), minId));
        }

        predicatesList.add(root.get("uid").in(idList));

        query.where(predicatesList.toArray(new Predicate[0]));

        query.orderBy(builder.desc(root.get("createTime").as(Date.class)));

        return query.getRestriction();
    }


    public static MomentSpecification of(Long minId,List<Long> idList) {
        return new MomentSpecification(minId,idList);
    }


}
