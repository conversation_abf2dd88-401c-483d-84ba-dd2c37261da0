
package com.bogo.boot.moment.repository;

import com.bogo.boot.moment.entity.Comment;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface CommentRepository extends JpaRepository<Comment, Long> {

	@Modifying
	@Query("delete from Comment where momentId = ?1")
	void deleteAll(long momentId);

	@Modifying
	@Query("update Comment set deleted = true where id = ?1")
	void delete(long id);

	@Modifying
	@Query("delete from Comment where id = ?1")
	void remove(long id);

	@Query("from Comment where momentId = ?1  order by id")
	List<Comment> findList(long id);

	@Query("select count (*) from Comment where parentId = ?1 ")
	long countChild(long id);

	@Query("from Comment where momentId = ?1 and uid in(?2) order by id")
	List<Comment> findList(long id,List<Long> idList);

	@Query("from Comment where momentId in(?1) and uid in(?2) order by id")
	List<Comment> findList(List<Long> momentIdList,List<Long> uidList);

	@Query("from Comment where momentId  in(?1) order by id")
	List<Comment> findList(List<Long> momentIdList);

	@Query("from Comment where id = :id and uid = :uid")
	Comment findOne(long uid,long id);

	@Query("select max(id) from Comment where momentId = :momentId and uid = :uid and type = :type")
	Long findLastId(long momentId,long uid,byte type);
}
