
package com.bogo.boot.moment.repository;

import com.bogo.boot.moment.entity.Moment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MomentRepository extends JpaRepository<Moment, Long> {

	Page<Moment> findAll(Specification<Moment> sp, Pageable pageable);

}
