
package com.bogo.boot.moment.repository.specification;


import com.bogo.boot.moment.constant.MomentType;
import com.bogo.boot.moment.entity.Moment;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import org.springframework.data.jpa.domain.Specification;

public class GallerySpecification implements Specification<Moment> {

    private final long uid;

    private GallerySpecification(long uid) {
        this.uid = uid;
    }

    @Override
    public Predicate toPredicate(Root<Moment> root, CriteriaQuery<?> query, CriteriaBuilder builder) {

        List<Predicate> predicatesList = new ArrayList<>();

        predicatesList.add(builder.equal(root.get("uid").as(Long.class),uid));

        CriteriaBuilder.In<Byte> typeIn = builder.in(root.get("type").as(Byte.class));
        typeIn.value(MomentType.IMAGE.getValue());
        typeIn.value(MomentType.VIDEO.getValue());
        typeIn.value(MomentType.IMAGE_GRID.getValue());
        predicatesList.add(typeIn);

        query.where(predicatesList.toArray(new Predicate[0]));

        return query.getRestriction();
    }


    public static GallerySpecification of(long uid) {
        return new GallerySpecification(uid);
    }


}
