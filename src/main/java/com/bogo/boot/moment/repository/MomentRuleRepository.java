
package com.bogo.boot.moment.repository;

import com.bogo.boot.moment.constant.MomentRuleType;
import com.bogo.boot.moment.entity.MomentRule;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MomentRuleRepository extends JpaRepository<MomentRule, String> {

	@Query("select targetId from MomentRule where uid = ?1 and type = " + MomentRuleType.BLOCK)
	List<Long> findMeBlockedList(Long uid);

	@Query("select targetId from MomentRule where uid = ?1 and type = " + MomentRuleType.IGNORE)
	List<Long> findMeIgnoreList(Long uid);

	@Query("select uid from MomentRule where targetId = ?1 and type = " + MomentRuleType.BLOCK)
	List<Long> findBlockedMeList(Long uid);

	@Query("select uid from MomentRule where targetId = ?1 and type = " + MomentRuleType.IGNORE)
	List<Long> findIgnoreMeList(Long uid);

	@Query("from MomentRule where uid =?1")
	List<MomentRule> findList(Long uid);

	@Query("select count(*) from MomentRule where uid =:uid and targetId =:targetId and type = :type")
	long count(Long uid, Long targetId,byte type);

	@Modifying
	@Query("delete from MomentRule where uid =?1 and targetId=?2 and type = ?3")
	void delete(Long uid, Long targetId, byte type);
}
