package com.bogo.boot.moment.predicate;

import com.bogo.boot.moment.entity.Comment;
import java.util.Objects;
import java.util.function.Predicate;

public class CommentPredicate implements Predicate<Comment> {
    private final Long momentId;

    public CommentPredicate(Long momentId) {
        this.momentId = momentId;
    }

    @Override
    public boolean test(Comment comment) {
        return Objects.equals(comment.getMomentId(),momentId);
    }
}
