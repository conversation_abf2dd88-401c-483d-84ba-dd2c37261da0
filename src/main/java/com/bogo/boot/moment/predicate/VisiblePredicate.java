package com.bogo.boot.moment.predicate;

import com.bogo.boot.moment.constant.MomentVisible;
import com.bogo.boot.moment.entity.Moment;
import com.bogo.boot.moment.model.MomentMetadata;
import java.util.Objects;
import java.util.function.Predicate;

public class VisiblePredicate {

    public static class ForUid  implements Predicate<Long> {
        private final Moment moment;

        public ForUid(Moment moment) {
            this.moment = moment;
        }

        public static ForUid of(Moment moment){
            return new ForUid(moment);
        }

        @Override
        public boolean test(Long uid) {
            return VisiblePredicate.test(uid,moment);
        }
    }

    public static class ForMoment  implements Predicate<Moment>{
        private final long uid;

        public ForMoment(long uid) {
            this.uid = uid;
        }

        public static ForMoment of(long uid){
            return new ForMoment(uid);
        }

        @Override
        public boolean test(Moment moment) {
            return VisiblePredicate.test(uid,moment);
        }
    }

    private static boolean test(Long uid,Moment moment){

        /*
        自己可以看自己的
         */
        if (Objects.equals(uid, moment.getUid())){
            return true;
        }

         /*
         公开的
         */
        if (moment.getVisibleType() == MomentVisible.PUBLIC.getValue()) {
            return true;
        }

         /*
         仅自己可见的
         */
        if (moment.getVisibleType() == MomentVisible.PRIVATE.getValue()) {
            return Objects.equals(uid, moment.getUid());
        }

         /*
         部分可见的
         */
        if (moment.getVisibleType() == MomentVisible.INCLUDED.getValue()) {
            return MomentMetadata.of(moment.getMetadata()).isVisible(uid);
        }

         /*
         部分不可见的
         */
        if (moment.getVisibleType() == MomentVisible.EXCLUDED.getValue()) {
            return !MomentMetadata.of(moment.getMetadata()).isVisible(uid);
        }

        return false;
    }

}
