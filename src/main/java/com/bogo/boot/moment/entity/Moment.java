
package com.bogo.boot.moment.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 朋友圈文章实体
 */
@Entity
@Table(name = "t_bogo_moment")
@Getter
@Setter
public class Moment{

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "uid", nullable = false)
	private Long uid;

	@Column(name = "text", length = 1000)
	private String text;

	@Column(name = "content", length = 2000)
	private String content;

	/**
	 * 内容类型
	 *
	 * @see com.bogo.boot.moment.constant.MomentType
	 */
	@Column(name = "type",nullable = false)
	private Byte type;

	/**
	 * 可见性
	 * @see com.bogo.boot.moment.constant.MomentVisible
	 */
	@Column(name = "visible_type",nullable = false)
	private Byte visibleType;

	@Column(name = "metadata", length = 10000)
	private String metadata;

	@Column(name = "create_time",nullable = false)
	private Date createTime;

}
