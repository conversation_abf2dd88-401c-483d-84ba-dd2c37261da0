
package com.bogo.boot.moment.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 朋友圈权限设置
 */
@Entity
@Table(name = "t_bogo_moment_rule")
@Getter
@Setter
public class MomentRule {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;
	
	@Column(name = "uid",nullable = false)
	private Long uid;

	/**
	 * 被拉黑的好友用户ID
	 */
	@Column(name = "target_id",nullable = false)
	private Long targetId;

	/**
	 * 屏蔽类型
	 *
	 * @see com.bogo.boot.moment.constant.MomentRuleType
	 */
	@Column(name = "type", length = 1,nullable = false)
	private Byte type;

	@Column(name = "create_time")
	private Date createTime;
}
