
package com.bogo.boot.moment.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 评论实体
 */
@Entity
@Table(name = "t_bogo_moment_comment")
@Getter
@Setter
public class Comment {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;
	
	@Column(name = "moment_id",nullable = false)
	private Long momentId;
	
	@Column(name = "uid",nullable = false)
	private Long uid;
	
	@Column(name = "parent_id")
	private Long parentId;

	@Column(name = "content", length = 320)
	private String content;

	/**
	 * 类型
	 *
	 * @see com.bogo.boot.moment.constant.CommentType
	 */
	@Column(name = "type", length = 2,nullable = false)
	private Byte type;

	@Column(name = "is_deleted")
	private boolean deleted;

	@Column(name = "create_time", nullable = false)
	private Date createTime;

}
