
package com.bogo.boot.moment.redis;

import com.bogo.boot.infra.redis.LongRedisTemplate;
import java.util.List;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.stereotype.Component;

@Component
public class MomentListRedisTemplate extends LongRedisTemplate {

    private static final String KEY_ME_BLOCK_LIST = "MOMENT_RULE_ME_%d";
    private static final String KEY_ME_IGNORE_LIST = "MOMENT_RULE_MI_%d";
    private static final String KEY_BLOCK_ME_LIST = "MOMENT_RULE_BM_%d";
    private static final String KEY_IGNORE_ME_LIST = "MOMENT_RULE_IM_%d";

    public MomentListRedisTemplate(RedisConnectionFactory connectionFactory) {
        super(connectionFactory);
    }

    public void block(long uid, long fid, boolean add) {
        this.handle(String.format(KEY_ME_BLOCK_LIST, uid), fid, add);
        this.handle(String.format(KEY_BLOCK_ME_LIST, fid), uid, add);
    }

    public void ignore(long uid, long fid, boolean add) {
        this.handle(String.format(KEY_ME_IGNORE_LIST, uid), fid, add);
        this.handle(String.format(KEY_IGNORE_ME_LIST, fid), uid, add);
    }


    private void handle(String key, long fid, boolean add) {
        BoundSetOperations<String, Long> operations = super.boundSetOps(key);
        if (add) {
            operations.add(fid);
        } else {
            operations.remove(fid);
        }
    }

    public void addMeBlockList(long uid,List<Long> idList) {
        super.add(String.format(KEY_ME_BLOCK_LIST, uid),idList);
    }

    public void addMeIgnoreList(long uid,List<Long> idList) {
        super.add(String.format(KEY_ME_IGNORE_LIST, uid),idList);
    }

    public void addBlockMeList(long uid,List<Long> idList) {
        super.add(String.format(KEY_BLOCK_ME_LIST, uid),idList);
    }

    public void addIgnoreMeList(long uid,List<Long> idList) {
        super.add(String.format(KEY_IGNORE_ME_LIST, uid),idList);
    }

    public List<Long> findBlockMeList(long uid) {
        return findList(String.format(KEY_BLOCK_ME_LIST, uid));
    }

    public List<Long> findIgnoreMeList(long uid) {
        return findList(String.format(KEY_IGNORE_ME_LIST, uid));
    }

    public List<Long> findMeBlockList(long uid) {
        return findList(String.format(KEY_ME_BLOCK_LIST, uid));
    }

    public List<Long> findMeIgnoreList(long uid) {
        return findList(String.format(KEY_ME_IGNORE_LIST, uid));
    }

}
