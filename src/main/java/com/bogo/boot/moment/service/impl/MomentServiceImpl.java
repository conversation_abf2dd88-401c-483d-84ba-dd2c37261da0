
package com.bogo.boot.moment.service.impl;

import com.bogo.boot.contact.service.FriendService;
import com.bogo.boot.infra.constant.ChangeType;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.PageX;
import com.bogo.boot.message.constant.MessageActionGroup;
import com.bogo.boot.message.repository.MessageEventRepository;
import com.bogo.boot.moment.api.vo.CommentVO;
import com.bogo.boot.moment.api.vo.MomentVO;
import com.bogo.boot.moment.entity.Comment;
import com.bogo.boot.moment.entity.Moment;
import com.bogo.boot.moment.event.MomentActionEvent;
import com.bogo.boot.moment.predicate.CommentPredicate;
import com.bogo.boot.moment.predicate.VisiblePredicate;
import com.bogo.boot.moment.repository.CommentRepository;
import com.bogo.boot.moment.repository.MomentRepository;
import com.bogo.boot.moment.repository.specification.GallerySpecification;
import com.bogo.boot.moment.repository.specification.MomentSpecification;
import com.bogo.boot.moment.service.MomentRuleService;
import com.bogo.boot.moment.service.MomentService;
import com.bogo.boot.moment.service.MomentVisibleService;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
public class MomentServiceImpl implements MomentService {

	@Resource
	private MomentRepository momentRepository;

	@Resource
	private CommentRepository commentRepository;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private MomentVisibleService momentVisibleService;

	@Resource
	private FriendService friendService;

	@Resource
	private MessageEventRepository messageEventRepository;

	@Resource
	private MomentRuleService momentRuleService;


	@Override
	public void add(Moment moment) {

		moment.setCreateTime(new Date());

		this.momentRepository.saveAndFlush(moment);

		applicationEventProducer.publish(new MomentActionEvent(moment, ChangeType.ADD));
	}

	@Override
	public void delete(long id) {

		Moment moment = momentRepository.findById(id).orElse(null);
		if (moment == null) {
			return;
		}

		/*
		 用户接口请求验证只能删除自己的，
		 为空是admin请求过来的不用验证
		 */
		if (UidHolder.getUid() != null && !Objects.equals(moment.getUid(), UidHolder.getUid())) {
			return;
		}

		commentRepository.deleteAll(id);

		momentRepository.deleteById(id);

		messageEventRepository.deleteAll(id, MessageActionGroup.MOMENT_EVENT);

		applicationEventProducer.publish(new MomentActionEvent(moment, ChangeType.DELETE));
	}

	@Override
	public Page<MomentVO> querySelfPage(Long uid,Long minId, Pageable pageable) {

		Page<Moment> page = momentRepository.findAll(MomentSpecification.of(minId,Collections.singletonList(uid)), pageable);

		if (page.isEmpty()){
			return PageX.map(MomentVO::of,page);
		}

		List<Long> idList = page.getContent().stream().map(Moment::getId).collect(Collectors.toList());

		List<Comment> comments = commentRepository.findList(idList);

		return PageX.map(moment -> {
			MomentVO vo = MomentVO.of(moment);
			vo.setCommentList(PageX.filter(new CommentPredicate(moment.getId()), comments, CommentVO::of));
			return vo;
		},page);

	}

	@Override
	public Page<MomentVO> queryOtherPage(Long uid, Long makerId,Long minId, Pageable pageable) {

		/*
		 * 被限制访问朋友圈
		 */
		if (momentRuleService.isBlacked(makerId, uid)) {
			return new PageImpl<>(Collections.emptyList());
		}

		Page<Moment> page = momentRepository.findAll(MomentSpecification.of(minId,Collections.singletonList(makerId)), pageable);

		if (page.isEmpty()){
			return PageX.map(MomentVO::of,page);
		}

		boolean isFriend = friendService.isFriend(uid,makerId);

		/*
		 * 过滤掉我不可见的内容
		 */
		List<Moment> momentList = page.getContent().stream().filter(VisiblePredicate.ForMoment.of(uid)).collect(Collectors.toList());

		Page<MomentVO> voPage = PageX.map(MomentVO::of,momentList,page.getPageable(),page.getTotalElements());

		if (!isFriend){
			return voPage;
		}

		List<Long> uidList = momentVisibleService.findVisibleList(makerId,uid);
		uidList.add(uid);
		uidList.add(makerId);

		List<Long> momentIdList = voPage.getContent().stream().map(MomentVO::getId).collect(Collectors.toList());

		List<Comment> comments = commentRepository.findList(momentIdList,uidList);

		for (MomentVO moment : voPage.getContent()) {
			moment.setCommentList(PageX.filter(new CommentPredicate(moment.getId()),comments, CommentVO::of));
		}
		return voPage;
	}

	@Override
	public Page<MomentVO> queryTimelinePage(Long minId, Pageable pageable) {

		long uid = UidHolder.getUid();

		List<Long> idList = momentVisibleService.findVisibleList(uid);
		idList.add(uid);

		Page<Moment> page = momentRepository.findAll(MomentSpecification.of(minId,idList), pageable);

		/*
		 * 过滤掉我不可见的内容
		 */
		List<Moment> momentList = page.getContent().stream().filter(VisiblePredicate.ForMoment.of(uid)).collect(Collectors.toList());

		List<Long> momentIdList = momentList.stream().map(Moment::getId).collect(Collectors.toList());

		List<Comment> comments = commentRepository.findList(momentIdList,idList);

		return PageX.map(moment -> MomentVO.of(moment,comments),momentList,page.getPageable(),page.getTotalElements());
	}

	@Override
	public MomentVO findOne(long id , long uid) {

		Moment moment = momentRepository.findById(id).orElse(null);
		if (moment == null) {
			return null;
		}

		MomentVO vo = MomentVO.of(moment);

		if (vo.getUid() == uid){
			vo.setCommentList(PageX.map(CommentVO::of,commentRepository.findList(moment.getId())));
			return vo;
		}

		List<Long> idList = momentVisibleService.findVisibleList(uid);
		idList.add(uid);

		vo.setCommentList(PageX.map(CommentVO::of,commentRepository.findList(moment.getId(),idList)));
		return vo;
	}

	@Override
	public List<MomentVO> queryListGallery(Long uid) {

		/*
		 * 被限制访问朋友圈
		 */
		if (momentRuleService.isBlacked(UidHolder.getUid(), uid)) {
			return Collections.emptyList();
		}

		Pageable pageable = PageRequest.of(0, 5,Sort.by("id").descending());

		Page<Moment> page = momentRepository.findAll(GallerySpecification.of(uid), pageable);

		/*
		 * 过滤掉我不可见的内容
		 */
		return page.getContent().stream().filter(VisiblePredicate.ForMoment.of(uid)).map(MomentVO::of).collect(Collectors.toList());
	}

}
