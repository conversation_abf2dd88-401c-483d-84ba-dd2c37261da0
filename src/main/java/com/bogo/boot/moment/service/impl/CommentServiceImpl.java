
package com.bogo.boot.moment.service.impl;

import com.bogo.boot.infra.constant.ChangeType;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.moment.constant.CommentType;
import com.bogo.boot.moment.entity.Comment;
import com.bogo.boot.moment.event.CommentActionEvent;
import com.bogo.boot.moment.repository.CommentRepository;
import com.bogo.boot.moment.service.CommentService;
import jakarta.annotation.Resource;
import java.util.Date;
import org.springframework.stereotype.Service;

@Service
public class CommentServiceImpl implements CommentService {

	@Resource
	private CommentRepository commentRepository;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Override
	public long add(Comment comment) {

		/* 避免出现重复点赞  */
		if (comment.getType() == CommentType.PRAISE.getValue()) {
			Long maxId = commentRepository.findLastId(comment.getMomentId(),comment.getUid(),comment.getType());
			if (maxId != null){
				return maxId;
			}
		}

		comment.setCreateTime(new Date());

		this.commentRepository.saveAndFlush(comment);

		applicationEventProducer.publish(new CommentActionEvent(comment, ChangeType.ADD));

		return comment.getId();
	}

	@Override
	public Comment findOne(Long id) {
		return commentRepository.findById(id).orElse(null);
	}

	@Override
	public void delete(long uid,long id) {

		/*
		只能删除自己发的内容
		 */
		Comment comment = commentRepository.findOne(uid,id);

		if (comment == null){
			return;
		}

		/*
		 * 如果没有被引用回复，直接删除
		 * 否则逻辑删除
		 */
		if (this.commentRepository.countChild(id) == 0){
			commentRepository.remove(id);
		}else {
			commentRepository.delete(id);
		}

		applicationEventProducer.publish(new CommentActionEvent(comment, ChangeType.DELETE));
	}

}
