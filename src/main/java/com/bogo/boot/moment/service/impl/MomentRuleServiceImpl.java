
package com.bogo.boot.moment.service.impl;

import com.bogo.boot.moment.constant.MomentRuleType;
import com.bogo.boot.moment.entity.MomentRule;
import com.bogo.boot.moment.redis.MomentListRedisTemplate;
import com.bogo.boot.moment.repository.MomentRuleRepository;
import com.bogo.boot.moment.service.MomentRuleService;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
public class MomentRuleServiceImpl implements MomentRuleService {

	@Resource
	private MomentRuleRepository momentRuleRepository;

	@Resource
	private MomentListRedisTemplate momentListRedisTemplate;

	@Override
	public void delete(@RequestBody MomentRule rule) {
		momentRuleRepository.delete(rule.getUid(),rule.getTargetId(),rule.getType());
		if (rule.getType() == MomentRuleType.BLOCK) {
			momentListRedisTemplate.block(rule.getUid(),rule.getTargetId(),false);
		}

		if (rule.getType() == MomentRuleType.IGNORE) {
			momentListRedisTemplate.ignore(rule.getUid(),rule.getTargetId(),false);
		}
	}

	@Override
	public boolean isBlacked(Long uid, Long targetId) {
		return momentRuleRepository.count(uid, targetId, MomentRuleType.BLOCK) > 0;
	}

	@Override
	public boolean isIgnored(Long uid, Long targetId) {
		return momentRuleRepository.count(uid, targetId, MomentRuleType.IGNORE) > 0;
	}

	@Override
	public void save(@RequestBody MomentRule rule) {
		rule.setCreateTime(new Date());
		momentRuleRepository.saveAndFlush(rule);
		if (rule.getType() == MomentRuleType.BLOCK) {
			momentListRedisTemplate.block(rule.getUid(),rule.getTargetId(),true);
		}

		if (rule.getType() == MomentRuleType.IGNORE) {
			momentListRedisTemplate.ignore(rule.getUid(),rule.getTargetId(),true);
		}
	}

	@Override
	public List<Long> findNonNotifiableList(Long uid) {
		List<Long> idList = findMeBlockedList(uid);
		idList.addAll(findIgnoreMeList(uid));
		return idList;
	}

	@Override
	public List<Long> findInvisibleList(Long uid) {
		List<Long> idList = findBlockedMeList(uid);
		idList.addAll(findMeIgnoreList(uid));
		return idList;
	}

	@Override
	public List<Long> findMeBlockedList(Long uid) {
		List<Long> idList = momentListRedisTemplate.findMeBlockList(uid);
		if (idList.isEmpty()){
			idList.addAll(momentRuleRepository.findMeBlockedList(uid));
			momentListRedisTemplate.addMeBlockList(uid,idList);
		}
		return idList;
	}

	@Override
	public List<Long> findMeIgnoreList(Long uid) {
		List<Long> idList = momentListRedisTemplate.findMeIgnoreList(uid);
		if (idList.isEmpty()){
			idList.addAll(momentRuleRepository.findMeIgnoreList(uid));
			momentListRedisTemplate.addMeIgnoreList(uid,idList);
		}
		return idList;
	}

	@Override
	public List<Long> findBlockedMeList(Long uid) {
		List<Long> idList = momentListRedisTemplate.findBlockMeList(uid);
		if (idList.isEmpty()){
			idList.addAll(momentRuleRepository.findBlockedMeList(uid));
			momentListRedisTemplate.addBlockMeList(uid,idList);
		}
		return idList;
	}

	@Override
	public List<Long> findIgnoreMeList(Long uid) {
		List<Long> idList = momentListRedisTemplate.findIgnoreMeList(uid);
		if (idList.isEmpty()){
			idList.addAll(momentRuleRepository.findIgnoreMeList(uid));
			momentListRedisTemplate.addIgnoreMeList(uid,idList);
		}
		return idList;
	}

}
