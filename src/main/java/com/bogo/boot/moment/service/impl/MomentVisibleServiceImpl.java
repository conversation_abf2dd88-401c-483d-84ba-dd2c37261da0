
package com.bogo.boot.moment.service.impl;

import com.bogo.boot.contact.service.FriendService;
import com.bogo.boot.contact.service.OrganizationService;
import com.bogo.boot.moment.service.MomentRuleService;
import com.bogo.boot.moment.service.MomentVisibleService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class MomentVisibleServiceImpl implements MomentVisibleService {

	@Resource
	private MomentRuleService momentRuleService;

	@Resource
	private FriendService friendService;

	@Resource
	private OrganizationService organizationService;

	@Override
	public List<Long> findNotifiableList(Long uid) {

		List<Long> uidList = friendService.findUidList(uid);

		uidList.addAll(organizationService.findMateUidList(uid));

		uidList.removeAll(momentRuleService.findNonNotifiableList(uid));

		uidList.remove(uid);

		return uidList.stream().distinct().collect(Collectors.toList());
	}

	@Override
	public List<Long> findVisibleList(Long uid, long friendId) {
		List<Long> uidList = findNotifiableList(uid);

		if (uid == friendId){
			return uidList;
		}

		uidList.retainAll(findNotifiableList(friendId));

		return uidList.stream().distinct().collect(Collectors.toList());
	}

	@Override
	public List<Long> findVisibleList(Long uid) {

		List<Long> uidList = friendService.findUidList(uid);

		uidList.addAll(organizationService.findMateUidList(uid));

		List<Long> invisibleList = momentRuleService.findInvisibleList(uid);

		uidList.removeAll(invisibleList);

		return uidList.stream().distinct().collect(Collectors.toList());
	}
}
