
package com.bogo.boot.moment.service;

import java.util.List;

public interface MomentVisibleService {

    /**
     * 返回当前UID可通知的其他用户ID列表
	 * @param uid
     * @return
	 */
	List<Long> findNotifiableList(Long uid);

	/**
	 * 返回2个用户的共同可见的好友列表
	 * @param friendId
	 * @param uid
	 * @return
	 */
	List<Long> findVisibleList(Long uid,long friendId);

	/**
	 * 返回可查看的用户列表
	 * @param uid
	 * @return
	 */
	List<Long> findVisibleList(Long uid);
}
