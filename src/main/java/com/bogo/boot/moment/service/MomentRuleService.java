
package com.bogo.boot.moment.service;

import com.bogo.boot.moment.entity.MomentRule;
import java.util.List;

public interface MomentRuleService {

	void save(MomentRule model);

	void delete(MomentRule model);

	/**
	 * 查询我是否被对方限制访问TA的空间
	 * 
	 * @param uid
	 *            我的ID
	 * @param targetId
	 *            对方UD
	 * @return true 被限制的，false 没有被限制
	 */
	boolean isBlacked(Long uid, Long targetId);

	/**
	 * 查询我是不看TA的朋友圈
	 */
	boolean isIgnored(Long uid, Long targetId);


	/**
	 * 我不通知的用户列表
	 * 包含 我不让看的 和 不看我的
	 * @param uid
	 * @return
	 */
	List<Long> findNonNotifiableList(Long uid);


	/**
	 * 查询我看不到的用户ID列表
	 * 包含 我不看的 和 不让我看的
	 * @param uid
	 * @return
	 */
	List<Long> findInvisibleList(Long uid);

	/**
	 * 我不让看的用户ID列表
	 * @param uid
	 * @return
	 */
	List<Long> findMeBlockedList(Long uid);


	/**
	 * 查询我不看的用户ID列表
	 * @param uid
	 * @return
	 */
	List<Long> findMeIgnoreList(Long uid);


	/**
	 * 不让我看的用户ID列表
	 * @param uid
	 * @return
	 */
	List<Long> findBlockedMeList(Long uid);


	/**
	 * 不看我的用户ID列表
	 * @param uid
	 * @return
	 */
	List<Long> findIgnoreMeList(Long uid);

}
