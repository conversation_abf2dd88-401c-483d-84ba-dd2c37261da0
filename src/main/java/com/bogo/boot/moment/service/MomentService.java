
package com.bogo.boot.moment.service;

import com.bogo.boot.moment.api.vo.MomentVO;
import com.bogo.boot.moment.entity.Moment;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface MomentService {

	void add(Moment moment);

	void delete(long id);

	/**
	 * 查询空间时间线列表
	 * 
	 * @param minId
	 *            当前页面最小ID
	 * @param page
	 *            分页信息
	 * @return
	 */
	Page<MomentVO> queryTimelinePage(Long minId, Pageable page);

	/**
	 * 查询我的空间记录
	 * 
	 * @param uid
	 *            用户账户
	 * @param page
	 *            分页信息
	 * @return
	 */
	Page<MomentVO> querySelfPage(Long uid, Long minId,Pageable page);

	/**
	 * 查询某个用户的空间记录
	 *
	 * @param uid
	 *            当前用户账户
	 * @param makerId
	 *            查看的用户ID
	 * @param page
	 *            分页信息
	 * @return
	 */
	Page<MomentVO> queryOtherPage(Long uid, Long makerId,Long minId, Pageable page);

	MomentVO findOne(long id, long uid);

	/**
	 * 查询最近5个带图片的朋友圈
	 *
	 * @param uid
	 *
	 * @return
	 */
	List<MomentVO> queryListGallery(Long uid);
}
