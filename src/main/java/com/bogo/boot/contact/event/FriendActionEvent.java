package com.bogo.boot.contact.event;

import com.bogo.boot.message.entity.EventMessage;
import java.util.Collections;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * 给全好友推送的事件
 */
@Getter
@Setter
public class FriendActionEvent extends ApplicationEvent {

    private EventMessage message = new EventMessage();

    private List<Long> receivers;

    public FriendActionEvent() {
        super("");
    }

    public void setAction(String action) {
        message.setAction(action);
    }

    public void setContent(String content) {
        message.setContent(content);
    }

    public void setExtra(String extra) {
        message.setExtra(extra);
    }

    public void setSender(Long sender) {
        message.setSender(sender);
    }

    public void setReceiver(Long receiver) {
        message.setReceiver(receiver);
        receivers = Collections.singletonList(message.getReceiver());
    }

}
