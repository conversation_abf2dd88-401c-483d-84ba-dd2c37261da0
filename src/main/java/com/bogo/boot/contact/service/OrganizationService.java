
package com.bogo.boot.contact.service;

import com.bogo.boot.account.entity.User;
import com.bogo.boot.contact.entity.Department;
import com.bogo.boot.contact.entity.DepartmentMember;
import com.bogo.boot.contact.entity.Organization;
import java.util.List;


public interface OrganizationService {

    Organization findOne(long id);

    List<User> findList(long id);

    List<Department> findDepartments(long id);

    List<DepartmentMember> findMembers(long id);

    /**
     * 通过组织ID 查询成员用户ID列表
     * @param id
     * @return
     */
    List<Long> findUidList(long id);

    /**
     * 通过UID查同组织成员用户ID列表
     * @param uid
     * @return
     */
    List<Long> findMateUidList(long uid);

}
