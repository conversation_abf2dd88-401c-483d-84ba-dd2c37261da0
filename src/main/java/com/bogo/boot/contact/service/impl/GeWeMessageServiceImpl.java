package com.bogo.boot.contact.service.impl;

import com.bogo.boot.contact.entity.GeWeFriend;
import com.bogo.boot.contact.service.GeWeFriendSyncService;
import com.bogo.boot.contact.service.GeWeMessageService;
import com.bogo.boot.infra.service.GeWeApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * GeWe消息发送服务实现类
 */
@Service
@Slf4j
public class GeWeMessageServiceImpl implements GeWeMessageService {

    @Resource
    private GeWeApiService geWeApiService;

    @Resource
    private GeWeFriendSyncService geWeFriendSyncService;

    @Override
    public boolean sendTextToFriend(String wxId, String content) {
        if (!StringUtils.hasText(wxId) || !StringUtils.hasText(content)) {
            log.error("发送消息参数不能为空: wxId={}, content={}", wxId, content);
            return false;
        }

        // 验证好友是否存在
        GeWeFriend friend = geWeFriendSyncService.getFriendByWxId(wxId);
        if (friend == null) {
            log.error("好友不存在: {}", wxId);
            return false;
        }

        if (Boolean.TRUE.equals(friend.getIsGroup())) {
            log.error("微信ID {} 是群聊，请使用发送群聊消息接口", wxId);
            return false;
        }

        log.info("发送消息给好友: {} ({}), 内容: {}", friend.getNickName(), wxId, content);
        
        boolean success = geWeApiService.sendTextMessage(wxId, content, null);
        
        if (success) {
            log.info("消息发送成功");
        } else {
            log.error("消息发送失败");
        }
        
        return success;
    }

    @Override
    public boolean sendTextToGroup(String groupWxId, String content) {
        if (!StringUtils.hasText(groupWxId) || !StringUtils.hasText(content)) {
            log.error("发送群聊消息参数不能为空: groupWxId={}, content={}", groupWxId, content);
            return false;
        }

        // 验证群聊是否存在
        GeWeFriend group = geWeFriendSyncService.getFriendByWxId(groupWxId);
        if (group == null) {
            log.error("群聊不存在: {}", groupWxId);
            return false;
        }

        if (!Boolean.TRUE.equals(group.getIsGroup())) {
            log.error("微信ID {} 不是群聊，请使用发送好友消息接口", groupWxId);
            return false;
        }

        log.info("发送消息给群聊: {} ({}), 内容: {}", group.getNickName(), groupWxId, content);
        
        boolean success = geWeApiService.sendTextMessage(groupWxId, content, null);
        
        if (success) {
            log.info("群聊消息发送成功");
        } else {
            log.error("群聊消息发送失败");
        }
        
        return success;
    }

    @Override
    public boolean sendTextToGroupWithAt(String groupWxId, String content, String atWxId) {
        if (!StringUtils.hasText(atWxId)) {
            return sendTextToGroup(groupWxId, content);
        }

        return sendTextToGroupWithMultipleAt(groupWxId, content, atWxId);
    }

    @Override
    public boolean sendTextToGroupWithMultipleAt(String groupWxId, String content, String atWxIds) {
        if (!StringUtils.hasText(groupWxId) || !StringUtils.hasText(content)) {
            log.error("发送群聊@消息参数不能为空: groupWxId={}, content={}", groupWxId, content);
            return false;
        }

        // 验证群聊是否存在
        GeWeFriend group = geWeFriendSyncService.getFriendByWxId(groupWxId);
        if (group == null) {
            log.error("群聊不存在: {}", groupWxId);
            return false;
        }

        if (!Boolean.TRUE.equals(group.getIsGroup())) {
            log.error("微信ID {} 不是群聊，请使用发送好友消息接口", groupWxId);
            return false;
        }

        String finalContent = content;
        String finalAtWxIds = null;

        // 处理@功能
        if (StringUtils.hasText(atWxIds)) {
            finalAtWxIds = atWxIds;
            
            // 解析@的微信ID，构建@消息内容
            List<String> atWxIdList = Arrays.stream(atWxIds.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());

            if (!atWxIdList.isEmpty()) {
                StringBuilder atContent = new StringBuilder();
                
                for (String atWxId : atWxIdList) {
                    GeWeFriend atFriend = geWeFriendSyncService.getFriendByWxId(atWxId);
                    if (atFriend != null) {
                        String displayName = StringUtils.hasText(atFriend.getRemark()) 
                                ? atFriend.getRemark() 
                                : atFriend.getNickName();
                        atContent.append("@").append(displayName).append(" ");
                    } else {
                        log.warn("要@的好友不存在: {}", atWxId);
                        atContent.append("@").append(atWxId).append(" ");
                    }
                }
                
                finalContent = atContent.toString() + content;
            }
        }

        log.info("发送@消息给群聊: {} ({}), 内容: {}, @用户: {}", 
                group.getNickName(), groupWxId, finalContent, atWxIds);
        
        boolean success = geWeApiService.sendTextMessage(groupWxId, finalContent, finalAtWxIds);
        
        if (success) {
            log.info("群聊@消息发送成功");
        } else {
            log.error("群聊@消息发送失败");
        }
        
        return success;
    }
}