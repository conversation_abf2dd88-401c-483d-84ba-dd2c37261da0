
package com.bogo.boot.contact.service;

import com.bogo.boot.account.api.vo.UserVO;
import com.bogo.boot.contact.api.request.FriendAlisaRequest;
import com.bogo.boot.contact.entity.Friend;
import java.util.List;
import java.util.Map;


public interface FriendService {

    void add(Friend user);

    void setAlias(Long uid, FriendAlisaRequest request);

    List<UserVO> findList(Long uid);

    void delete(Long uid,Long fid);

    boolean isFriend(Long uid,Long fid);

    List<Long> findUidList(Long uid);

    Map<Long,String> findAliasMap(Long uid);
}
