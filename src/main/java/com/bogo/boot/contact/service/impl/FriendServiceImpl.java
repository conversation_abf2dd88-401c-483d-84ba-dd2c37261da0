
package com.bogo.boot.contact.service.impl;

import com.bogo.boot.account.api.vo.UserVO;
import com.bogo.boot.account.repository.UserRepository;
import com.bogo.boot.contact.api.request.FriendAlisaRequest;
import com.bogo.boot.contact.entity.Friend;
import com.bogo.boot.contact.event.FriendActionEvent;
import com.bogo.boot.contact.redis.FriendRedisTemplate;
import com.bogo.boot.contact.repository.FriendRepositoryProxy;
import com.bogo.boot.contact.service.FriendService;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.constant.MessageAction;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class FriendServiceImpl implements FriendService {

	@Resource
	private FriendRepositoryProxy friendRepositoryProxy;

	@Resource
	private UserRepository userRepository;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private FriendRedisTemplate friendRedisTemplate;

	@Override
	@Transactional
	public void add(Friend friend) {

		friendRepositoryProxy.remove(friend.getUid(),friend.getFid());
		friendRepositoryProxy.remove(friend.getFid(),friend.getUid());

		/*
		 将自己添加到对方好友列表
		 */
		Friend newFriend = new Friend();
		newFriend.setUid(friend.getFid());
		newFriend.setFid(friend.getUid());
		newFriend.setCreateTime(new Date());

		friendRepositoryProxy.add(friend);
		friendRepositoryProxy.add(newFriend);

		friendRedisTemplate.add(newFriend.getUid(),newFriend.getFid());
		friendRedisTemplate.add(friend.getUid(),friend.getFid());

		FriendActionEvent friendEvent = new FriendActionEvent();
		friendEvent.setSender(friend.getUid());
		friendEvent.setReceiver(friend.getFid());
		friendEvent.setAction(MessageAction.ACTION_106);
		applicationEventProducer.publish(friendEvent);
	}

	@Override
	public void setAlias(Long uid, FriendAlisaRequest request) {
		friendRepositoryProxy.updateAlias(uid,request.getUid(),request.getAlias());
	}


	@Override
	public List<UserVO> findList(Long uid) {
		Map<Long,String> aliasMap = findAliasMap(uid);
		return userRepository.findList(aliasMap.keySet()).stream().map(user -> UserVO.of(user,aliasMap.get(user.getId()))).collect(Collectors.toList());
	}

	@Override
	@Transactional
	public void delete(Long uid,Long fid) {

		friendRepositoryProxy.remove(fid,uid);
		friendRepositoryProxy.remove(uid,fid);

		friendRedisTemplate.delete(fid,uid);
		friendRedisTemplate.delete(uid,fid);

		FriendActionEvent friendEvent = new FriendActionEvent();
		friendEvent.setSender(uid);
		friendEvent.setReceiver(fid);
		friendEvent.setAction(MessageAction.ACTION_107);
		applicationEventProducer.publish(friendEvent);
	}


	@Override
	public boolean isFriend(Long uid, Long fid) {
		if (friendRedisTemplate.isFriend(uid,fid)){
			return true;
		}
		return friendRepositoryProxy.isFriend(uid,fid);
	}


	@Override
	public List<Long> findUidList(Long uid) {
		List<Long> fidList = friendRedisTemplate.findUidList(uid);
		if (fidList.isEmpty()){
			fidList.addAll(friendRepositoryProxy.findUidList(uid));
			friendRedisTemplate.add(uid,fidList);
		}
		return fidList;
	}

	@Override
	public Map<Long, String> findAliasMap(Long uid) {
		Map<Long,String> aliasMap = new HashMap<>();
		List<Friend> friends = friendRepositoryProxy.findList(uid);
		for (Friend friend : friends){
			aliasMap.put(friend.getFid(),friend.getAlias());
		}
		return aliasMap;
	}

}
