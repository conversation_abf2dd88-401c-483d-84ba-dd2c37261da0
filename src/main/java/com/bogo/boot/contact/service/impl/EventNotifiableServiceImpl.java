
package com.bogo.boot.contact.service.impl;

import com.bogo.boot.contact.service.EventNotifiableService;
import com.bogo.boot.contact.service.FriendService;
import com.bogo.boot.contact.service.OrganizationService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class EventNotifiableServiceImpl implements EventNotifiableService {

	@Resource
	private FriendService friendService;

	@Resource
	private OrganizationService organizationService;

	@Override
	public List<Long> findNotifiableList(Long uid) {

		List<Long> uidList = friendService.findUidList(uid);

		uidList.addAll(organizationService.findMateUidList(uid));

		uidList.remove(uid);

		return uidList.stream().distinct().collect(Collectors.toList());
	}

}
