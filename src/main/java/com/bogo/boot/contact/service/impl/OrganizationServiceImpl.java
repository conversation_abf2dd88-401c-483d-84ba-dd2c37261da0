
package com.bogo.boot.contact.service.impl;

import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.repository.UserRepository;
import com.bogo.boot.contact.entity.Department;
import com.bogo.boot.contact.entity.DepartmentMember;
import com.bogo.boot.contact.entity.Organization;
import com.bogo.boot.contact.redis.OrganizationRedisTemplate;
import com.bogo.boot.contact.repository.DepartmentMemberRepository;
import com.bogo.boot.contact.repository.DepartmentRepository;
import com.bogo.boot.contact.repository.OrganizationRepository;
import com.bogo.boot.contact.service.OrganizationService;
import jakarta.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class OrganizationServiceImpl implements OrganizationService {

	@Resource
	private OrganizationRepository organizationRepository;

	@Resource
	private UserRepository userRepository;

	@Resource
	private OrganizationRedisTemplate organizationRedisTemplate;

	@Resource
	private DepartmentRepository departmentRepository;

	@Resource
	private DepartmentMemberRepository departmentMemberRepository;

	@Override
	public Organization findOne(long id) {
		return organizationRepository.findById(id).orElse(null);
	}

	@Override
	public List<User> findList(long id) {
		return userRepository.findList(id);
	}

	@Override
	public List<Department> findDepartments(long id) {
		return departmentRepository.findList(id);
	}

	@Override
	public List<DepartmentMember> findMembers(long id) {
		return departmentMemberRepository.findList(id);
	}

	@Override
	public List<Long> findMateUidList(long uid) {
		Long organizationId = userRepository.getOrganizationId(uid);
		if (organizationId == null){
			return new LinkedList<>();
		}
		return findUidList(organizationId);
	}

	@Override
	public List<Long> findUidList(long id) {
		List<Long> cachedUidList = organizationRedisTemplate.findUidList(id);
		if (!CollectionUtils.isEmpty(cachedUidList)){
			return cachedUidList;
		}

		List<Long> uidList = userRepository.findIdList(id);
		if (!CollectionUtils.isEmpty(uidList)){
			organizationRedisTemplate.add(id,uidList);
		}

		return uidList;
	}
}
