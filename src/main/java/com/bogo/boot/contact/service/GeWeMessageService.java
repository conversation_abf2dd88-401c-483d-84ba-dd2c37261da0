package com.bogo.boot.contact.service;

/**
 * GeWe消息发送服务接口
 */
public interface GeWeMessageService {

    /**
     * 发送文字消息给好友
     * @param wxId 好友微信ID
     * @param content 消息内容
     * @return 是否发送成功
     */
    boolean sendTextToFriend(String wxId, String content);

    /**
     * 发送文字消息给群聊
     * @param groupWxId 群聊微信ID
     * @param content 消息内容
     * @return 是否发送成功
     */
    boolean sendTextToGroup(String groupWxId, String content);

    /**
     * 发送文字消息给群聊并@某人
     * @param groupWxId 群聊微信ID
     * @param content 消息内容
     * @param atWxId 要@的人的微信ID
     * @return 是否发送成功
     */
    boolean sendTextToGroupWithAt(String groupWxId, String content, String atWxId);

    /**
     * 发送文字消息给群聊并@多人
     * @param groupWxId 群聊微信ID
     * @param content 消息内容
     * @param atWxIds 要@的人的微信ID列表，用逗号分隔
     * @return 是否发送成功
     */
    boolean sendTextToGroupWithMultipleAt(String groupWxId, String content, String atWxIds);
}