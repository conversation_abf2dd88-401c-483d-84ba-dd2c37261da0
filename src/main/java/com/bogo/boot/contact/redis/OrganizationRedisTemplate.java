
package com.bogo.boot.contact.redis;

import com.bogo.boot.infra.redis.LongRedisTemplate;
import java.util.Collection;
import java.util.List;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;


@Component
public class OrganizationRedisTemplate extends LongRedisTemplate {

	private static final String KEY_MEMBERS = "ORG_MEMBER_%d";

	public OrganizationRedisTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void add(long id, Collection<Long> uidList){
		super.add(String.format(KEY_MEMBERS,id),uidList);
	}

	public List<Long> findUidList(long id){
		return super.findList(String.format(KEY_MEMBERS,id));
	}
}
