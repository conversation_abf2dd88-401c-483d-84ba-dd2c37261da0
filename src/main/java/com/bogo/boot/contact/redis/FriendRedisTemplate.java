
package com.bogo.boot.contact.redis;

import com.bogo.boot.infra.redis.LongRedisTemplate;
import java.util.List;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;


@Component
public class FriendRedisTemplate extends LongRedisTemplate {

	private static final String KEY_FRIEND_LIST = "FRIEND_LIST_%d";

	public FriendRedisTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void add(long uid, long friendId){
		String key = String.format(KEY_FRIEND_LIST,uid);
		if(super.size(key) == 0){
			return;
		}
		/* 缓存中有数据，添加好友才追加，否则可能缓存被删除，该操作不能追加，否则缓存和数据数据不一致 */

		super.add(key,friendId);
	}

	public void add(long uid, List<Long> idList){
		String key = String.format(KEY_FRIEND_LIST,uid);
		super.add(key,idList);
	}

	public void delete(long uid, long friendId){
		String key = String.format(KEY_FRIEND_LIST,uid);
		super.remove(key,friendId);
	}

	public boolean isFriend(long uid, long friendId){
		String key = String.format(KEY_FRIEND_LIST,uid);
		return Boolean.TRUE.equals(super.boundSetOps(key).isMember(friendId));
	}

	public List<Long> findUidList(long uid){
		String key = String.format(KEY_FRIEND_LIST,uid);
		return super.findList(key);
	}
}
