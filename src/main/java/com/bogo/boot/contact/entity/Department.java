
package com.bogo.boot.contact.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_department")
@Getter
@Setter
public class Department {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "organization_id")
    private Long organizationId;

    @Column(name = "leader_id")
    private Long leaderUid;

    @Column(name = "name", length = 16,nullable = false)
    private String name;

    @Column(name = "create_time",nullable = false)
    private Date createTime;

}
