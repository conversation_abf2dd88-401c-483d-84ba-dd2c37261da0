package com.bogo.boot.contact.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * GeWe微信好友信息实体
 */
@Entity
@Table(name = "gewe_friend")
@Getter
@Setter
public class GeWeFriend {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 微信ID
     */
    @Column(name = "wx_id", nullable = false, length = 100)
    private String wxId;

    /**
     * 昵称
     */
    @Column(name = "nick_name", length = 200)
    private String nickName;

    /**
     * 拼音首字母
     */
    @Column(name = "py_initial", length = 200)
    private String pyInitial;

    /**
     * 全拼
     */
    @Column(name = "quan_pin", length = 200)
    private String quanPin;

    /**
     * 性别 1-男 2-女
     */
    @Column(name = "sex")
    private Integer sex;

    /**
     * 备注
     */
    @Column(name = "remark", length = 200)
    private String remark;

    /**
     * 备注拼音首字母
     */
    @Column(name = "remark_py_initial", length = 200)
    private String remarkPyInitial;

    /**
     * 备注全拼
     */
    @Column(name = "remark_quan_pin", length = 200)
    private String remarkQuanPin;

    /**
     * 个性签名
     */
    @Column(name = "signature", length = 1000)
    private String signature;

    /**
     * 微信号
     */
    @Column(name = "alias", length = 100)
    private String alias;

    /**
     * 朋友圈背景图
     */
    @Column(name = "sns_bg_img", length = 500)
    private String snsBgImg;

    /**
     * 国家
     */
    @Column(name = "country", length = 100)
    private String country;

    /**
     * 省份
     */
    @Column(name = "province", length = 100)
    private String province;

    /**
     * 城市
     */
    @Column(name = "city", length = 100)
    private String city;

    /**
     * 大头像URL
     */
    @Column(name = "big_head_img_url", length = 500)
    private String bigHeadImgUrl;

    /**
     * 小头像URL
     */
    @Column(name = "small_head_img_url", length = 500)
    private String smallHeadImgUrl;

    /**
     * 是否为群聊
     */
    @Column(name = "is_group")
    private Boolean isGroup = false;

    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否已同步
     */
    @Column(name = "synced")
    private Boolean synced = true;
}