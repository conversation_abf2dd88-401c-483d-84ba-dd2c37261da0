
package com.bogo.boot.contact.entity;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 好友关系
 */
@MappedSuperclass
@Getter
@Setter
public class Friend{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "uid",nullable = false)
    private Long uid;

    @Column(name = "fid",nullable = false)
    private Long fid;

    @Column(name = "alias")
    private String alias;

    @Column(name = "create_time", updatable = false)
    private Date createTime;

}
