
package com.bogo.boot.contact.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_department_member")
@Getter
@Setter
public class DepartmentMember {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "organization_id",nullable = false)
    private Long organizationId;

    @Column(name = "department_id",nullable = false)
    private Long departmentId;

    @Column(name = "title",nullable = false)
    private String title;

    @Column(name = "uid",nullable = false)
    private Long uid;

    @Column(name = "create_time",nullable = false)
    private Date createTime;

}
