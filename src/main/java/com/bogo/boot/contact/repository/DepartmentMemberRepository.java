
package com.bogo.boot.contact.repository;

import com.bogo.boot.contact.entity.DepartmentMember;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface DepartmentMemberRepository extends JpaRepository<DepartmentMember, Long> {

    @Query("from DepartmentMember where organizationId = ?1  order by createTime")
    List<DepartmentMember> findList(long organizationId);
}
