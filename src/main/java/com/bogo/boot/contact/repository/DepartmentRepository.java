
package com.bogo.boot.contact.repository;

import com.bogo.boot.contact.entity.Department;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface DepartmentRepository extends JpaRepository<Department, Long> {
    @Query("from Department where organizationId = ?1  order by createTime")
    List<Department> findList(long organizationId);
}
