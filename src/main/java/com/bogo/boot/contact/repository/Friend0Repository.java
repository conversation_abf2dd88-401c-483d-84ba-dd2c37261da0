
package com.bogo.boot.contact.repository;

import com.bogo.boot.contact.entity.Friend;
import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface Friend0Repository extends FriendRepository {

	@Query("from Friend0 where uid = :uid")
	List<Friend> findList(Long uid);

	@Query("select fid from Friend0 where uid = ?1")
	List<Long> findUidList(Long uid);

	@Modifying
	@Query("delete from Friend0 where uid = ?1 and fid = ?2")
	void remove(Long uid ,Long fid);

	@Query("select count(*) > 0 from Friend0 where uid = ?1 and fid = ?2")
	boolean isFriend(Long uid ,Long fid);

	@Modifying
	@Query("update Friend0 set alias=:alias where uid = :uid and fid = :fid")
	void updateAlias(Long uid ,Long fid,String alias);
}
