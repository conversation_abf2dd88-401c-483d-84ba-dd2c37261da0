package com.bogo.boot.contact.repository;

import com.bogo.boot.contact.entity.GeWeFriend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * GeWe好友Repository
 */
@Repository
@Transactional(rollbackFor = Exception.class)
public interface GeWeFriendRepository extends JpaRepository<GeWeFriend, Long> {

    /**
     * 根据微信ID查找好友
     */
    Optional<GeWeFriend> findByWxId(String wxId);

    /**
     * 根据微信ID列表查找好友
     */
    List<GeWeFriend> findByWxIdIn(List<String> wxIds);

    /**
     * 查找所有好友（排除群聊）
     */
    @Query("SELECT g FROM GeWeFriend g WHERE g.isGroup = false ORDER BY g.remarkPyInitial, g.pyInitial")
    List<GeWeFriend> findAllFriends();

    /**
     * 查找所有群聊
     */
    @Query("SELECT g FROM GeWeFriend g WHERE g.isGroup = true ORDER BY g.remarkPyInitial, g.pyInitial")
    List<GeWeFriend> findAllGroups();

    /**
     * 根据昵称或备注模糊搜索
     */
    @Query("SELECT g FROM GeWeFriend g WHERE g.nickName LIKE %:keyword% OR g.remark LIKE %:keyword% ORDER BY g.remarkPyInitial, g.pyInitial")
    List<GeWeFriend> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 检查微信ID是否存在
     */
    boolean existsByWxId(String wxId);
}