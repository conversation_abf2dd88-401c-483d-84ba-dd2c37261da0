
package com.bogo.boot.contact.repository;

import com.bogo.boot.contact.entity.Friend;
import com.bogo.boot.contact.entity.Friend0;
import com.bogo.boot.contact.entity.Friend1;
import com.bogo.boot.contact.entity.Friend2;
import com.bogo.boot.contact.entity.Friend3;
import com.bogo.boot.contact.entity.Friend4;
import com.bogo.boot.contact.entity.Friend5;
import com.bogo.boot.contact.entity.Friend6;
import com.bogo.boot.contact.entity.Friend7;
import com.bogo.boot.contact.entity.Friend8;
import com.bogo.boot.contact.entity.Friend9;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消息队列 根据UID尾号分表实现
 */
@Component
public class FriendRepositoryProxy{

	private final Map<Integer, FriendRepository> repositoryMap = new HashMap<>();

	private final Map<Integer, Supplier<Friend>> friendIndexMapping = new HashMap<>();


	@Autowired
	public FriendRepositoryProxy(Friend0Repository friend0Repository,
                                 Friend1Repository friend1Repository,
                                 Friend2Repository friend2Repository,
                                 Friend3Repository friend3Repository,
                                 Friend4Repository friend4Repository,
                                 Friend5Repository friend5Repository,
                                 Friend6Repository friend6Repository,
                                 Friend7Repository friend7Repository,
                                 Friend8Repository friend8Repository,
                                 Friend9Repository friend9Repository) {

		repositoryMap.put(0, friend0Repository);
		repositoryMap.put(1, friend1Repository);
		repositoryMap.put(2, friend2Repository);
		repositoryMap.put(3, friend3Repository);
		repositoryMap.put(4, friend4Repository);
		repositoryMap.put(5, friend5Repository);
		repositoryMap.put(6, friend6Repository);
		repositoryMap.put(7, friend7Repository);
		repositoryMap.put(8, friend8Repository);
		repositoryMap.put(9, friend9Repository);


		friendIndexMapping.put(0, Friend0::new);
		friendIndexMapping.put(1, Friend1::new);
		friendIndexMapping.put(2, Friend2::new);
		friendIndexMapping.put(3, Friend3::new);
		friendIndexMapping.put(4, Friend4::new);
		friendIndexMapping.put(5, Friend5::new);
		friendIndexMapping.put(6, Friend6::new);
		friendIndexMapping.put(7, Friend7::new);
		friendIndexMapping.put(8, Friend8::new);
		friendIndexMapping.put(9, Friend9::new);
	}

	/**
	 * 获取UID尾号分表
	 * @param uid 原始UID
	 * @return 分表索引
	 */
	private int getIndexNumber(long uid) {
		return (int) (uid % 10);
	}

	public void add(Friend friend) {
		Friend addable = new TableMapping(friend.getUid()).apply(friend);
		getRepository(friend.getUid()).saveAndFlush(addable);
	}
	public List<Friend> findList(Long uid) {
		return getRepository(uid).findList(uid);
	}

	public List<Long> findUidList(Long uid) {
		return getRepository(uid).findUidList(uid);
	}

	public void remove(Long uid, Long fid) {
		getRepository(uid).remove(uid, fid);
	}

	public boolean isFriend(Long uid, Long fid) {
		return getRepository(uid).isFriend(uid, fid);
	}

	public void updateAlias(Long uid, Long fid, String alias) {
		getRepository(uid).updateAlias(uid, fid, alias);
	}

	private FriendRepository getRepository(Long uid) {
		return repositoryMap.get(getIndexNumber(uid));
	}

	private class TableMapping implements Function<Friend,Friend> {

		private final Supplier<Friend> supplier ;

		private TableMapping(long uid) {
			int index = getIndexNumber(uid);
			this.supplier = friendIndexMapping.get(index);
		}

		@Override
		public Friend apply(Friend friend) {
			Friend copy = supplier.get();
			copy.setUid(friend.getUid());
			copy.setFid(friend.getFid());
			copy.setAlias(friend.getAlias());
			copy.setCreateTime(friend.getCreateTime());
			return copy;
		}
	}

}
