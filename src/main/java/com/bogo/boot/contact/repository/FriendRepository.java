
package com.bogo.boot.contact.repository;

import com.bogo.boot.contact.entity.Friend;
import java.util.Collections;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FriendRepository extends JpaRepository<Friend, Long> {

	default List<Friend> findList(Long uid){
		return Collections.emptyList();
	}

	default List<Long> findUidList(Long uid) {
		return Collections.emptyList();
	}

	default void remove(Long uid ,Long fid){}

	default boolean isFriend(Long uid ,Long fid){
		return false;
	}

	default void updateAlias(Long uid ,Long fid,String alias){}
}
