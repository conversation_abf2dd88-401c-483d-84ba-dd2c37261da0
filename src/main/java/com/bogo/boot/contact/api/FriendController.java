
package com.bogo.boot.contact.api;

import com.bogo.boot.account.api.vo.UserVO;
import com.bogo.boot.contact.api.request.FriendAcceptRequest;
import com.bogo.boot.contact.api.request.FriendAlisaRequest;
import com.bogo.boot.contact.entity.Friend;
import com.bogo.boot.contact.service.FriendService;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.annotation.UpdateAction;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.pusher.DefaultMessagePusher;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/friend")
@Tag(name = "好友相关接口")
public class FriendController {

	@Resource
	private FriendService friendService;

	@Resource
	private DefaultMessagePusher defaultMessagePusher;

	@Operation( summary = "获取好友列表")
	@GetMapping(value = "/list")
	public ResponseEntity<List<UserVO>> list(@Parameter(hidden = true) @UID Long uid) {
		return ResponseEntity.ok(friendService.findList(uid));
	}


	@Operation( summary = "同意添加好友")
	@PostMapping(value = "/accept")
	public ResponseEntity<Void> accept(@Parameter(hidden = true) @UID Long uid,@RequestBody FriendAcceptRequest request) {

		Friend friend = new Friend();
		friend.setUid(uid);
		friend.setFid(request.getUid());
		friend.setAlias(request.getAlias());
		friend.setCreateTime(new Date());

		friendService.add(friend);

		return ResponseEntity.ok();
	}

	@Operation( summary = "申请添加好友")
	@Parameter(name = "fid", description = "用户ID", in = ParameterIn.PATH,required = true, example = "0")
	@ApiResponses(value = {@ApiResponse(responseCode = "200" ,description = "OK"),@ApiResponse(responseCode = "423" ,description = "已经是好友")})
	@PostMapping(value = "/apply/{fid}")
	public ResponseEntity<Void> apply(@PathVariable Long fid,
									  @Parameter(hidden = true) @UID Long uid,
									  @RequestParam String message) {

		if (Objects.equals(fid,uid)){
			return ResponseEntity.make(HttpStatus.BAD_REQUEST,"不能添加自己为好友");
		}

		if (friendService.isFriend(uid,fid)){
			return ResponseEntity.make(HttpStatus.LOCKED,"已经是好友");
		}

		Message eventMessage = new EventMessage();
		eventMessage.setFormat(MessageFormat.TEXT);
		eventMessage.setReceiver(fid);
		eventMessage.setAction(MessageAction.ACTION_105);
		eventMessage.setSender(uid);
		eventMessage.setContent(message);

		defaultMessagePusher.push(eventMessage);

		return ResponseEntity.ok();
	}

	@Operation( summary = "删除好友")
	@Parameter(name = "fid", description = "用户ID", in = ParameterIn.PATH, required = true, example = "0")
	@DeleteMapping(value = "/{fid}")
	public ResponseEntity<Void> delete(@PathVariable Long fid, @Parameter(hidden = true) @UID Long uid) {
		friendService.delete(uid,fid);
		return ResponseEntity.ok();
	}

	@Operation( summary = "设置好友备注")
	@PostMapping(value = "/alias")
	public ResponseEntity<Void> setAlias(@Parameter(hidden = true) @UID Long uid,@Validated(UpdateAction.class) @RequestBody FriendAlisaRequest request) {
		friendService.setAlias(uid,request);
		return ResponseEntity.ok();
	}
}
