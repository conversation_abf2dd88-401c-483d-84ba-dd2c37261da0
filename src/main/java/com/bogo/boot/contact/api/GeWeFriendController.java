package com.bogo.boot.contact.api;

import com.bogo.boot.contact.api.request.SendMessageRequest;
import com.bogo.boot.contact.api.request.SyncFriendsRequest;
import com.bogo.boot.contact.api.vo.GeWeFriendVO;
import com.bogo.boot.contact.entity.GeWeFriend;
import com.bogo.boot.contact.service.GeWeFriendSyncService;
import com.bogo.boot.contact.service.GeWeMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * GeWe好友管理控制器
 */
@RestController
@RequestMapping("/gewe/friend")
@Tag(name = "GeWe好友管理接口")
@Slf4j
public class GeWeFriendController {

    @Resource
    private GeWeFriendSyncService geWeFriendSyncService;

    @Resource
    private GeWeMessageService geWeMessageService;

    @Operation(summary = "同步好友信息", description = "从GeWe API同步好友信息到本地数据库")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "同步成功"),
            @ApiResponse(responseCode = "500", description = "同步失败")
    })
    @PostMapping("/sync")
    public ResponseEntity<Map<String, Object>> syncFriends(@RequestBody(required = false) SyncFriendsRequest request) {
        try {
            int syncCount;
            
            if (request == null || CollectionUtils.isEmpty(request.getWxIds())) {
                // 同步所有好友
                syncCount = geWeFriendSyncService.syncAllFriends();
            } else {
                // 同步指定好友
                syncCount = geWeFriendSyncService.syncFriendsByWxIds(request.getWxIds());
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("syncCount", syncCount);
            result.put("message", "同步完成，共同步 " + syncCount + " 个好友");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("同步好友信息失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    @Operation(summary = "获取好友列表", description = "获取所有好友列表（不包含群聊）")
    @GetMapping("/list")
    public ResponseEntity<List<GeWeFriendVO>> getFriendList() {
        List<GeWeFriend> friends = geWeFriendSyncService.getAllFriends();
        List<GeWeFriendVO> friendVOs = friends.stream()
                .map(GeWeFriendVO::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(friendVOs);
    }

    @Operation(summary = "获取群聊列表", description = "获取所有群聊列表")
    @GetMapping("/groups")
    public ResponseEntity<List<GeWeFriendVO>> getGroupList() {
        List<GeWeFriend> groups = geWeFriendSyncService.getAllGroups();
        List<GeWeFriendVO> groupVOs = groups.stream()
                .map(GeWeFriendVO::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(groupVOs);
    }

    @Operation(summary = "搜索好友", description = "根据关键词搜索好友（昵称或备注）")
    @GetMapping("/search")
    public ResponseEntity<List<GeWeFriendVO>> searchFriends(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        List<GeWeFriend> friends = geWeFriendSyncService.searchFriends(keyword);
        List<GeWeFriendVO> friendVOs = friends.stream()
                .map(GeWeFriendVO::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(friendVOs);
    }

    @Operation(summary = "获取好友详情", description = "根据微信ID获取好友详细信息")
    @GetMapping("/{wxId}")
    public ResponseEntity<GeWeFriendVO> getFriendDetail(
            @Parameter(description = "微信ID") @PathVariable String wxId) {
        
        GeWeFriend friend = geWeFriendSyncService.getFriendByWxId(wxId);
        
        if (friend == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(GeWeFriendVO.from(friend));
    }

    @Operation(summary = "刷新好友信息", description = "从GeWe API重新获取指定好友的最新信息")
    @PostMapping("/{wxId}/refresh")
    public ResponseEntity<Map<String, Object>> refreshFriend(
            @Parameter(description = "微信ID") @PathVariable String wxId) {
        
        try {
            GeWeFriend friend = geWeFriendSyncService.refreshFriend(wxId);
            
            Map<String, Object> result = new HashMap<>();
            if (friend != null) {
                result.put("success", true);
                result.put("friend", GeWeFriendVO.from(friend));
                result.put("message", "刷新成功");
            } else {
                result.put("success", false);
                result.put("message", "刷新失败，无法获取好友信息");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("刷新好友信息失败: {}", wxId, e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "刷新失败：" + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    @Operation(summary = "发送消息", description = "向指定好友或群聊发送文字消息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "发送成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "好友不存在"),
            @ApiResponse(responseCode = "500", description = "发送失败")
    })
    @PostMapping("/message/send")
    public ResponseEntity<Map<String, Object>> sendMessage(@Valid @RequestBody SendMessageRequest request) {
        try {
            boolean success = false;
            
            if ("group".equals(request.getMessageType())) {
                // 发送群聊消息
                if (StringUtils.hasText(request.getAtWxIds())) {
                    success = geWeMessageService.sendTextToGroupWithMultipleAt(
                            request.getToWxId(), request.getContent(), request.getAtWxIds());
                } else {
                    success = geWeMessageService.sendTextToGroup(
                            request.getToWxId(), request.getContent());
                }
            } else {
                // 发送好友消息
                success = geWeMessageService.sendTextToFriend(
                        request.getToWxId(), request.getContent());
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "消息发送成功" : "消息发送失败");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("发送消息失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "发送失败：" + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }
}