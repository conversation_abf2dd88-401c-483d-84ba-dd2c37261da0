package com.bogo.boot.contact.api.vo;

import com.bogo.boot.contact.entity.Department;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "部门信息")
@Getter
@Setter
public class DepartmentVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "上级部门ID",description = "一级部门为空")
    private Long parentId;

    @Schema(title = "主管UID")
    private Long leaderUid;

    @Schema(title = "名称")
    private String name;

    public static DepartmentVO of(Department department){
        if (department == null){
            return null;
        }
        DepartmentVO vo = new DepartmentVO();
        vo.name = department.getName();
        vo.id = department.getId();
        vo.parentId = department.getParentId();
        vo.leaderUid = department.getLeaderUid();
        return vo;
    }
}
