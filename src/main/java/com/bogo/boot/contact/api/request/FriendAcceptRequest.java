
package com.bogo.boot.contact.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Schema
@Getter
@Setter
public class FriendAcceptRequest implements Serializable {

	@Schema(title = "备注名称")
	private String alias;

	@Schema(title = "好友UID")
	@NotNull(message = "好友UID不能为空",groups = CreateAction.class)
	private Long uid;

}
