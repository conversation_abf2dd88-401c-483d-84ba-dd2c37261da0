
package com.bogo.boot.contact.api.request;

import com.bogo.boot.infra.annotation.UpdateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
public class FriendAlisaRequest implements Serializable {

	@Schema(title = "备注名称")
	@Length(max = 16,message = "备注名称不能超过16个字符",groups = UpdateAction.class)
	private String alias;

	@Schema(title = "好友UID")
	@NotNull(message = "好友UID不能为空",groups = UpdateAction.class)
	private Long uid;

}
