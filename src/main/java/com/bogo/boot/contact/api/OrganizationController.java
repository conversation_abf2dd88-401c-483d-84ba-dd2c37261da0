
package com.bogo.boot.contact.api;

import com.bogo.boot.account.api.vo.UserVO;
import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.contact.api.vo.DepartmentMemberVO;
import com.bogo.boot.contact.api.vo.DepartmentVO;
import com.bogo.boot.contact.api.vo.OrganizationVO;
import com.bogo.boot.contact.service.FriendService;
import com.bogo.boot.contact.service.OrganizationService;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.stream.Collectors;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/organization")
@Tag(name = "组织相关接口")
public class OrganizationController {

	@Resource
	private OrganizationService organizationService;

	@Resource
	private UserService userService;

	@Resource
	private FriendService friendService;

	@GetMapping(value = "")
	@Operation( summary = "获取全量组织数据",description = "收到组织变更通知(400、407)后重新获取")
	public ResponseEntity<OrganizationVO> get(@Parameter(hidden = true) @UID Long uid) {

		User user = userService.findOne(uid);

		if (user.getOrganizationId() == null){
			return ResponseEntity.ok();
		}

		OrganizationVO organization = OrganizationVO.of(organizationService.findOne(user.getOrganizationId()));
		organization.setDepartmentList(organizationService.findDepartments(organization.getId()).stream().map(DepartmentVO::of).toList());
		organization.setMemberList(organizationService.findMembers(organization.getId()).stream().map(DepartmentMemberVO::of).toList());

		Collection<Long> friendUidSet = friendService.findUidList(user.getId());
		Collection<Long> memberUidSet = organization.getMemberList().stream().map(DepartmentMemberVO::getUid).collect(Collectors.toSet());

		memberUidSet.removeAll(friendUidSet);

		organization.setContactList(userService.findList(memberUidSet).stream().map(UserVO::of).toList());

		return ResponseEntity.ok(organization);
	}



}
