package com.bogo.boot.contact.api.vo;

import com.bogo.boot.contact.entity.DepartmentMember;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "部门成员信息")
@Getter
@Setter
public class DepartmentMemberVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "部门ID")
    private Long departmentId;

    @Schema(title = "用户UID")
    private Long uid;

    @Schema(title = "职位名称")
    private String title;

    public static DepartmentMemberVO of(DepartmentMember member){
        if (member == null){
            return null;
        }
        DepartmentMemberVO vo = new DepartmentMemberVO();
        vo.id = member.getId();
        vo.departmentId = member.getDepartmentId();
        vo.uid = member.getUid();
        vo.title = member.getTitle();
        return vo;
    }
}
