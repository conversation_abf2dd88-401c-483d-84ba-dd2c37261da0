package com.bogo.boot.contact.api.vo;

import com.bogo.boot.account.api.vo.UserVO;
import com.bogo.boot.contact.entity.Organization;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "组织信息")
@Getter
@Setter
public class OrganizationVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "名称")
    private String name;

    @Schema(title = "部门列表")
    private List<DepartmentVO> departmentList;

    @Schema(title = "部门成员列表")
    private List<DepartmentMemberVO> memberList;

    @Schema(title = "组织联系人列表(非好友的)",description = "登录后获取主要初始数据接口中不返回")
    private List<UserVO> contactList;

    public static OrganizationVO of(Organization organization){
        if (organization == null){
            return null;
        }
        OrganizationVO vo = new OrganizationVO();
        vo.name = organization.getName();
        vo.id = organization.getId();
        return vo;
    }
}
