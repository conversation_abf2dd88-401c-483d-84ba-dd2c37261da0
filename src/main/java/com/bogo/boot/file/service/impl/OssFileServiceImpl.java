
package com.bogo.boot.file.service.impl;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PolicyConditions;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.TagSet;
import com.bogo.boot.file.api.vo.UploadSignVO;
import com.bogo.boot.file.config.properties.FileOssProperties;
import com.bogo.boot.file.constant.FileBucket;
import com.bogo.boot.file.event.ImageUploadEvent;
import com.bogo.boot.file.model.OssFile;
import com.bogo.boot.file.service.FileStoreService;
import com.bogo.boot.infra.holder.EnvironmentHolder;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.JSON;
import io.netty.handler.codec.http.QueryStringEncoder;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiFunction;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpMethod;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件存储在阿里云OSS的策略下 文件管理实现
 */
public class OssFileServiceImpl implements FileStoreService{

    private static final Logger LOGGER = LoggerFactory.getLogger(OssFileServiceImpl.class);

    private static final String META_REF_COUNT = "X-REF-COUNT";

    private final FileOssProperties properties;

    private final OSSClient ossClient;

    @jakarta.annotation.Resource
    private ApplicationEventProducer applicationEventProducer;

    @jakarta.annotation.Resource
    private BiFunction<String,String,String> contentTypeParser;

    @jakarta.annotation.Resource
    private EnvironmentHolder environmentHolder;

    /**
     * 上传文件完成OSS回调接口地址
     * @see "com.bogo.boot.file.api.FileStoreController.onUploadCallback()"
     */
    private static final String CALLBACK_API_PATH = "/file/cloud/storage/callback";

    /**
     * OSS上传成功回调接口地址
     */
    private final String callbackUrl;


    public OssFileServiceImpl(FileOssProperties properties,String endpoint){
        this.properties = properties;
        this.callbackUrl = endpoint + CALLBACK_API_PATH;
        CredentialsProvider provider = new DefaultCredentialProvider(properties.getAccessId(),properties.getAccessKey());
        ossClient = new OSSClient(properties.getEndpoint(), provider, new ClientConfiguration());
    }

    @Override
    public void upload(MultipartFile file, String bucket, String key) {

        try {

            OssFile ossFile = OssFile.of(bucket,key);

            PutObjectRequest putRequest = new PutObjectRequest(properties.getBucket(), ossFile.getKey(), file.getInputStream());
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentTypeParser.apply(bucket,key));
            metadata.setContentDisposition("attachment;fileName=" + URLEncoder.encode(key, StandardCharsets.UTF_8));
            putRequest.setMetadata(metadata);

            ossClient.putObject(putRequest);

            applicationEventProducer.publish(new ImageUploadEvent(bucket,key));

        } catch(Exception e) {
            LOGGER.error("OSS文件上传失败,bucket:{} key:{}",bucket,key);
        }
    }

    @Override
    public Resource get(String bucket, String key) {

        OssFile ossFile = OssFile.of(bucket,key);

        boolean isDestFileExist = ossClient.doesObjectExist(properties.getBucket(),ossFile.getKey());

        if (isDestFileExist){
            return new UrlResource(getOssFileUrl(bucket,key));
        }

        String defaultFileName =  FileBucket.getDefaultFilename(bucket);
        if (defaultFileName == null){
            return null;
        }

        return new UrlResource(getOssFileUrl(bucket,defaultFileName));
    }

    @Override
    public void delete(String bucket, String key) {

        OssFile ossFile = OssFile.of(bucket,key);

        int count = getReferenceCount(ossFile);

        if (count == 0){
            ossClient.deleteObject(properties.getBucket(), ossFile.getKey());
            return;
        }

        this.addReferenceCount(ossFile,-1);
    }

    @Override
    public void addReferenceCount(String bucket, String key) {
         this.addReferenceCount(OssFile.of(bucket,key),1);
    }

    /**
     * 返回阿里云OSS 客户端直传 签名信息
     * <a href="https://help.aliyun.com/document_detail/31926.html?spm=a2c4g.112718.0.0.392b3967OJMVSY">OSS文档地址</a>
     * @param bucket 目录名
     * @param name 文件名
     * @return 签名信息
     */
    @Override
    public UploadSignVO getUploadSign(String bucket, String name) {
        Date expiration = new Date( System.currentTimeMillis() + 300 * 1000);
        PolicyConditions policyConditions = new PolicyConditions();
        policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, FileUtils.ONE_MB * 50);
        policyConditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, bucket + "/");

        String policy = ossClient.generatePostPolicy(expiration, policyConditions);

        String host = properties.getEndpoint().replace("://",("://" + properties.getBucket() + "."));

        UploadSignVO signVO = new UploadSignVO();
        signVO.addParameter("OSSAccessKeyId",properties.getAccessId());
        signVO.addParameter("policy",Base64.encodeBase64String(policy.getBytes(StandardCharsets.UTF_8)));
        signVO.addParameter("signature",ossClient.calculatePostSignature(policy));
        signVO.addParameter("key",bucket + "/" + name);
        signVO.setHost(host);
        signVO.setMethod(HttpMethod.POST.name());

        /*
         开发环境(dev)不返回callback信息，因为OSS服务器可能调用不同本地服务
         */
        if (environmentHolder.isPro()){
            Map<String,String> callbackMap = new HashMap<>();
            callbackMap.put("callbackUrl",callbackUrl);
            callbackMap.put("callbackBody","filename=${object}&size=${size}&mimeType=${mimeType}");
            signVO.addParameter("callback",Base64.encodeBase64String((JSON.toJSONString(callbackMap).getBytes())));
        }

        return signVO;
    }



    private int getReferenceCount(OssFile ossFile) {

        if (!ossClient.doesObjectExist(properties.getBucket(),ossFile.getKey())){
            return 0;
        }

        TagSet tagSet = ossClient.getObjectTagging(properties.getBucket(),ossFile.getKey());

        String value = tagSet.getTag(META_REF_COUNT);

        return NumberUtils.toInt(value,0);
    }

    private void addReferenceCount(OssFile ossFile,int add) {

        if (!ossClient.doesObjectExist(properties.getBucket(),ossFile.getKey())){
            return;
        }

        TagSet tagSet = ossClient.getObjectTagging(properties.getBucket(),ossFile.getKey());

        String value = tagSet.getTag(META_REF_COUNT);

        tagSet.setTag(META_REF_COUNT,String.valueOf(NumberUtils.toInt(value,0) + add));

        ossClient.setObjectTagging(properties.getBucket(),ossFile.getKey(),tagSet);
    }


    /**
     * 这里需要把properties.bucket 设置为公共读
     * @param bucket
     * @param fileName
     * @return
     */
    private URL getOssFileUrl(String bucket,String fileName){
        Date expiration = new Date(System.currentTimeMillis() + 60000);
        GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(properties.getBucket(),bucket + "/" + fileName);
        urlRequest.setExpiration(expiration);
        URL url =  ossClient.generatePresignedUrl(urlRequest);
        try {

            // url.getPath()去掉时间有效期参数，转换为永久有效URL
            ServletRequestAttributes requestAttributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
            if (requestAttributes == null){
                return new URL(url.getProtocol(),url.getHost(),url.getPath());
            }

            // WEB端强制刷新缓存 新增随机参数，让URL产生变化
            String random = requestAttributes.getRequest().getParameter("random");
            if (StringUtils.isBlank(random)){
                return new URL(url.getProtocol(),url.getHost(),url.getPath());
            }

            QueryStringEncoder queryEncoder = new QueryStringEncoder(url.getPath());
            queryEncoder.addParam("random",random);
            return new URL(url.getProtocol(),url.getHost(),queryEncoder.toString());

        } catch (MalformedURLException e) {
            throw new IllegalArgumentException(e);
        }
    }
}
