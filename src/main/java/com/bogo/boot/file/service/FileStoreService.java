
package com.bogo.boot.file.service;

import com.bogo.boot.file.api.vo.UploadSignVO;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

public interface FileStoreService {

	void upload(MultipartFile file, String bucket, String key);

	Resource get(String bucket, String key);

	void delete(String bucket, String key);

	/**
     * 添加文件引用计数，避免原内容被删除，转发分享后无法看到附件内容
	 */
	void addReferenceCount(String bucket, String key);

	UploadSignVO getUploadSign(String bucket, String name);

}
