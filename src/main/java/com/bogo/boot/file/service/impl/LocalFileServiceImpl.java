
package com.bogo.boot.file.service.impl;

import com.bogo.boot.file.api.vo.UploadSignVO;
import com.bogo.boot.file.config.properties.FileLocalProperties;
import com.bogo.boot.file.constant.FileBucket;
import com.bogo.boot.file.event.BucketCreatedEvent;
import com.bogo.boot.file.event.ImageUploadEvent;
import com.bogo.boot.file.service.FileStoreService;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import java.io.File;
import java.nio.charset.Charset;
import java.nio.file.FileSystems;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.context.ApplicationListener;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpMethod;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件存储在本地文件夹的策略下 文件管理实现
 */
public class LocalFileServiceImpl implements FileStoreService, ApplicationListener<ApplicationStartedEvent> {
	private static final Logger LOGGER = LoggerFactory.getLogger(LocalFileServiceImpl.class);
	private File jarFilePath;
	private static final String REF_COUNT_FILE = "%s.ref";

	/**
	 * 上传文件接口路径
	 */
	private static final String UPLOAD_API_PATH = "/file/%s/%s";

	private final FileLocalProperties properties;

	/**
	 * boot服务的访问地址
	 */
	private final String endpoint;

	private final String fileSeparator = FileSystems.getDefault().getSeparator();

	@jakarta.annotation.Resource
	private ApplicationEventProducer applicationEventProducer;

	public LocalFileServiceImpl(FileLocalProperties properties, String endpoint){
		this.properties = properties;
		this.endpoint = endpoint;
	}

	@Override
	public void delete(String bucket, String key) {
		if (StringUtils.isBlank(bucket)
				|| StringUtils.isBlank(key)) {
			return;
		}

		File targetFile = new File(getBaseBucketPath(), bucket + fileSeparator + key);

		if (getReferenceCount(targetFile) == 0){
			FileUtils.deleteQuietly(targetFile);
			return;
		}

		addReferenceCount(targetFile,-1);
	}

	@Override
	public void addReferenceCount(String bucket, String key){

		File targetFile = new File(getBaseBucketPath() , bucket  + fileSeparator + key);

		addReferenceCount(targetFile,1);
	}

	/**
	 * 适配三方存储获取签名 客户端直传
	 * @param bucket
	 * @param name
	 * @return
	 */
	@Override
	public UploadSignVO getUploadSign(String bucket, String name) {
		UploadSignVO signVO = new UploadSignVO();
		signVO.setHost(endpoint + String.format(UPLOAD_API_PATH,bucket,name));
		signVO.setMethod(HttpMethod.POST.name());
		return signVO;
	}


	@Override
	public void upload(MultipartFile file, String bucket, String key) {

		File desFile = new File(getBaseBucketPath(),bucket  + fileSeparator + key);
		FileUtils.deleteQuietly(desFile);
		try {
			file.transferTo(desFile);
			applicationEventProducer.publish(new ImageUploadEvent(bucket,key));
		} catch (Exception e) {
			LOGGER.error("upload local file error",e);
		}
	}

	@Override
	public Resource get(String bucket, String key) {

		File destFile = new File(getBucket(bucket), key);

		if (destFile.exists() && destFile.length() == 0L){
			FileUtils.deleteQuietly(destFile);
			return null;
		}

		if (destFile.exists() && destFile.length() > 0L){
			return new FileSystemResource(destFile);
		}

		String defaultFileName =  FileBucket.getDefaultFilename(bucket);
		if (defaultFileName == null){
			return null;
		}

		File defaultFile = new File(getBucket(bucket), defaultFileName);

		return defaultFile.exists() ? new FileSystemResource(defaultFile) : null;
	}

	public String getBaseBucketPath() {
		return new File(jarFilePath, Common.LOCAL_BUCKET).getAbsolutePath();
	}


	private int getReferenceCount(File file){

		File countFile = new File(file.getParent(),String.format(REF_COUNT_FILE,file.getName()));

		try {
			return !countFile.exists() ? 0 :  Integer.parseInt(FileUtils.readFileToString(countFile, Charset.defaultCharset()));
		}catch (Exception ignore){
			return 0;
		}
	}


	public void addReferenceCount(File file,int add){

		File countFile = new File(file.getParent(),String.format(REF_COUNT_FILE,file.getName()));
		if (!countFile.exists() && add < 0){
			return;
		}

		try {
			if (!countFile.exists()){
				countFile.createNewFile();
			}

			String text = FileUtils.readFileToString(countFile, Charset.defaultCharset());

			int count =  text == null ? 0 : Integer.parseInt(text);

			FileUtils.write(countFile,String.valueOf(count + add),Charset.defaultCharset());

		}catch (Exception ignore){}

	}

	private File getBucket(String bucket) {
		return  new File(getBaseBucketPath(),bucket);
	}

	@Override
	public void onApplicationEvent(@NotNull ApplicationStartedEvent event) {
		ApplicationHome home = new ApplicationHome(getClass());
		if (home.getSource() != null) {
			jarFilePath = home.getSource().getParentFile();
		}else {
			jarFilePath = home.getDir();
		}

		File bucketDir = new File(getBaseBucketPath());

		try {

			FileUtils.forceMkdir(bucketDir);

			for (String bucket : properties.getBuckets()) {
				FileUtils.forceMkdir(new File(bucketDir, bucket));
			}

			applicationEventProducer.publish(new BucketCreatedEvent(bucketDir));

		}catch (Exception ignore){}

	}
}
