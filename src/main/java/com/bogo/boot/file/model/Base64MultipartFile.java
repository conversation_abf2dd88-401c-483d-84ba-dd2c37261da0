package com.bogo.boot.file.model;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.multipart.MultipartFile;

public class Base64MultipartFile implements MultipartFile {

    private final byte[] content;
    private final String header;

    public Base64MultipartFile(byte[] content, String header) {
        this.content = content;
        this.header = header.split(";")[0];
    }

    @NotNull
    @Override
    public String getName() {
        return System.currentTimeMillis() + Math.random() + "." + header.split("/")[1];
    }

    @Override
    public String getOriginalFilename() {
        return getName();
    }

    @Override
    public String getContentType() {
        return header.split(":")[1];
    }

    @Override
    public boolean isEmpty() {
        return content == null || content.length == 0;
    }

    @Override
    public long getSize() {
        return content.length;
    }

    @NotNull
    @Override
    public byte[] getBytes() {
        return content;
    }

    @NotNull
    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(content);
    }

    @Override
    public void transferTo(@NotNull File file) throws IOException, IllegalStateException {
        IOUtils.write(content, Files.newOutputStream(file.toPath()));
    }

    public static MultipartFile of(String base64) {
        String[] array = base64.split(",");

        String header = array[0];
        String content = array[1];
        byte[] bytes =  Base64.decodeBase64(content);
        for(int i = 0; i < bytes.length; ++i) {
            if (bytes[i] < 0) {
                bytes[i] += 256;
            }
        }
        return new Base64MultipartFile(bytes,header);
    }
}
