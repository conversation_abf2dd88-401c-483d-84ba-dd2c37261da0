package com.bogo.boot.file.event;

import com.bogo.boot.file.constant.FileBucket;
import java.util.Objects;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.ApplicationEvent;

public class ImageUploadEvent extends ApplicationEvent {

    private final String bucket;
    private final String filename;

    public ImageUploadEvent(String bucket,String filename) {
        super("");
        this.bucket = bucket;
        this.filename = filename;
    }

    public long getId(){
        return NumberUtils.toLong(filename,0L);
    }

    public boolean isGroupLogo(){
        return Objects.equals(FileBucket.GROUP_LOGO.getValue(),bucket);
    }

    public boolean isRobotLogo(){
        return Objects.equals(FileBucket.ROBOT_LOGO.getValue(),bucket);
    }

    public boolean isUserLogo(){
        return Objects.equals(FileBucket.USER_LOGO.getValue(),bucket);
    }

    public boolean isAppLogo(){
        return Objects.equals(FileBucket.APP_LOGO.getValue(),bucket);
    }

    public boolean isMomentWallpaper(){
        return Objects.equals(FileBucket.MOMENT_WALLPAPER.getValue(),bucket);
    }

    public boolean isUserWallpaper(){
        return Objects.equals(FileBucket.USER_BANNER.getValue(),bucket);
    }

}
