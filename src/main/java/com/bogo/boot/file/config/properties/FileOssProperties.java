package com.bogo.boot.file.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;


@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "bogo.file.oss")
public class FileOssProperties {

    private String endpoint;
    private String accessId;
    private String accessKey;
    private String bucket;

    private boolean enable;


}
