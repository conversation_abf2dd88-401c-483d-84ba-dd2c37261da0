
package com.bogo.boot.file.config;

import com.bogo.boot.file.config.properties.FileLocalProperties;
import com.bogo.boot.file.config.properties.FileOssProperties;
import com.bogo.boot.file.config.properties.FileUploadProperties;
import com.bogo.boot.file.service.FileStoreService;
import com.bogo.boot.file.service.impl.LocalFileServiceImpl;
import com.bogo.boot.file.service.impl.OssFileServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class FileConfiguration {

	@Bean("fileStoreService")
	@ConditionalOnProperty(
			name = {"bogo.file.local.enable"},
			matchIfMissing = true
	)
	@ConditionalOnMissingBean
	FileStoreService localFileStoreService(FileLocalProperties properties, FileUploadProperties uploadProperties) {
		return new LocalFileServiceImpl(properties,uploadProperties.getEndpoint());
	}

	@Bean("fileStoreService")
	@ConditionalOnProperty(
			name = {"bogo.file.oss.enable"},
			havingValue = "true"
	)
	FileStoreService ossFileStoreService(FileOssProperties properties, FileUploadProperties uploadProperties){
		return new OssFileServiceImpl(properties,uploadProperties.getEndpoint());
	}

}