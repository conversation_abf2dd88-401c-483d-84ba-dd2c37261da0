package com.bogo.boot.file.config.properties;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "bogo.file.local")
public class FileLocalProperties {

    private List<String> buckets;
    private boolean enable;


}
