
package com.bogo.boot.file.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.unit.DataSize;

@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "bogo.upload.file")
public class FileUploadProperties {

    /**
     * 最大可上传文件大小
     */
    private DataSize maxSize;

    /**
     * boot服务地址
     */
    private String endpoint;

}
