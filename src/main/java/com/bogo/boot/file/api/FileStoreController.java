
package com.bogo.boot.file.api;

import com.bogo.boot.file.api.vo.UploadSignVO;
import com.bogo.boot.file.event.ImageUploadEvent;
import com.bogo.boot.file.model.Base64MultipartFile;
import com.bogo.boot.file.service.FileStoreService;
import com.bogo.boot.infra.annotation.GreenFunction;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.function.BiFunction;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

@RestController
@RequestMapping("/file")
@Tag(name = "文件存储接口" )
public class FileStoreController  extends ResourceHttpRequestHandler {

	private static final Logger LOGGER = LoggerFactory.getLogger(FileStoreController.class);

	@Resource
	private FileStoreService fileStoreService;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private BiFunction<String,String,String> contentTypeParser;


	/**
	 * 本地存储模式下，上传文件接口
	 * @see "com.bogo.boot.file.service.impl.LocalFileServiceImpl.UPLOAD_API_PATH"
	 */
	@Operation( summary = "上传文件",hidden = true)
	@Parameters({
			@Parameter(name = "bucket", description = "文件库名", required = true,in = ParameterIn.PATH),
			@Parameter(name = "key", description = "文件KEY",required = true, in = ParameterIn.PATH),
			@Parameter(name = "file", description = "文件体",required = true, in = ParameterIn.DEFAULT)
	})
	@PostMapping(value = "/{bucket}/{key:.+}",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
	@GreenFunction
	public ResponseEntity<Void> upload(@RequestPart("file") MultipartFile file, @PathVariable String bucket, @PathVariable String key) {
		fileStoreService.upload(file, getNewBucketName(bucket), key);
		return ResponseEntity.ok();
	}


	@Operation( summary = "上传base64")
	@Parameters({
			@Parameter(name = "bucket", description = "文件库名", in = ParameterIn.PATH),
			@Parameter(name = "key", description = "文件KEY", in = ParameterIn.PATH)
	})
	@PostMapping(value = "/base64/{bucket}/{key:.+}")
	public ResponseEntity<Void> upload(@RequestBody String base64, @PathVariable String bucket, @PathVariable String key) {
		fileStoreService.upload(Base64MultipartFile.of(base64), getNewBucketName(bucket), key);
		return ResponseEntity.ok();
	}


	@Operation( summary = "删除文件")
	@Parameters({
			@Parameter(name = "bucket", description = "文件库名", in = ParameterIn.PATH),
			@Parameter(name = "key", description = "文件KEY", in = ParameterIn.PATH)
	})
	@DeleteMapping(value = "/{bucket}/{key:.+}")
	public ResponseEntity<Void> delete(@PathVariable String bucket, @PathVariable String key) {
		fileStoreService.delete(getNewBucketName(bucket), key);
		return ResponseEntity.ok();
	}

	/**
	 * 文件下载，支持断点续传
	 */

	@Operation( summary = "下载文件")
	@Parameters({
			@Parameter(name = "bucket", description = "文件库名", in = ParameterIn.PATH),
			@Parameter(name = "key", description = "文件KEY", in = ParameterIn.PATH),
			@Parameter(name = "name", description = "文件名", in = ParameterIn.QUERY)

	})
	@GreenFunction
	@GetMapping(value = "/{bucket}/{key:.+}")
	public void download(@PathVariable String bucket, @PathVariable String key,String name, HttpServletRequest request,
						 HttpServletResponse response) throws IOException, ServletException {

		String fileName = name == null ? key : name;

		org.springframework.core.io.Resource resource = fileStoreService.get(getNewBucketName(bucket), key);

		if (resource == null) {
			response.sendError(HttpStatus.NOT_FOUND.value());
			return;
		}

		response.setHeader(HttpHeaders.CONTENT_TYPE, contentTypeParser.apply(getNewBucketName(bucket),fileName));

		/*
		 * 来自OSS的文件地址，重定向到第三方文件地址
		 */
		if (resource instanceof UrlResource) {
			response.setStatus(HttpStatus.MOVED_PERMANENTLY.value());
			response.setHeader(HttpHeaders.LOCATION,resource.getURL().toString());
			return;
		}

		/*
		 * 来自本机文件，则使用spring mvc 自带的文件下载组件处理
		 */
		response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
						   "attachment;fileName=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

		response.setHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(resource.contentLength()));

		request.setAttribute("resource", resource);

		super.handleRequest(request, response);

	}

	@Override
	protected org.springframework.core.io.Resource getResource(HttpServletRequest request) {
		return (org.springframework.core.io.Resource) request.getAttribute("resource");
	}


	@Operation( summary = "获取客户端直传签名")
	@Parameters({
			@Parameter(name = "bucket", description = "文件库名", in = ParameterIn.QUERY),
			@Parameter(name = "name", description = "文件名称", in = ParameterIn.QUERY)
	})
	@GetMapping(value = "/upload/sign")
	public ResponseEntity<UploadSignVO> getUploadSign(@RequestParam String bucket, @RequestParam String name) {
		return ResponseEntity.ok(fileStoreService.getUploadSign(getNewBucketName(bucket),name));
	}



	/**
	 * OSS上传完成回调
	 * @see "com.bogo.boot.file.service.impl.OssFileServiceImpl.CALLBACK_API_PATH"
	 */
	@Hidden
	@Operation( summary = "OSS上传完成回调")
	@Parameters({
			@Parameter(name = "filename", description = "文件名", in = ParameterIn.QUERY),
			@Parameter(name = "mimeType", description = "文件类型", in = ParameterIn.QUERY)
	})
	@PostMapping(value = "/cloud/storage/callback")
	@GreenFunction
	public ResponseEntity<Void> onUploadCallback(@RequestParam String filename,@RequestParam long size,@RequestParam String mimeType) {

		String displaySize = FileUtils.byteCountToDisplaySize(size);

		LOGGER.info("OSS文件上传成功回调,filename:{} mimeType:{} size:{}",filename,mimeType, displaySize);

		if (mimeType.startsWith("image/")){
			String bucket = filename.split("/")[0];
			String name   = filename.split("/")[1];
			applicationEventProducer.publish(new ImageUploadEvent(bucket,name));
		}
		return ResponseEntity.ok();
	}

	private String getNewBucketName(String bucketName) {
		return bucketName.replace("hoxin-","");
	}
}
