package com.bogo.boot.file.api.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "客户端直传文件信息")
@Getter
@Setter
public class UploadSignVO {

    @Schema(title = "上传接口地址")
    private String host;

    @Schema(title = "请求方式(POST、PUT)")
    private String method;

    @Schema(title = "附带的form-data参数")
    private final Map<String,String> parameters = new HashMap<>();

    public void addParameter(String name, String value){
        parameters.put(name,value);
    }
}
