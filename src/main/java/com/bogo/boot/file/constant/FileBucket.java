
package com.bogo.boot.file.constant;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum FileBucket {
	CHAT_SPACE("chat-space"),

	MOMENT_SPACE("moment-space"),

	USER_LOGO("user-icon","default.png"),

	USER_BANNER("user-banner"),

	ROBOT_LOGO("robot-logo"),

	GROUP_LOGO("group-icon","default.png"),

	APP_LOGO("micro-server-icon"),

	MOMENT_WALLPAPER("moment-wallpaper"),

	MEETING("meeting"),

	NOTE("note");

	private final String value;

	private final String defaultFilename;


	FileBucket(String value) {
		this.value = value;
		this.defaultFilename = null;
	}

	FileBucket(String value,String defaultFilename) {
		this.value = value;
		this.defaultFilename = defaultFilename;
	}

    public static String getDefaultFilename(String bucketName){
		for (FileBucket bucket : values()){
			if (Objects.equals(bucket.value,bucketName)){
				return bucket.defaultFilename;
			}
		}
		return null;
	}

}
