package com.bogo.boot.file.function;

import com.bogo.boot.file.constant.FileBucket;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiFunction;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;


@Component
public class ContentTypeParser implements BiFunction<String,String,String> {

    private final Map<String,String> nameMimeTypeMap = new HashMap<>();
    private final Map<String,String> bucketMimeTypeMap = new HashMap<>();

    public ContentTypeParser(){
        nameMimeTypeMap.put("jpg" ,MediaType.IMAGE_JPEG_VALUE);
        nameMimeTypeMap.put("jpeg",MediaType.IMAGE_JPEG_VALUE);
        nameMimeTypeMap.put("png" ,MediaType.IMAGE_PNG_VALUE);
        nameMimeTypeMap.put("gif" ,MediaType.IMAGE_GIF_VALUE);
        nameMimeTypeMap.put("webp","image/webp");

        nameMimeTypeMap.put("txt" ,MediaType.TEXT_PLAIN_VALUE);
        nameMimeTypeMap.put("csv" ,MediaType.TEXT_PLAIN_VALUE);
        nameMimeTypeMap.put("log" ,MediaType.TEXT_PLAIN_VALUE);
        nameMimeTypeMap.put("ini" ,MediaType.TEXT_PLAIN_VALUE);
        nameMimeTypeMap.put("json",MediaType.TEXT_PLAIN_VALUE);
        nameMimeTypeMap.put("css" ,MediaType.TEXT_PLAIN_VALUE);
        nameMimeTypeMap.put("js"  ,MediaType.TEXT_PLAIN_VALUE);

        nameMimeTypeMap.put("html",MediaType.TEXT_HTML_VALUE);
        nameMimeTypeMap.put("htm" ,MediaType.TEXT_HTML_VALUE);
        nameMimeTypeMap.put("xml" ,MediaType.TEXT_XML_VALUE);

        nameMimeTypeMap.put("mp4" ,"video/mpeg4");
        nameMimeTypeMap.put("mp3" ,"audio/mp3");
        nameMimeTypeMap.put("aac" ,"audio/aac");
        nameMimeTypeMap.put("ogg" ,"audio/ogg");

        nameMimeTypeMap.put("pdf" ,MediaType.APPLICATION_PDF_VALUE);

        nameMimeTypeMap.put("doc" ,"application/msword");
        nameMimeTypeMap.put("docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        nameMimeTypeMap.put("ppt" ,"application/vnd.ms-powerpoint");
        nameMimeTypeMap.put("pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation");
        nameMimeTypeMap.put("xls" ,"application/vnd.ms-excel");
        nameMimeTypeMap.put("xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        nameMimeTypeMap.put("tar" ,"application/x-tar");
        nameMimeTypeMap.put("zip" ,"application/zip");
        nameMimeTypeMap.put("7z"  ,"application/x-7z-compressed");
        nameMimeTypeMap.put("rar" ,"application/x-rar-compressed");
        nameMimeTypeMap.put("apk" ,"application/vnd.android.package-archive");
        nameMimeTypeMap.put("ipa" ,"application/vnd.iphone");

        bucketMimeTypeMap.put(FileBucket.USER_LOGO.getValue(),MediaType.IMAGE_JPEG_VALUE);
        bucketMimeTypeMap.put(FileBucket.ROBOT_LOGO.getValue(),MediaType.IMAGE_JPEG_VALUE);
        bucketMimeTypeMap.put(FileBucket.APP_LOGO.getValue(),MediaType.IMAGE_JPEG_VALUE);
        bucketMimeTypeMap.put(FileBucket.GROUP_LOGO.getValue(),MediaType.IMAGE_JPEG_VALUE);

    }


    @Override
    public String apply(String bucket,String fileName) {
        if (bucketMimeTypeMap.containsKey(bucket)){
            return bucketMimeTypeMap.get(bucket);
        }
        String extension = StringUtils.toRootLowerCase(FilenameUtils.getExtension(fileName));
        return nameMimeTypeMap.getOrDefault(extension, MediaType.APPLICATION_OCTET_STREAM.toString());
    }

}
