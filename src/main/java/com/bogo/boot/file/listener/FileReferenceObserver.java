package com.bogo.boot.file.listener;

import com.bogo.boot.file.constant.FileBucket;
import com.bogo.boot.file.service.FileStoreService;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.event.ForwardMessageEvent;
import com.bogo.boot.message.model.ChatMap;
import com.bogo.boot.message.model.ChatVoice;
import com.bogo.boot.message.model.CloudFile;
import com.bogo.boot.message.model.CloudImage;
import com.bogo.boot.message.model.CloudVideo;
import com.bogo.boot.note.constant.NoteFormat;
import com.bogo.boot.note.entity.Note;
import com.bogo.boot.note.event.NoteCopyEvent;
import jakarta.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 文件引用计数器处理
 * 附件被转发，收藏等，需要增加引用计数器，避免源附件被删除
 */
@Component
public class FileReferenceObserver {

    @Resource
    private FileStoreService fileStoreService;

    /**
     * 转发消息，增加消息计数器，避免附件类消息被转发后，被测回或者删除 后看不见
     * @param event
     */
    @EventListener(classes = ForwardMessageEvent.class,condition = "#root.args[0].isFileMessage")
    public void onForwardMessageEvent(ForwardMessageEvent event){
        Message message = event.getMessage();
        if (MessageFormat.FILE == (message.getFormat())) {
            CloudFile cloudFile = JSON.parse(message.getContent(), CloudFile.class);
            fileStoreService.addReferenceCount(cloudFile.getBucket(), cloudFile.getFile());
        }
        if (MessageFormat.MAP == (message.getFormat())) {
            ChatMap chatMap = JSON.parse(message.getContent(), ChatMap.class);
            fileStoreService.addReferenceCount(FileBucket.CHAT_SPACE.getValue(),chatMap.getImage());
        }
        if (MessageFormat.VOICE == (message.getFormat())) {
            ChatVoice chatVoice = JSON.parse(message.getContent(), ChatVoice.class);
            fileStoreService.addReferenceCount(FileBucket.CHAT_SPACE.getValue(),chatVoice.getFile());
        }
        if (MessageFormat.IMAGE == (message.getFormat())) {
            CloudImage image = JSON.parse(message.getContent(), CloudImage.class);
            fileStoreService.addReferenceCount(image.getBucket(),image.getImage());
            fileStoreService.addReferenceCount(image.getBucket(),image.getThumb());
        }
        if (MessageFormat.VIDEO == (message.getFormat())) {
            CloudVideo video = JSON.parse(message.getContent(), CloudVideo.class);
            fileStoreService.addReferenceCount(video.getBucket(),video.getVideo());
            fileStoreService.addReferenceCount(video.getBucket(),video.getImage());
        }
    }

    @EventListener(classes = NoteCopyEvent.class)
    public void onNoteCopyEvent(NoteCopyEvent event){
        Note note = event.getSource();

        if (NoteFormat.FILE == (note.getFormat())) {
            CloudFile cloudFile = JSON.parse(note.getContent(), CloudFile.class);
            fileStoreService.addReferenceCount(cloudFile.getBucket(), cloudFile.getFile());
        }

        if (NoteFormat.IMAGE == (note.getFormat())) {
            CloudImage image = JSON.parse(note.getContent(), CloudImage.class);
            fileStoreService.addReferenceCount(image.getBucket(),image.getImage());
            fileStoreService.addReferenceCount(image.getBucket(),image.getThumb());
        }

        if (NoteFormat.VIDEO == (note.getFormat())) {
            CloudVideo video = JSON.parse(note.getContent(), CloudVideo.class);
            fileStoreService.addReferenceCount(video.getBucket(),video.getVideo());
            fileStoreService.addReferenceCount(video.getBucket(),video.getImage());
        }
    }

}
