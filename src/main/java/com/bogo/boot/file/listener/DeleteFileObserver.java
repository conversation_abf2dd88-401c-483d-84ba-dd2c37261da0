package com.bogo.boot.file.listener;

import com.bogo.boot.file.constant.FileBucket;
import com.bogo.boot.file.service.FileStoreService;
import com.bogo.boot.group.event.GroupDeleteEvent;
import com.bogo.boot.infra.constant.ChangeType;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.event.DeleteMessageEvent;
import com.bogo.boot.message.event.DeleteMessageListEvent;
import com.bogo.boot.message.model.ChatMap;
import com.bogo.boot.message.model.ChatVoice;
import com.bogo.boot.message.model.CloudFile;
import com.bogo.boot.message.model.CloudImage;
import com.bogo.boot.message.model.CloudVideo;
import com.bogo.boot.moment.constant.MomentType;
import com.bogo.boot.moment.entity.Moment;
import com.bogo.boot.moment.event.MomentActionEvent;
import com.bogo.boot.note.constant.NoteFormat;
import com.bogo.boot.note.entity.Note;
import com.bogo.boot.note.event.NoteDeleteEvent;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 消息删除，需要同步删除对应的附件
 */
@Component
public class DeleteFileObserver {

    @Resource
    private FileStoreService fileStoreService;

    @EventListener
    public void onDeleteMessageEvent(DeleteMessageEvent event){
        Message message = event.getSource();
        if (MessageFormat.FILE == (message.getFormat())) {
            CloudFile cloudFile = JSON.parse(message.getContent(), CloudFile.class);
            fileStoreService.delete(cloudFile.getBucket(), cloudFile.getFile());
        }
        if (MessageFormat.MAP == (message.getFormat())) {
            ChatMap chatMap = JSON.parse(message.getContent(), ChatMap.class);
            fileStoreService.delete(FileBucket.CHAT_SPACE.getValue(),chatMap.getImage());
        }
        if (MessageFormat.VOICE == (message.getFormat())) {
            ChatVoice chatVoice = JSON.parse(message.getContent(), ChatVoice.class);
            fileStoreService.delete(FileBucket.CHAT_SPACE.getValue(),chatVoice.getFile());
        }
        if (MessageFormat.IMAGE == (message.getFormat())) {
            CloudImage image = JSON.parse(message.getContent(), CloudImage.class);
            fileStoreService.delete(image.getBucket(),image.getImage());
            fileStoreService.delete(image.getBucket(),image.getThumb());
        }
        if (MessageFormat.VIDEO == (message.getFormat())) {
            CloudVideo video = JSON.parse(message.getContent(), CloudVideo.class);
            fileStoreService.delete(video.getBucket(),video.getVideo());
            fileStoreService.delete(video.getBucket(),video.getImage());
        }
    }

    @EventListener
    public void onDeleteMessageListEvent(DeleteMessageListEvent event){
        event.getMessages().forEach(message -> onDeleteMessageEvent(new DeleteMessageEvent(message)));
    }

    @EventListener
    public void onDeleteMomentEvent(MomentActionEvent event){

        if (event.getType() != ChangeType.DELETE){
            return;
        }

        Moment moment = event.getSource();

        if (Objects.equals(moment.getType(), MomentType.IMAGE.getValue())) {
            CloudImage image = JSON.parse(moment.getContent(), CloudImage.class);
            fileStoreService.delete(FileBucket.MOMENT_SPACE.getValue(),image.getImage());
            fileStoreService.delete(FileBucket.MOMENT_SPACE.getValue(),image.getThumb());
        }

        if (Objects.equals(moment.getType(), MomentType.IMAGE_GRID.getValue())) {

            List<CloudImage> snsImageList = JSON.parseList(moment.getContent(), CloudImage.class);
            snsImageList.forEach(image -> {
                fileStoreService.delete(FileBucket.MOMENT_SPACE.getValue(),image.getImage());
                fileStoreService.delete(FileBucket.MOMENT_SPACE.getValue(),image.getThumb());
            });
        }

        if (Objects.equals(moment.getType(), MomentType.VIDEO.getValue())) {
            CloudVideo video = JSON.parse(moment.getContent(), CloudVideo.class);
            fileStoreService.delete(FileBucket.MOMENT_SPACE.getValue(),video.getVideo());
            fileStoreService.delete(FileBucket.MOMENT_SPACE.getValue(),video.getImage());
        }
    }


    @EventListener(classes = NoteDeleteEvent.class)
    public void onNoteDeleteEvent(NoteDeleteEvent event){
        Note note = event.getSource();

        if (NoteFormat.FILE == (note.getFormat())) {
            CloudFile cloudFile = JSON.parse(note.getContent(), CloudFile.class);
            fileStoreService.delete(cloudFile.getBucket(), cloudFile.getFile());
        }

        if (NoteFormat.IMAGE == (note.getFormat())) {
            CloudImage image = JSON.parse(note.getContent(), CloudImage.class);
            fileStoreService.delete(image.getBucket(),image.getImage());
            fileStoreService.delete(image.getBucket(),image.getThumb());
        }

        if (NoteFormat.VIDEO == (note.getFormat())) {
            CloudVideo video = JSON.parse(note.getContent(), CloudVideo.class);
            fileStoreService.delete(video.getBucket(),video.getVideo());
            fileStoreService.delete(video.getBucket(),video.getImage());
        }
    }

    @EventListener(classes = GroupDeleteEvent.class)
    public void onGroupDeleteEvent(GroupDeleteEvent event){
        fileStoreService.delete(FileBucket.GROUP_LOGO.getValue(),String.valueOf(event.getId()));
    }

}
