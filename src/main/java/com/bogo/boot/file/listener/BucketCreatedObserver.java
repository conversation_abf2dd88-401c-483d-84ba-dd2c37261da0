package com.bogo.boot.file.listener;

import com.bogo.boot.file.constant.FileBucket;
import com.bogo.boot.file.event.BucketCreatedEvent;
import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.util.Objects;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class BucketCreatedObserver {

    @EventListener
    public void moveDefaultUserLogo(BucketCreatedEvent event) throws IOException {

        File defaultUserLogo = new File(event.getBaseBucketFile(),FileBucket.USER_LOGO.getValue() + FileSystems.getDefault().getSeparator() + FileBucket.USER_LOGO.getDefaultFilename());
        FileUtils.writeByteArrayToFile(defaultUserLogo, IOUtils.toByteArray(Objects.requireNonNull(getClass().getResourceAsStream("/logo/user/default.png"))));

        File defaultGroupLogo = new File(event.getBaseBucketFile(),FileBucket.GROUP_LOGO.getValue() + FileSystems.getDefault().getSeparator() + FileBucket.GROUP_LOGO.getDefaultFilename());
        FileUtils.writeByteArrayToFile(defaultGroupLogo, IOUtils.toByteArray(Objects.requireNonNull(getClass().getResourceAsStream("/logo/group/default.png"))));

    }

}
