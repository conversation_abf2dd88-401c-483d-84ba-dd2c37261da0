package com.bogo.boot.file.listener;

import com.bogo.boot.app.event.AppActionEvent;
import com.bogo.boot.contact.event.FriendActionEvent;
import com.bogo.boot.file.event.ImageUploadEvent;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.group.repository.GroupRobotRepository;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.constant.MessageAction;
import jakarta.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class ImageChangeObserver {

    @Resource
    private ApplicationEventProducer applicationEventProducer;

    @Resource
    private GroupRobotRepository groupRobotRepository;

    @EventListener(value = ImageUploadEvent.class,condition = "#root.args[0].isUserLogo")
    @Async("eventTaskExecutor")
    public void onUserLogoChanged(ImageUploadEvent event){

        FriendActionEvent friendEvent = new FriendActionEvent();
        friendEvent.setSender(event.getId());
        friendEvent.setAction(MessageAction.ACTION_102);
        applicationEventProducer.publish(friendEvent);
    }

    @EventListener(value = ImageUploadEvent.class,condition = "#root.args[0].isUserWallpaper")
    @Async("eventTaskExecutor")
    public void onUserWallpaperChanged(ImageUploadEvent event){

        FriendActionEvent friendEvent = new FriendActionEvent();
        friendEvent.setSender(event.getId());
        friendEvent.setAction(MessageAction.ACTION_113);
        applicationEventProducer.publish(friendEvent);
    }


    @EventListener(value = ImageUploadEvent.class,condition = "#root.args[0].isGroupLogo")
    @Async("eventTaskExecutor")
    public void onGroupLogoChanged(ImageUploadEvent event){
        GroupActionEvent groupEvent = new GroupActionEvent();
        groupEvent.setSender(event.getId());
        groupEvent.setAction(MessageAction.ACTION_306);
        applicationEventProducer.publish(groupEvent);
    }

    @EventListener(value = ImageUploadEvent.class,condition = "#root.args[0].isRobotLogo")
    @Async("eventTaskExecutor")
    public void onRobotLogoChanged(ImageUploadEvent event){
        Long groupId = groupRobotRepository.findGroupId(event.getId());
        GroupActionEvent groupEvent = new GroupActionEvent();
        groupEvent.setSender(groupId);
        groupEvent.setAction(MessageAction.ACTION_315);
        groupEvent.setContent(String.valueOf(event.getId()));
        groupEvent.setReplaceable(false);
        applicationEventProducer.publish(groupEvent);
    }

    @EventListener(value = ImageUploadEvent.class,condition = "#root.args[0].isAppLogo")
    @Async("eventTaskExecutor")
    public void onAppLogoChanged(ImageUploadEvent event){

        AppActionEvent appEvent = new AppActionEvent();
        appEvent.setSender(event.getId());
        appEvent.setAction(MessageAction.ACTION_204);
        applicationEventProducer.publish(appEvent);

    }

    @EventListener(value = ImageUploadEvent.class,condition = "#root.args[0].isMomentWallpaper")
    @Async("eventTaskExecutor")
    public void onMomentWallpaperChanged(ImageUploadEvent event){
        FriendActionEvent friendEvent = new FriendActionEvent();
        friendEvent.setSender(event.getId());
        friendEvent.setAction(MessageAction.ACTION_504);
        applicationEventProducer.publish(friendEvent);
    }
}
