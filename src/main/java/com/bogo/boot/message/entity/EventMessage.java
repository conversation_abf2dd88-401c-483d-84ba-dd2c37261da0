
package com.bogo.boot.message.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_message_event")
@Getter
@Setter
public class EventMessage extends Message implements Serializable {

	@Column(name = "sender", nullable = false)
	private Long sender;

	@Column(name = "receiver")
	private Long receiver;

	@Column(name = "extra", length = 1000)
	private String extra;

	@Column(name = "title", length = 1000)
	private String title;

	@Override
	public Long getSender() {
		return sender;
	}

	@Override
	public void setSender(Long sender) {
		this.sender = sender;
	}

	@Override
	public Long getReceiver() {
		return receiver;
	}

	@Override
	public void setReceiver(Long receiver) {
		this.receiver = receiver;
	}

	@Override
	public String getExtra() {
		return extra;
	}

	@Override
	public void setExtra(String extra) {
		this.extra = extra;
	}

	@Override
	public String getTitle() {
		return title;
	}

	@Override
	public void setTitle(String title) {
		this.title = title;
	}
}
