
package com.bogo.boot.message.entity;

import com.bogo.boot.message.constant.MessageAction;
import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

/**
 * 待接收消息队列，存储用户离线是产生的为接收消息
 * 根据UID 尾号分表存储
 */
@MappedSuperclass
@Getter
@Setter
public class MessageIndex{

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "uid", nullable = false)
	private Long uid;

	/**
	 消息唯一ID
	 */
	@Column(name = "mid", nullable = false)
	private Long mid;

	private Long sender;

	/**
	 * @see MessageAction
	 * 不同action 意味着通过mid去不同的消息表查询消息
	 */
	@Column(name = "action",length = 6 , nullable = false)
	private String action;

	@Column(name = "create_time", updatable = false)
	private Long createTime;

	public static MessageIndex of(Message message,long receiver) {
		MessageIndex index = new MessageIndex();
		index.setCreateTime(message.getCreateTime());
		index.setMid(message.getId());
		index.setUid(receiver);
		index.setSender(message.getSender());
		index.setAction(message.getAction());
		return index;
	}

}
