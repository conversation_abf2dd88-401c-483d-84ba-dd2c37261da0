
package com.bogo.boot.message.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_message_app")
@Getter
@Setter
public class AppMessage extends Message{

	@Column(name = "app_id", nullable = false)
	private Long appId;

	@Column(name = "uid")
	private Long uid;

	@Transient
	private Long receiver;

	@Transient
	private Long sender;

	@Override
	public Long getSender() {
		return sender;
	}

	@Override
	public void setSender(Long sender) {
		this.sender = sender;
	}

	@Override
	public Long getReceiver() {
		return receiver;
	}

	@Override
	public void setReceiver(Long receiver) {
		this.receiver = receiver;
	}
}
