
package com.bogo.boot.message.entity;

import com.bogo.boot.message.constant.MessageState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_message_chat")
@Getter
@Setter
public class ChatMessage extends Message {

	@Column(name = "sender", nullable = false)
	private Long sender;

	@Column(name = "receiver")
	private Long receiver;

	@Column(name = "title", length = 1000)
	private String title;

	/**
	 * @see MessageState
	 */
	@Column(name = "state", length = 2,nullable = false)
	private Byte state = MessageState.STATE_NOT_RECEIVED;

	@Override
	public Long getSender() {
		return sender;
	}

	@Override
	public void setSender(Long sender) {
		this.sender = sender;
	}

	@Override
	public Long getReceiver() {
		return receiver;
	}

	@Override
	public void setReceiver(Long receiver) {
		this.receiver = receiver;
	}

	@Override
	public String getTitle() {
		return title;
	}

	@Override
	public void setTitle(String title) {
		this.title = title;
	}

}
