
package com.bogo.boot.message.entity;

import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.message.constant.MessageAction;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_message_system")
@Getter
@Setter
public class SystemMessage extends Message{

	@Column(name = "uid")
	private Long uid;

	@Column(name = "title", length = 100)
	private String title;

	@Column(name = "extra", length = 1000)
	protected String extra;


	@Override
	public String getTitle() {
		return title;
	}

	@Override
	public void setTitle(String title) {
		this.title = title;
	}

	@Override
	public String getAction() {
		return MessageAction.ACTION_2;
	}

	@Override
	public Long getSender() {
		return Common.SYSTEM_ID;
	}

	@Override
	public void setSender(Long sender) {
	}

	@Override
	public Long getReceiver() {
		return uid;
	}

	@Override
	public void setReceiver(Long receiver) {
		this.uid = receiver;
	}

	@Override
	public String getExtra() {
		return extra;
	}

	@Override
	public void setExtra(String extra) {
		this.extra = extra;
	}
}
