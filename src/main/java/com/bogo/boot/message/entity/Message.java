
package com.bogo.boot.message.entity;

import com.bogo.boot.infra.model.Sortable;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
public abstract class Message implements Serializable, Sortable {

	@Id
	@Column(name = "id")
	protected Long id;

	/**
	 * 消息类型
	 * @see MessageAction
	 */
	@Column(name = "action",length = 6 , nullable = false)
	protected String action;

	@Column(name = "content")
	protected String content;

	/**
	 * 消息格式
	 * @see MessageFormat
	 */
	@Column(name = "format", length = 2)
	protected Byte format = MessageFormat.TEXT;

	@Column(name = "create_time", updatable = false)
	protected Long createTime = new Date().getTime();


	public Byte getState() {
		return null;
	}

	public void setState(Byte state) {
	}

	public String getTitle() {
		return null;
	}

	public void setTitle(String title) {
	}

	public String getExtra() {
		return null;
	}

	public void setExtra(String extra) {
	}

	public abstract Long getSender();

	public abstract void setSender(Long sender) ;

	public abstract Long getReceiver();

	public abstract void setReceiver(Long receiver);

	@Override
	public int hashCode() {
		return Message.class.hashCode();
	}

	@Override
	public boolean equals(Object o) {
		if (o instanceof Message) {
            return Objects.equals(((Message)o).id, id);
		}
		return false;
	}

}
