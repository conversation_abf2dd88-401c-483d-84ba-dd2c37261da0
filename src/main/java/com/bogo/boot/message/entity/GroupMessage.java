
package com.bogo.boot.message.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "t_bogo_message_group")
@Getter
@Setter
public class GroupMessage extends Message{

	@Setter
    @Getter
    @Column(name = "group_id", nullable = false)
	private Long groupId;

	@Setter
    @Getter
    @Column(name = "uid", nullable = false)
	private Long uid;

	@Column(name = "extra", length = 1000)
	private String extra;

	@Setter
    @Getter
    @Column(name = "read_count")
	private Integer readCount;

	@Transient
	private String title;

	@Transient
	private Long receiver;

	@Transient
	private Long sender;

	@Override
	public Long getSender() {
		return sender;
	}

	@Override
	public void setSender(Long sender) {
		this.sender = sender;
	}

	@Override
	public Long getReceiver() {
		return receiver;
	}

	@Override
	public void setReceiver(Long receiver) {
		this.receiver = receiver;
	}

	@Override
	public String getTitle() {
		return title;
	}

	@Override
	public void setTitle(String title) {
		this.title = title;
	}

	@Override
	public String getExtra() {
		return extra;
	}

	@Override
	public void setExtra(String extra) {
		this.extra = extra;
	}

}
