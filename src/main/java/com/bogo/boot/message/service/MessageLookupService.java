
package com.bogo.boot.message.service;

import com.bogo.boot.message.entity.Message;
import java.util.List;
import org.springframework.data.domain.Page;

public interface MessageLookupService {


	/**
	 * 分页查询群消息列表，
	 * @param id
	 * @param fromId 查这个消息ID之前的消息，避免按分页查询
	 * @return
	 */
	Page<Message> findGroup(long id, long fromId);

	Page<Message> findApp(long id, long fromId);

	Page<Message> findP2p(Long uid, long fid, long fromId);

	Page<Message> findSystem(long fromId);

	/**
	 * 查询离线消息
	 * @param uid
	 * @return
	 */
	List<Message> findOfflineList(long uid);

	List<Message> findPatchMessages(long uid,long maxMessageId);

}
