
package com.bogo.boot.message.service.impl;

import com.bogo.boot.message.constant.MessageActionGroup;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.model.StorageMessage;
import com.bogo.boot.message.repository.MessageIndexRepositoryProxy;
import com.bogo.boot.message.service.MessageIndexService;
import com.bogo.boot.message.service.MessageService;
import com.bogo.boot.message.storage.StorageHandler;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MessageIndexServiceImpl implements MessageIndexService {

	@Resource
	private MessageService messageService;

	@Resource
	private MessageIndexRepositoryProxy messageIndexRepositoryProxy;

	@Resource
	private StorageHandler storageHandlerProxy;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void add(Message message) {
		this.add(message, Collections.singletonList(message.getReceiver()));
	}

	/**
	 * 消息保存
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void add(Message message, List<Long> uidList) {

		if (MessageActionGroup.TRANSIENT.contains(message.getAction())){
			return;
		}

		StorageMessage storageMessage = new StorageMessage();
		storageMessage.setMessage(message);
		storageMessage.setReceivers(uidList);


		storageHandlerProxy.save(storageMessage);
	}

	@Override
	public void delete(Long uid) {
		messageIndexRepositoryProxy.delete(uid);
	}

	@Override
	public void delete(Long uid, String action) {
		messageIndexRepositoryProxy.delete(uid,action);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void receive(Long uid, long mid,String action) {
		storageHandlerProxy.receive(uid,mid,action);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> uidList, long mid) {
		messageIndexRepositoryProxy.delete(uidList.toArray(new Long[0]),mid);
	}

	@Override
	public void revoke(long uid,long mid, String action) {
		messageService.revoke(uid,mid,action);
	}
}
