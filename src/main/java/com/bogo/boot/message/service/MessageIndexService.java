
package com.bogo.boot.message.service;

import com.bogo.boot.message.entity.Message;
import java.util.List;

public interface MessageIndexService {

	void add(Message message);

	void add(Message message,List<Long> uidList);

	void delete(Long uid);

	void delete(Long uid,String action);

	void receive(Long uid,long mid,String action);

	void delete(List<Long> uidList,long mid);

	void revoke(long uid,long mid,String action);
}
