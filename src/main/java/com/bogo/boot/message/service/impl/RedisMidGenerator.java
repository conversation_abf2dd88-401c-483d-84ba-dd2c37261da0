
package com.bogo.boot.message.service.impl;

import com.bogo.boot.infra.redis.KeyValueRedisTemplate;
import com.bogo.boot.message.service.MidGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 全局消息ID自增，利用redis自增存储，环境迁移时记得，迁移该消息ID
 */
@Service
public class RedisMidGenerator implements MidGenerator {

	/**
	 * redis全局消息ID起始值
	 */
	private static final long START_MESSAGE_ID = 10000;

	/**
	 * redis全局消息ID自增KEY
	 */
	private static final String KEY_MESSAGE_ID = "ID_MESSAGE_INC";

	private final KeyValueRedisTemplate keyValueRedisTemplate;

	@Autowired
	public RedisMidGenerator(KeyValueRedisTemplate keyValueRedisTemplate){
		this.keyValueRedisTemplate = keyValueRedisTemplate;
		this.keyValueRedisTemplate.setIfAbsent(KEY_MESSAGE_ID,String.valueOf(START_MESSAGE_ID));
	}

	@Override
	public long nextId() {
		return keyValueRedisTemplate.increment(KEY_MESSAGE_ID);
	}
}
