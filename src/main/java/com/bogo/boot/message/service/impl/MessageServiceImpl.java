
package com.bogo.boot.message.service.impl;

import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.api.request.GroupReadRequest;
import com.bogo.boot.message.api.request.ReadRequest;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageState;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.event.DeleteMessageEvent;
import com.bogo.boot.message.event.ReadGroupMessageEvent;
import com.bogo.boot.message.event.ReadMessageEvent;
import com.bogo.boot.message.event.RevokerMessageEvent;
import com.bogo.boot.message.repository.MessageRepositoryProxy;
import com.bogo.boot.message.service.MessageService;
import com.bogo.boot.message.service.MidGenerator;
import jakarta.annotation.Resource;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MessageServiceImpl implements MessageService {

	@Resource
	private MessageRepositoryProxy messageRepositoryProxy;

	@Resource
	private MidGenerator midGenerator;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void revoke(long uid,Long id,String action) {

		Message message = findOne(id,action);

		if (message == null){
			return;
		}

		messageRepositoryProxy.delete(message);

		applicationEventProducer.publish(new DeleteMessageEvent(message));

		applicationEventProducer.publish(new RevokerMessageEvent(uid,message));
	}

	@Override
	public void delete(Long id, String action) {
		Message message = findOne(id,action);
		if (message == null){
			return;
		}
		messageRepositoryProxy.delete(message);
		applicationEventProducer.publish(new DeleteMessageEvent(message));
	}

	@Override
	public int receive(Long id,String action) {
		if (MessageAction.ACTION_0.equals(action)){
			return messageRepositoryProxy.receive(id);
		}
		return -1;
	}

	@Override
	public void read(ReadRequest request) {

		if (CollectionUtils.isEmpty(request.getIdList())){
			return;
		}

		messageRepositoryProxy.read(request.getIdList());

		applicationEventProducer.publish(new ReadMessageEvent(request));
	}

	@Override
	public void read(GroupReadRequest request) {

		if (request.isEmpty()){
			return;
		}

		messageRepositoryProxy.readGroup(request.getIdSet());

		applicationEventProducer.publish(new ReadGroupMessageEvent(request));

	}

	@Override
	public Message findOne(Long id,String action) {
		return messageRepositoryProxy.findOne(id,action);
	}

	@Override
	public void save(Message message) {

		/*
		 * 消息入库时，强制设置消息ID为生成的唯一消息ID
		 * 这里使用了redis自增实现
		 */
		if (message.getId() == null){
			message.setId(midGenerator.nextId());
		}

		if (message.getState() == null) {
			message.setState(MessageState.STATE_NOT_RECEIVED);
		}
		if (message.getCreateTime() == null){
			message.setCreateTime(new Date().getTime());
		}

		messageRepositoryProxy.save(message);
	}

}
