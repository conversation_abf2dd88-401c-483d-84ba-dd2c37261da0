
package com.bogo.boot.message.service.impl;

import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.entity.MessageIndex;
import com.bogo.boot.message.repository.MessageIndexRepositoryProxy;
import com.bogo.boot.message.repository.MessageLookupRepositoryProxy;
import com.bogo.boot.message.repository.MessagePatchRepositoryProxy;
import com.bogo.boot.message.service.MessageLookupService;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
public class MessageLookupServiceImpl implements MessageLookupService {


	@Resource
	private MessageLookupRepositoryProxy messageLookupRepositoryProxy;

	@Resource
	private MessageIndexRepositoryProxy messageIndexRepositoryProxy;

	@Resource
	private MessagePatchRepositoryProxy messagePatchRepositoryProxy;

	@Override
	public Page<Message> findGroup(long id, long fromId) {
		return messageLookupRepositoryProxy.findListOfGroup(id,fromId);
	}

	@Override
	public Page<Message> findApp(long id, long fromId) {
		return messageLookupRepositoryProxy.findListOfApp(id,fromId);
	}

	@Override
	public Page<Message> findP2p(Long uid, long fid, long fromId) {
		return messageLookupRepositoryProxy.findListOfFriend(uid,fid,fromId);
	}

	@Override
	public Page<Message> findSystem(long fromId) {
		return messageLookupRepositoryProxy.findListOfSystem(fromId);
	}

	@Override
	public List<Message> findOfflineList(long uid) {
		List<MessageIndex> indices = messageIndexRepositoryProxy.findList(uid);
        return messageLookupRepositoryProxy.findList(indices);
	}

	@Override
	public List<Message> findPatchMessages(long uid, long maxMessageId) {
		List<MessageIndex> indices = messagePatchRepositoryProxy.findList(uid,maxMessageId);
        return messageLookupRepositoryProxy.findList(indices);
	}
}
