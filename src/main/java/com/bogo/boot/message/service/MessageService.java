
package com.bogo.boot.message.service;

import com.bogo.boot.message.api.request.GroupReadRequest;
import com.bogo.boot.message.api.request.ReadRequest;
import com.bogo.boot.message.entity.Message;

public interface MessageService {

	/**
	 * 保存通知信息
	 * 
	 * @param message
	 */
	void save(Message message);

	void revoke(long uid,Long id,String action);

	void delete(Long id,String action);

	/**
	 * 设置单聊消息已接收状态
	 * @param id
	 */
	int receive(Long id,String action);

	/**
	 * 设置单聊消息已阅读状态
	 * @param request
	 */
	void read(ReadRequest request);


	/**
	 * 设置单聊消息已阅读状态
	 * @param request
	 */
	void read(GroupReadRequest request);

	Message findOne(Long id,String action);
}
