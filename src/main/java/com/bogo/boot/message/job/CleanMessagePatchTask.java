package com.bogo.boot.message.job;

import com.bogo.boot.infra.configuration.properties.MessageProperties;
import com.bogo.boot.message.repository.MessagePatchRepositoryProxy;
import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(
        name = {"bogo.message.patch.enable"}
)
public class CleanMessagePatchTask {

    @Resource
    private MessagePatchRepositoryProxy patchRepositoryProxy;

    @Resource
    private MessageProperties messageProperties;

    @Scheduled(cron = "0 0 0 * * ? ")
    private void run(){
        /*
         在此之前的补偿记录全部清除
         */
        long cleanBeforeAt = System.currentTimeMillis() - messageProperties.getPatch().getLifecycle().toMillis();

        patchRepositoryProxy.cleanup(cleanBeforeAt);
    }

}
