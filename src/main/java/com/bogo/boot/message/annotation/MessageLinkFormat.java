
package com.bogo.boot.message.annotation;


import com.bogo.boot.message.api.validator.MessageLinkValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Constraint(validatedBy = MessageLinkValidator.class)
public @interface MessageLinkFormat {

    String message();

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
