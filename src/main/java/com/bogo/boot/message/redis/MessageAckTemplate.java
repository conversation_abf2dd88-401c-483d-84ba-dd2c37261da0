
package com.bogo.boot.message.redis;

import com.bogo.boot.infra.redis.LongRedisTemplate;
import java.time.Duration;
import java.util.Set;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.stereotype.Component;

@Component
public class MessageAckTemplate extends LongRedisTemplate {

	private static final String KEY = "ASYNC_MESSAGE_ACK_%s";

	private static final Duration DURATION = Duration.ofHours(1);

	public MessageAckTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void setAck(long messageId ,long uid) {
		String key = String.format(KEY,messageId);
		BoundSetOperations<String, Long> operations = super.boundSetOps(key);
		operations.add(uid);
		operations.expire(DURATION);
	}

	public Set<Long> getAckedUidSet(long messageId) {
		String key = String.format(KEY,messageId);
		BoundSetOperations<String, Long> operations = super.boundSetOps(key);
		return operations.members();
	}
}
