
package com.bogo.boot.message.redis;

import com.bogo.boot.infra.redis.LongRedisTemplate;
import java.time.Duration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.stereotype.Component;

@Component
public class LastMessageIdTemplate extends LongRedisTemplate {

	private static final String KEY = "MID_%d_%s";

	private static final Duration DURATION = Duration.ofHours(2);

	public LastMessageIdTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void set(long id ,String action,long mid) {
		String key = String.format(KEY,id,action);
		BoundValueOperations<String, Long>  operations = super.boundValueOps(key);
		operations.set(mid);
		operations.expire(DURATION);
	}

	public Long get(long id ,String action) {
		String key = String.format(KEY,id,action);
		return super.boundValueOps(key).get();
	}
}
