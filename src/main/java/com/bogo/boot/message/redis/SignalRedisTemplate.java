
package com.bogo.boot.message.redis;

import com.bogo.boot.account.entity.Session;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.holder.EnvironmentHolder;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.event.MakeSessionEvent;
import com.bogo.boot.message.event.PushMessageEvent;
import com.bogo.boot.message.event.StorageMessageEvent;
import com.bogo.boot.message.model.PushMessage;
import com.bogo.boot.message.model.StorageMessage;
import jakarta.annotation.Resource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class SignalRedisTemplate extends StringRedisTemplate {

	@Resource
	private EnvironmentHolder environmentHolder;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	public SignalRedisTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	/**
	 * 消息发送到 集群中的每个实例，获取对应长连接进行消息写入
	 * @param message
	 */
	public void push(PushMessage message) {
		if (environmentHolder.isDev()){
			applicationEventProducer.publish(new PushMessageEvent(message));
			return;
		}
		super.convertAndSend(Common.PUSH_MESSAGE_INNER_QUEUE, JSON.toJSONString(message));
	}

	/**
	 * 消息发送到 集群中的每个实例，解决多终端在线冲突问题
	 * @param session
	 */
	public void bind(Session session) {
		if (environmentHolder.isDev()){
			applicationEventProducer.publish(new MakeSessionEvent(session));
			return;
		}
		super.convertAndSend(Common.BIND_MESSAGE_INNER_QUEUE, JSON.toJSONString(session));
	}


	/**
	 * 异步存储消息，发送消息队列异步接收存储
	 * @param message
	 */
	public void save(StorageMessage message) {
		if (environmentHolder.isDev()){
			applicationEventProducer.publish(new StorageMessageEvent(message));
			return;
		}
		super.convertAndSend(Common.STORAGE_MESSAGE_INNER_QUEUE, JSON.toJSONString(message));
	}
}
