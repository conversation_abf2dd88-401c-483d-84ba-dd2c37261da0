package com.bogo.boot.message.storage;

import com.bogo.boot.message.model.StorageMessage;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 消息存储策略
 * 根据开关可选择同步入库或者是异步入库
 */
@Component
public class StorageHandlerProxy implements StorageHandler {

    @Value("${bogo.message.storage.instant}")
    private boolean instant;

    @Resource
    private StorageHandler instantStorageHandler;

    @Resource
    private StorageHandler asyncStorageHandler;

    @Override
    public void save(StorageMessage message) {

        if (instant) {
            instantStorageHandler.save(message);
        }else {
            asyncStorageHandler.save(message);
        }

    }

    @Override
    public void receive(Long uid, long mid, String action) {
        if (instant) {
            instantStorageHandler.receive(uid, mid, action);
        }else {
            asyncStorageHandler.receive(uid, mid, action);
        }
    }

}
