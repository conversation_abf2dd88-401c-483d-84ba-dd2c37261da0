package com.bogo.boot.message.storage;

import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageActionGroup;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.entity.MessageIndex;
import com.bogo.boot.message.model.StorageMessage;
import com.bogo.boot.message.repository.MessageIndexRepositoryProxy;
import com.bogo.boot.message.repository.MessagePatchRepositoryProxy;
import com.bogo.boot.message.service.MessageService;
import jakarta.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class InstantStorageHandler implements StorageHandler {

    @Resource
    protected MessageService messageService;

    @Resource
    protected MessageIndexRepositoryProxy messageIndexRepositoryProxy;

    @Resource
    protected MessagePatchRepositoryProxy messagePatchRepositoryProxy;

    @Override
    public void save(StorageMessage storageMessage) {

        Message message = storageMessage.getMessage();

        messageService.save(message);

        List<MessageIndex> indices = new LinkedList<>();
        for (Long uid : storageMessage.getReceivers()){
            indices.add(MessageIndex.of(message,uid));
        }

        messageIndexRepositoryProxy.save(indices);

        /* 如果是单聊天消息，将自己发送的记录也存入消息补偿 */
        if (MessageAction.ACTION_0.contains(message.getAction())){
            indices.add(MessageIndex.of(message,message.getSender()));
        }

        /* 如果是群聊天消息，将自己发送的记录也存入消息补偿，发送者是extra字段 */
        if (MessageAction.ACTION_3.contains(message.getAction())){
            indices.add(MessageIndex.of(message,Long.parseLong(message.getExtra())));
        }

		/*
		 保存到多端同步补偿表
		 */
        messagePatchRepositoryProxy.save(indices);
    }


    @Override
    public void receive(Long uid, long mid, String action) {
        messageIndexRepositoryProxy.delete(uid,mid);
        messageService.receive(mid,action);
    }
}
