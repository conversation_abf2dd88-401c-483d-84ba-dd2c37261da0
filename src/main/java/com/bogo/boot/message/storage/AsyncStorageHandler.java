package com.bogo.boot.message.storage;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageState;
import com.bogo.boot.message.event.StorageMessageEvent;
import com.bogo.boot.message.model.StorageMessage;
import com.bogo.boot.message.redis.MessageAckTemplate;
import com.bogo.boot.message.redis.SignalRedisTemplate;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 * 异步存储这里使用了redis消息队列，有必要可以自行替换为其他MQ
 */
@Component
public class AsyncStorageHandler extends InstantStorageHandler implements MessageListener {

    private static final String LOCK_KEY = "ASYNC_SAVE_MESSAGE_LOCK_%s";

    @Resource
    private SignalRedisTemplate signalRedisTemplate;

    @Resource
    private MessageAckTemplate messageAckTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public void save(StorageMessage message) {
        signalRedisTemplate.save(message);
    }

    @Override
    public void receive(Long uid, long mid, String action) {

        int removeCount = messageIndexRepositoryProxy.delete(uid,mid);
        int updateCount = messageService.receive(mid,action);

        /*
         * 客户接收很快，服务端还没保存
         * 预先标记消息，不存储待接收队列 和 标记为已经接收状态
         */
        if (removeCount == 0 && updateCount == 0) {
            messageAckTemplate.setAck(mid,uid);
        }
    }

    /**
     * 接收到MQ的存储消息
     * redis 是广播模式，需要用分布式锁 保证一台服务器来处理存储即可
     * 非广播模式下的MQ 可去掉分布式锁
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {

        StorageMessage storageMessage = JSON.parse(message.getBody(), StorageMessage.class);

        String key = String.format(LOCK_KEY, storageMessage.getMessage().getId());
        RLock lock = redissonClient.getLock(key);

        if (!lock.tryLock()) {
            return;
        }

        try {
            beforAckHandle(storageMessage);
            super.save(storageMessage);
        } finally {
            lock.unlock();
        }

    }

    /**
     * ACK 补偿
     * @param storageMessage
     */
    private void beforAckHandle(StorageMessage storageMessage) {
        Set<Long> ackedUidSet = messageAckTemplate.getAckedUidSet(storageMessage.getMessage().getId());
        if (CollectionUtils.isEmpty(ackedUidSet)) {
            return;
        }

        /*
         * 已经发送过接收回执的UID，不存待接收队列了
         */
        storageMessage.removeUidList(ackedUidSet);

        /*
         单聊消息的话 设置状态为已经接收
         */
        if (Objects.equals(storageMessage.getMessage().getAction(),MessageAction.ACTION_0)){
            storageMessage.getMessage().setState(MessageState.STATE_RECEIVED);
        }

    }

    /**
     * 本地开发环境走内部事件
     * @param event
     */
    @EventListener
    public void onMessage(StorageMessageEvent event) {
        super.save(event.getSource());
    }

}
