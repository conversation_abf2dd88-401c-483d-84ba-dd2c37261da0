package com.bogo.boot.message.predicate;

import com.bogo.boot.message.model.SendCondition;
import com.bogo.messaging.constant.ChannelAttr;
import io.netty.channel.Channel;
import java.util.function.Predicate;

/**
 * 发时消息，终端类型过滤
 */
public class ExcludeChannelPredicate implements Predicate<Channel> {

    private final SendCondition condition;

    public static Predicate<Channel> of(SendCondition condition){
        return new ExcludeChannelPredicate(condition);
    }

    public ExcludeChannelPredicate(SendCondition condition) {
        this.condition = condition;
    }

    @Override
    public boolean test(Channel channel) {
        String clientChannel = channel.attr(ChannelAttr.CHANNEL).get();
        return !condition.getExcludedChannels().contains(clientChannel);
    }

}
