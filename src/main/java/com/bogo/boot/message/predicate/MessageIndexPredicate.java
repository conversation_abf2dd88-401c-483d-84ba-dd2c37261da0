package com.bogo.boot.message.predicate;

import com.bogo.boot.message.entity.MessageIndex;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * 根据消息action筛选
 */
public class MessageIndexPredicate implements Predicate<MessageIndex> {

    private final String action;

    public MessageIndexPredicate(String action) {
        this.action = action;
    }

    @Override
    public boolean test(MessageIndex index) {
        return Objects.equals(action,index.getAction());
    }

}
