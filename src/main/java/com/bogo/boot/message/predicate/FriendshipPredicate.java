package com.bogo.boot.message.predicate;

import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.contact.service.FriendService;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.function.BiPredicate;
import org.springframework.stereotype.Component;

/**
 * 是好友关系或者是同组织成员
 */
@Component
public class FriendshipPredicate implements BiPredicate<Long,Long> {

    @Resource
    private FriendService friendService;

    @Resource
    private UserService userService;

    @Override
    public boolean test(Long fromId, Long toId) {
        if (friendService.isFriend(fromId,toId)){
            return true;
        }

        User from = userService.findOne(fromId);
        if (from == null || from.getOrganizationId() == null){
            return false;
        }

        User to = userService.findOne(toId);
        if (to == null || to.getOrganizationId() == null){
            return false;
        }

        return Objects.equals(from.getOrganizationId(), to.getOrganizationId());
    }
}
