package com.bogo.boot.message.predicate;

import com.bogo.boot.infra.configuration.properties.MessageProperties;
import com.bogo.boot.infra.entity.BlackWord;
import com.bogo.boot.infra.repository.BlackWordRepository;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.exception.ChatForbiddenException;
import com.bogo.boot.message.feign.IBlackwordHookService;
import com.bogo.boot.message.feign.request.BlackwordHookRequest;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import jakarta.annotation.Resource;
import java.net.URI;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 发时消息判断内容中是否存在敏感词
 */
@Component
public class BlackwordPredicate implements Consumer<Message> {

    private static final String KEY = "BAN_WORDS";

    @Resource
    private BlackWordRepository wordRepository;

    @Resource
    private MessageProperties properties;

    @Resource
    private IBlackwordHookService hookService;


    /*
     敏感词缓存 1小时更新一次
     */
    private final LoadingCache<String, List<String>> keywordCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(CacheLoader.from(id -> wordRepository.findAll().stream().map(BlackWord::getContent).toList()));


    @Override
    public void accept(Message message) {

        /*
        仅检查文字消息
         */
        if (!message.getFormat().equals(MessageFormat.TEXT) || !properties.getBlackword().isEnable()){
            return;
        }

        boolean isHitBanWords = keywordCache.getUnchecked(KEY).stream().anyMatch(words -> message.getContent().contains(words));

        /*
         通知其他服务触发了敏感词
         */
        if (isHitBanWords && StringUtils.isNotBlank(properties.getBlackword().getWebhook())){
            BlackwordHookRequest request = new BlackwordHookRequest();
            request.setAction(message.getAction());
            request.setContent(message.getContent());
            request.setReceiver(message.getReceiver());
            request.setSender(message.getSender());
            hookService.notify(URI.create(properties.getBlackword().getWebhook()),request);
        }

        /*
         拦截本条消息发送
         */
        if (isHitBanWords && properties.isBlackwordIntercept()){
            throw new ChatForbiddenException("消息内容包含敏感词");
        }

    }

}
