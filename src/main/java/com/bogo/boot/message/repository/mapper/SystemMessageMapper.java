package com.bogo.boot.message.repository.mapper;

import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.entity.SystemMessage;

public class SystemMessageMapper extends BaseMessageMapper<Message>{
    @Override
    public SystemMessage save(Message message) {
        SystemMessage systemMessage = new SystemMessage();
        super.copy(message,systemMessage);
        systemMessage.setUid(message.getReceiver());
        return systemMessage;
    }

    @Override
    public Message view(Message message) {
        SystemMessage source = (SystemMessage) message;
        SystemMessage systemMessage = new SystemMessage();
        super.copy(source,systemMessage);
        systemMessage.setReceiver(source.getUid());
        systemMessage.setSender(Common.SYSTEM_ID);
        return systemMessage;
    }
}
