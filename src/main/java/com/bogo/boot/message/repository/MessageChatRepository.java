
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.ChatMessage;
import com.bogo.boot.message.entity.Message;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MessageChatRepository extends MessageRepository{


	@Override
	@Modifying
	@Query("update ChatMessage set state=:state  where id in(:ids)")
	int updateState(Set<Long> ids, byte state);

	@Override
	@Query("from ChatMessage where id in (:ids) order by id")
	List<Message> findList(Long[] ids);

	@Override
	@Query("from ChatMessage where id = :id")
	ChatMessage findOne(long id);

}
