
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.MessageIndex;
import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MessageIndex2Repository extends MessageIndexRepository {

	@Modifying
	@Query("delete from MessageIndex2 where uid = ?1")
	void delete(long uid);

	@Modifying
	@Query("delete from MessageIndex2 where uid = ?1 and mid = ?2")
	int delete(long uid,long mid);

	@Modifying
	@Query("delete from MessageIndex2 where uid in (?1) and mid = ?2")
	void delete(List<Long> uidList,long mid);

	@Query("from MessageIndex2 where uid = ?1 order by id")
	List<MessageIndex> findList(long uid);

	@Modifying
	@Query("delete from MessageIndex2 where uid = ?1 and action = ?2")
	void delete(long uid,String action);
}
