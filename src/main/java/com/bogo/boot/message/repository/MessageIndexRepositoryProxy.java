
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.MessageIndex;
import com.bogo.boot.message.entity.MessageIndex0;
import com.bogo.boot.message.entity.MessageIndex1;
import com.bogo.boot.message.entity.MessageIndex2;
import com.bogo.boot.message.entity.MessageIndex3;
import com.bogo.boot.message.entity.MessageIndex4;
import com.bogo.boot.message.entity.MessageIndex5;
import com.bogo.boot.message.entity.MessageIndex6;
import com.bogo.boot.message.entity.MessageIndex7;
import com.bogo.boot.message.entity.MessageIndex8;
import com.bogo.boot.message.entity.MessageIndex9;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消息队列 根据UID尾号分表实现
 */
@Component
public class MessageIndexRepositoryProxy  {

	private final Map<Integer,MessageIndexRepository> messageIndexRepositoryMap = new HashMap<>();

	private final Map<Integer,Supplier<MessageIndex>> messageIndexEntityMap = new HashMap<>();

	@Autowired
	public MessageIndexRepositoryProxy(MessageIndex0Repository messageIndex0Repository,
									   MessageIndex1Repository messageIndex1Repository,
									   MessageIndex2Repository messageIndex2Repository,
									   MessageIndex3Repository messageIndex3Repository,
									   MessageIndex4Repository messageIndex4Repository,
									   MessageIndex5Repository messageIndex5Repository,
									   MessageIndex6Repository messageIndex6Repository,
									   MessageIndex7Repository messageIndex7Repository,
									   MessageIndex8Repository messageIndex8Repository,
									   MessageIndex9Repository messageIndex9Repository){

		messageIndexRepositoryMap.put(0,messageIndex0Repository);
		messageIndexRepositoryMap.put(1,messageIndex1Repository);
		messageIndexRepositoryMap.put(2,messageIndex2Repository);
		messageIndexRepositoryMap.put(3,messageIndex3Repository);
		messageIndexRepositoryMap.put(4,messageIndex4Repository);
		messageIndexRepositoryMap.put(5,messageIndex5Repository);
		messageIndexRepositoryMap.put(6,messageIndex6Repository);
		messageIndexRepositoryMap.put(7,messageIndex7Repository);
		messageIndexRepositoryMap.put(8,messageIndex8Repository);
		messageIndexRepositoryMap.put(9,messageIndex9Repository);

		messageIndexEntityMap.put(0, MessageIndex0::new);
		messageIndexEntityMap.put(1, MessageIndex1::new);
		messageIndexEntityMap.put(2, MessageIndex2::new);
		messageIndexEntityMap.put(3, MessageIndex3::new);
		messageIndexEntityMap.put(4, MessageIndex4::new);
		messageIndexEntityMap.put(5, MessageIndex5::new);
		messageIndexEntityMap.put(6, MessageIndex6::new);
		messageIndexEntityMap.put(7, MessageIndex7::new);
		messageIndexEntityMap.put(8, MessageIndex8::new);
		messageIndexEntityMap.put(9, MessageIndex9::new);

	}


	public void delete(long uid) {
		getMessageIndexRepository(uid).delete(uid);
	}

	public int delete(long uid, long mid) {
		return getMessageIndexRepository(uid).delete(uid,mid);
	}

	public void delete(Long[] uidList, long mid) {
		Map<Integer, List<Long>> indexGroup = Arrays.stream(uidList).collect(Collectors.groupingBy(this::getIndexNumber));
		for (Map.Entry<Integer, List<Long>> entry : indexGroup.entrySet()) {
			int index = entry.getKey();
			List<Long> idList = entry.getValue();
			getMessageIndexRepository(index).delete(idList,mid);
		}
	}


	public void save(List<MessageIndex> indices) {
		Map<Integer, List<MessageIndex>> indexGroup = indices.stream().collect(Collectors.groupingBy(messageIndex -> getIndexNumber(messageIndex.getUid())));
		for (Map.Entry<Integer, List<MessageIndex>> entry : indexGroup.entrySet()) {
			int index = entry.getKey();

			/*
			 * 转为对应的存储index实体，存如对应的分表
			 */
			List<MessageIndex>  addableIndices = entry.getValue().stream().map(new TableMapping(index)).collect(Collectors.toList());

			messageIndexRepositoryMap.get(index).saveAllAndFlush(addableIndices);
		}
	}


	public List<MessageIndex> findList(long uid) {
		return getMessageIndexRepository(uid).findList(uid);
	}

	public void delete(long uid, String action) {
		getMessageIndexRepository(uid).delete(uid,action);
	}


	/**
	 * 获取UID尾号分表
	 * @param uid 原始UID
	 * @return 分表索引
	 */
	private int getIndexNumber(long uid) {
		return (int) (uid % 10);
	}

	private MessageIndexRepository getMessageIndexRepository(long uid){
		return messageIndexRepositoryMap.get(getIndexNumber(uid));
	}

	private MessageIndexRepository getMessageIndexRepository(int index){
		return messageIndexRepositoryMap.get(index);
	}

	/*
	 * 转为对应的存储index实体，存如对应的分表
	 */
	private class TableMapping implements Function<MessageIndex,MessageIndex>{

		private final Supplier<MessageIndex> supplier ;

		private TableMapping(int index) {
			this.supplier = messageIndexEntityMap.get(index);
        }

        @Override
		public MessageIndex apply(MessageIndex messageIndex) {
			MessageIndex copy = supplier.get();
			copy.setUid(messageIndex.getUid());
			copy.setAction(messageIndex.getAction());
			copy.setMid(messageIndex.getMid());
			copy.setSender(messageIndex.getSender());
			copy.setCreateTime(messageIndex.getCreateTime());
			return copy;
		}
	}

}
