
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.Message;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MessageAppRepository extends MessageRepository{

	@Override
	@Query("from AppMessage where id in (:ids) order by id")
	List<Message> findList(Long[] ids);

	@Override
	@Query("from AppMessage where id = :id")
	Message findOne(long id);
}
