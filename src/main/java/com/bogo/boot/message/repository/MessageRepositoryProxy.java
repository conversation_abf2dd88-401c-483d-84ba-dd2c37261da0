package com.bogo.boot.message.repository;


import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageState;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.entity.MessageIndex;
import com.bogo.boot.message.predicate.MessageIndexPredicate;
import com.bogo.boot.message.repository.mapper.AppMessageMapper;
import com.bogo.boot.message.repository.mapper.BaseMessageMapper;
import com.bogo.boot.message.repository.mapper.GroupMessageMapper;
import com.bogo.boot.message.repository.mapper.NoneMessageMapper;
import com.bogo.boot.message.repository.mapper.P2pMessageMapper;
import com.bogo.boot.message.repository.mapper.SystemMessageMapper;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MessageRepositoryProxy {

    private final MessageEventRepository messageEventRepository;

    private final MessageChatRepository messageChatRepository;

    private final MessageGroupRepository messageGroupRepository;

    private final Map<String, MessageRepository> repositoryMap = new HashMap<>();
    private final Map<String, BaseMessageMapper<? extends Message>> mappingMap = new HashMap<>();

    private final BaseMessageMapper<? extends Message> noneMessageMapper = new NoneMessageMapper();


    @Autowired
    public MessageRepositoryProxy(MessageAppRepository messageAppRepository,
                                  MessageChatRepository messageChatRepository,
                                  MessageGroupRepository messageGroupRepository,
                                  MessageEventRepository messageEventRepository,
                                  MessageSystemRepository messageSystemRepository
                                  ){
        this.messageEventRepository = messageEventRepository;
        this.messageChatRepository = messageChatRepository;
        this.messageGroupRepository = messageGroupRepository;

        repositoryMap.put(MessageAction.ACTION_0, messageChatRepository);
        repositoryMap.put(MessageAction.ACTION_2, messageSystemRepository);
        repositoryMap.put(MessageAction.ACTION_3, messageGroupRepository);
        repositoryMap.put(MessageAction.ACTION_4, messageGroupRepository);
        repositoryMap.put(MessageAction.ACTION_200, messageAppRepository);

        mappingMap.put(MessageAction.ACTION_0,new P2pMessageMapper());
        mappingMap.put(MessageAction.ACTION_2,new SystemMessageMapper());
        mappingMap.put(MessageAction.ACTION_3,new GroupMessageMapper());
        mappingMap.put(MessageAction.ACTION_4,new GroupMessageMapper());
        mappingMap.put(MessageAction.ACTION_200,new AppMessageMapper());
    }

    public void save(Message message){

        Message newMessage = mappingMap.getOrDefault(message.getAction(),noneMessageMapper).save(message);

        MessageRepository messageRepository = repositoryMap.getOrDefault(message.getAction(), messageEventRepository);

        messageRepository.saveAndFlush(newMessage);
    }


    public void delete(Message message){
        repositoryMap.getOrDefault(message.getAction(), messageEventRepository).delete(message);
    }

    public Message findOne(Long id,String action) {
        Message message = repositoryMap.getOrDefault(action, messageEventRepository).findOne(id);
        if (message == null){
            return null;
        }
        return mappingMap.getOrDefault(message.getAction(),noneMessageMapper).view(message);
    }

    public List<Message> findList(List<MessageIndex> indices, String action) {

        MessageIndexPredicate filter = new MessageIndexPredicate(action);

        List<Long> idList = indices.stream().filter(new MessageIndexPredicate(action)).map(MessageIndex::getMid).toList();

        if (idList.isEmpty()){
            return new LinkedList<>();
        }

        /*
         查完从indices中删除对应的记录
         */
        indices.removeIf(filter);

        List<Message> dataList = repositoryMap.getOrDefault(action, messageEventRepository).findList(idList.toArray(new Long[0]));

        BaseMessageMapper<? extends Message> messageMapper = mappingMap.getOrDefault(action,noneMessageMapper);

        return dataList.stream().map(messageMapper::view).collect(Collectors.toList());
    }

    /*
     * 查询事件消息
     * @param indices
     * @return
     */
    public List<Message> findList(List<MessageIndex> indices) {

        List<Long> idList = indices.stream().map(MessageIndex::getMid).toList();

        if (idList.isEmpty()){
            return new LinkedList<>();
        }

        List<Message> dataList = messageEventRepository.findList(idList.toArray(new Long[0]));

        return dataList.stream().map(noneMessageMapper::view).collect(Collectors.toList());
    }


    public void readGroup(Set<Long> idList) {
        messageGroupRepository.addReadCount(idList);
    }

    public void read(Set<Long> idList) {
        messageChatRepository.updateState(idList, MessageState.STATE_READ);
    }

    public int receive(Long id) {
        return messageChatRepository.updateState(Collections.singleton(id), MessageState.STATE_RECEIVED);
    }

}
