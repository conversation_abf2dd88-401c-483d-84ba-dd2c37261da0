package com.bogo.boot.message.repository.mapper;

import com.bogo.boot.message.entity.ChatMessage;
import com.bogo.boot.message.entity.Message;

public class P2pMessageMapper extends BaseMessageMapper<Message>{

    @Override
    public ChatMessage save(Message message) {
        return (ChatMessage) message;
    }

    @Override
    public Message view(Message message) {
        return message;
    }
}
