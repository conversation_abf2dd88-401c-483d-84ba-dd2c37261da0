package com.bogo.boot.message.repository.mapper;

import com.bogo.boot.message.entity.AppMessage;
import com.bogo.boot.message.entity.Message;

public class AppMessageMapper extends BaseMessageMapper<Message>{
    @Override
    public AppMessage save(Message message) {
        AppMessage appMessage = new AppMessage();
        super.copy(message,appMessage);
        appMessage.setAppId(message.getSender());
        appMessage.setUid(message.getReceiver());
        return appMessage;
    }

    @Override
    public Message view(Message message) {
        AppMessage source = (AppMessage) message;
        AppMessage appMessage = new AppMessage();
        super.copy(source,appMessage);
        appMessage.setSender(source.getAppId());
        appMessage.setReceiver(source.getUid());
        return appMessage;
    }
}
