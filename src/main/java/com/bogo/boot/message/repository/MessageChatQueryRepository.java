
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.ChatMessage;
import com.bogo.boot.message.entity.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageChatQueryRepository extends JpaRepository<ChatMessage, Long> {
	Page<Message> findAll(Specification<ChatMessage> sp, Pageable pageable);
}
