
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.entity.SystemMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageSystemQueryRepository extends JpaRepository<SystemMessage, Long> {
	Page<Message> findAll(Specification<SystemMessage> sp, Pageable pageable);
}
