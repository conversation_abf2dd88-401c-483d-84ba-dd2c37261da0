
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.Message;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MessageRepository extends JpaRepository<Message, Long> {

	default List<Message> findList(Long[] ids){return Collections.emptyList();}

	default int updateState(Set<Long> idList, byte state){return 0;}

	default Message findOne(long id){return null;}

}
