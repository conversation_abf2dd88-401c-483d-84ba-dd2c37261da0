package com.bogo.boot.message.repository.mapper;

import com.bogo.boot.message.entity.GroupMessage;
import com.bogo.boot.message.entity.Message;

public class GroupMessageMapper extends BaseMessageMapper<Message>{
    @Override
    public GroupMessage save(Message message) {
        GroupMessage groupMessage = new GroupMessage();
        super.copy(message,groupMessage);
        groupMessage.setGroupId(message.getSender());
        groupMessage.setUid(Long.parseLong(message.getExtra()));
        groupMessage.setExtra(message.getTitle());
        groupMessage.setReadCount(0);

        return groupMessage;
    }

    @Override
    public Message view(Message message) {
        GroupMessage source = (GroupMessage) message;
        GroupMessage newMessage = new GroupMessage();
        super.copy(source,newMessage);
        newMessage.setSender(source.getGroupId());
        newMessage.setExtra(source.getUid().toString());
        newMessage.setTitle(message.getExtra());
        return newMessage;
    }
}
