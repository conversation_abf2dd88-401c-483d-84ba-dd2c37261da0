
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.AppMessage;
import com.bogo.boot.message.entity.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageAppQueryRepository extends JpaRepository<AppMessage, Long> {
	Page<Message> findAll(Specification<AppMessage> sp, Pageable pageable);
}
