package com.bogo.boot.message.repository.mapper;

import com.bogo.boot.message.entity.Message;

public abstract class BaseMessageMapper<T extends Message>{
    /**
     * 保存数据库时，做数据库字段和对象字段映射
     * @param message
     * @return
     */
    public abstract T save(Message message);

    /**
     * 查询出库时，做数据库字段和对象字段映射
     * @param message
     * @return
     */
    public abstract Message view(Message message);

    protected void copy(Message source, Message target){
        target.setId(source.getId());
        target.setExtra(source.getExtra());
        target.setContent(source.getContent());
        target.setTitle(source.getTitle());
        target.setAction(source.getAction());
        target.setFormat(source.getFormat());
        target.setState(source.getState());
        target.setCreateTime(source.getCreateTime());
    }
}
