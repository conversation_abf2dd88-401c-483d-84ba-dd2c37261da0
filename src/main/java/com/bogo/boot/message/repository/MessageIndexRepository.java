
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.MessageIndex;
import java.util.Collections;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MessageIndexRepository extends JpaRepository<MessageIndex, Long> {

	default void delete(long uid){}

    default int delete(long uid, long mid){return 0;}

    default void delete(List<Long> uidList,long mid){}

    default List<MessageIndex> findList(long uid){return Collections.emptyList();}

    default void delete(long uid,String action){}

}
