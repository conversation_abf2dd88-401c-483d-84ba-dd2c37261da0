
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.MessageIndex;
import java.util.Collections;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MessagePatchRepository extends JpaRepository<MessageIndex, Long> {

	default void delete(long uid){}

    default void cleanup(long beforeAt){}

    default List<MessageIndex> findList(long uid,long maxMessageId){return Collections.emptyList();}

}
