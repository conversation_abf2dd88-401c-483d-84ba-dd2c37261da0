
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.Message;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


@Repository
@Transactional(rollbackFor = Exception.class)
public interface MessageEventRepository extends MessageRepository {

	@Override
	@Query("from EventMessage where id in (?1) order by id")
	List<Message> findList(Long[] ids);

	@Override
	@Query("from EventMessage where id = ?1")
	Message findOne(long id);

	@Modifying
	@Query("delete from EventMessage where sender = :sender and action in (:action)")
	void deleteAll(long sender,Set<String> action);

}
