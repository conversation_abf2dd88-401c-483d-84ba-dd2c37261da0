package com.bogo.boot.message.repository;


import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.AppMessage;
import com.bogo.boot.message.entity.ChatMessage;
import com.bogo.boot.message.entity.GroupMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.entity.MessageIndex;
import com.bogo.boot.message.entity.SystemMessage;
import com.bogo.boot.message.repository.mapper.AppMessageMapper;
import com.bogo.boot.message.repository.mapper.GroupMessageMapper;
import com.bogo.boot.message.repository.mapper.SystemMessageMapper;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

@Component
public class MessageLookupRepositoryProxy {

    @Resource
    private MessageRepositoryProxy messageRepositoryProxy;

    @Resource
    private MessageGroupQueryRepository messageGroupQueryRepository;

    @Resource
    private MessageAppQueryRepository messageAppQueryRepository;

    @Resource
    private MessageChatQueryRepository messageChatQueryRepository;

    @Resource
    private MessageSystemQueryRepository messageSystemQueryRepository;

    public List<Message> findList(List<MessageIndex> indices) {

        if (CollectionUtils.isEmpty(indices)) {
            return Collections.emptyList();
        }

        long uid = indices.get(0).getUid();

        List<Message> messageList = new LinkedList<>();

        /* 查系统消息 */
        messageList.addAll(messageRepositoryProxy.findList(indices,MessageAction.ACTION_2));

        /* 查群消息 */
        messageList.addAll(messageRepositoryProxy.findList(indices,MessageAction.ACTION_3));

        /* 查机器人消息 */
        messageList.addAll(messageRepositoryProxy.findList(indices,MessageAction.ACTION_4));

        /* 查机公众号消息 */
        messageList.addAll(messageRepositoryProxy.findList(indices,MessageAction.ACTION_200));

        /* 查事件消息 */
        messageList.addAll(messageRepositoryProxy.findList(indices));

        /* 以上消息需要填充接收人为当前UID */
        messageList.forEach(message -> message.setReceiver(uid));

        /* 查单聊 */
        messageList.addAll(messageRepositoryProxy.findList(indices,MessageAction.ACTION_0));

        messageList.sort(Comparator.comparingLong(Message::getCreateTime));

        return messageList;
    }

    public Page<Message> findListOfGroup(long groupId , long fromId){

        Pageable pageable = PageRequest.of(0, Common.API_PAGE_SIZE, Sort.by(Sort.Order.desc("id")));

        Page<Message> page = messageGroupQueryRepository.findAll(new GroupSpecification(groupId,fromId),pageable);

        List<Message> mappedList = page.get().map(new GroupMessageMapper()::view).collect(Collectors.toList());

        return new PageImpl<>(mappedList,pageable,page.getTotalElements());


    }

    public Page<Message> findListOfApp(long appId , long fromId){

        Pageable pageable = PageRequest.of(0, Common.API_PAGE_SIZE, Sort.by(Sort.Order.desc("id")));

        Page<Message> page = messageAppQueryRepository.findAll(new AppSpecification(appId,fromId),pageable);

        List<Message> mappedList = page.get().map(new AppMessageMapper()::view).collect(Collectors.toList());

        return new PageImpl<>(mappedList,pageable,page.getTotalElements());

    }

    public Page<Message> findListOfSystem(long fromId){

        Pageable pageable = PageRequest.of(0, Common.API_PAGE_SIZE, Sort.by(Sort.Order.desc("id")));

        Page<Message> page = messageSystemQueryRepository.findAll(new SystemSpecification(fromId),pageable);

        List<Message> mappedList = page.get().map(new SystemMessageMapper()::view).collect(Collectors.toList());

        return new PageImpl<>(mappedList,pageable,page.getTotalElements());

    }


    public Page<Message> findListOfFriend(long uid ,long fid, long fromId){

        Pageable pageable = PageRequest.of(0, Common.API_PAGE_SIZE, Sort.by(Sort.Order.desc("id")));
        return messageChatQueryRepository.findAll(new P2pSpecification(uid,fid,fromId),pageable);

    }

    private static class GroupSpecification implements Specification<GroupMessage> {

        private final long groupId;

        private final long fromId;

        private GroupSpecification(long groupId,long fromId){
            this.fromId = fromId;
            this.groupId = groupId;
        }

        @Override
        public Predicate toPredicate(Root<GroupMessage> root, CriteriaQuery<?> query, CriteriaBuilder builder) {

            Predicate idPredicate = fromId <= 0 ?  builder.isNotNull(root.get("id")) :  builder.lt(root.get("id").as(Long.class), fromId);

            Predicate groupIdPredicate = builder.equal(root.get("groupId").as(Long.class),groupId);

            query.where(idPredicate,groupIdPredicate);
            return query.getRestriction();
        }
    }

    private static class P2pSpecification implements Specification<ChatMessage> {

        private final long uid;
        private final long fid;

        private final long fromId;

        private P2pSpecification(long uid,long fid,long fromId){
            this.fid = fid;
            this.uid = uid;
            this.fromId = fromId;
        }
        @Override
        public Predicate toPredicate(Root<ChatMessage> root, CriteriaQuery<?> query, CriteriaBuilder builder) {

            Predicate idPredicate = fromId <= 0 ?  builder.isNotNull(root.get("id")) :  builder.lt(root.get("id").as(Long.class), fromId);

            Predicate sendPredicate = builder.and(builder.equal(root.get("sender").as(Long.class), uid),builder.equal(root.get("receiver").as(Long.class), fid));
            Predicate recvPredicate = builder.and(builder.equal(root.get("receiver").as(Long.class), uid),builder.equal(root.get("sender").as(Long.class), fid));

            query.where(builder.and(idPredicate,builder.or(sendPredicate,recvPredicate)));

            return query.getRestriction();
        }
    }

    private static class SystemSpecification implements Specification<SystemMessage> {

        private final long fromId;

        private SystemSpecification(long fromId){
            this.fromId = fromId;
        }

        @Override
        public Predicate toPredicate(@NotNull Root<SystemMessage> root, CriteriaQuery<?> query, @NotNull CriteriaBuilder builder) {

            Predicate idPredicate = fromId <= 0 ?  builder.isNotNull(root.get("id")) :  builder.lt(root.get("id").as(Long.class), fromId);

            query.where(idPredicate);
            return query.getRestriction();
        }
    }

    private static class AppSpecification implements Specification<AppMessage> {

        private final long appId;

        private final long fromId;

        private AppSpecification(long appId,long fromId){
            this.fromId = fromId;
            this.appId = appId;
        }

        @Override
        public Predicate toPredicate(Root<AppMessage> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
            Predicate idPredicate = fromId <= 0 ?  builder.isNotNull(root.get("id")) :  builder.lt(root.get("id").as(Long.class), fromId);
            query.where(idPredicate, builder.equal(root.get("appId").as(Long.class), appId));
            return query.getRestriction();
        }
    }
}
