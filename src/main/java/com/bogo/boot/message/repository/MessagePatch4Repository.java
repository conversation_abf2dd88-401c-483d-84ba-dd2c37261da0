
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.MessageIndex;
import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MessagePatch4Repository extends MessagePatchRepository {

	@Modifying
	@Query("delete from MessagePatch4 where uid = :uid")
	void delete(long uid);

	@Modifying
	@Query("delete from MessagePatch4 where createTime < :beforeAt")
	void cleanup(long beforeAt);

	@Query("from MessagePatch4 where uid = :uid and mid > :maxMessageId order by id")
	List<MessageIndex> findList(long uid, long maxMessageId);

}
