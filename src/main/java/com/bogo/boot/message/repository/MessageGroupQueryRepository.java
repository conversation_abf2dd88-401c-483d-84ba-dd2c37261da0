
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.GroupMessage;
import com.bogo.boot.message.entity.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageGroupQueryRepository extends JpaRepository<GroupMessage, Long> {
	Page<Message> findAll(Specification<GroupMessage> sp, Pageable pageable);
}
