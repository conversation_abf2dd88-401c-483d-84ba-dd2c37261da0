
package com.bogo.boot.message.repository;

import com.bogo.boot.infra.configuration.properties.MessageProperties;
import com.bogo.boot.message.entity.MessageIndex;
import com.bogo.boot.message.entity.MessagePatch0;
import com.bogo.boot.message.entity.MessagePatch1;
import com.bogo.boot.message.entity.MessagePatch2;
import com.bogo.boot.message.entity.MessagePatch3;
import com.bogo.boot.message.entity.MessagePatch4;
import com.bogo.boot.message.entity.MessagePatch5;
import com.bogo.boot.message.entity.MessagePatch6;
import com.bogo.boot.message.entity.MessagePatch7;
import com.bogo.boot.message.entity.MessagePatch8;
import com.bogo.boot.message.entity.MessagePatch9;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 多端同步消息补偿 根据UID尾号分表实现
 */
@Component
public class MessagePatchRepositoryProxy {

	private final Map<Integer,MessagePatchRepository> messagePatchRepositoryMap = new HashMap<>();

	private final Map<Integer,Supplier<MessageIndex>> messagePatchEntityMap = new HashMap<>();

	@Resource
	private MessageProperties messageProperties;

	@Autowired
	public MessagePatchRepositoryProxy(MessagePatch0Repository messagePatch0Repository,
                                       MessagePatch1Repository messagePatch1Repository,
                                       MessagePatch2Repository messagePatch2Repository,
                                       MessagePatch3Repository messagePatch3Repository,
                                       MessagePatch4Repository messagePatch4Repository,
                                       MessagePatch5Repository messagePatch5Repository,
                                       MessagePatch6Repository messagePatch6Repository,
                                       MessagePatch7Repository messagePatch7Repository,
                                       MessagePatch8Repository messagePatch8Repository,
                                       MessagePatch9Repository messagePatch9Repository){

		messagePatchRepositoryMap.put(0,messagePatch0Repository);
		messagePatchRepositoryMap.put(1,messagePatch1Repository);
		messagePatchRepositoryMap.put(2,messagePatch2Repository);
		messagePatchRepositoryMap.put(3,messagePatch3Repository);
		messagePatchRepositoryMap.put(4,messagePatch4Repository);
		messagePatchRepositoryMap.put(5,messagePatch5Repository);
		messagePatchRepositoryMap.put(6,messagePatch6Repository);
		messagePatchRepositoryMap.put(7,messagePatch7Repository);
		messagePatchRepositoryMap.put(8,messagePatch8Repository);
		messagePatchRepositoryMap.put(9,messagePatch9Repository);

		messagePatchEntityMap.put(0, MessagePatch0::new);
		messagePatchEntityMap.put(1, MessagePatch1::new);
		messagePatchEntityMap.put(2, MessagePatch2::new);
		messagePatchEntityMap.put(3, MessagePatch3::new);
		messagePatchEntityMap.put(4, MessagePatch4::new);
		messagePatchEntityMap.put(5, MessagePatch5::new);
		messagePatchEntityMap.put(6, MessagePatch6::new);
		messagePatchEntityMap.put(7, MessagePatch7::new);
		messagePatchEntityMap.put(8, MessagePatch8::new);
		messagePatchEntityMap.put(9, MessagePatch9::new);

	}


	/*
	 * 转为对应的存储patch实体，存如对应的分表
	 */
	public void save(List<MessageIndex> indices) {

		/*
		 同步补偿策略被关闭不再写入
		 */
		if (!messageProperties.getPatch().isEnable()){
			return;
		}

		Map<Integer, List<MessageIndex>> indexGroup = indices.stream().collect(Collectors.groupingBy(messageIndex -> getIndexNumber(messageIndex.getUid())));
		for (Map.Entry<Integer, List<MessageIndex>> entry : indexGroup.entrySet()) {
			int index = entry.getKey();
			List<MessageIndex>  addIndices = entry.getValue().stream().map(new TableMapping(index)).toList();
			messagePatchRepositoryMap.get(index).saveAllAndFlush(addIndices);
		}
	}

	public List<MessageIndex> findList(long uid,long maxMessageId) {
		if (!messageProperties.getPatch().isEnable()){
			return Collections.emptyList();
		}
		return getMessagePatchRepository(uid).findList(uid,maxMessageId);
	}

	public void cleanup(long cleanBeforeAt) {
		messagePatchRepositoryMap.values().forEach(messagePatchRepository -> messagePatchRepository.cleanup(cleanBeforeAt));
	}


	/**
	 * 获取UID尾号分表
	 * @param uid 原始UID
	 * @return 分表索引
	 */
	private int getIndexNumber(long uid) {
		return (int) (uid % 10);
	}

	private MessagePatchRepository getMessagePatchRepository(long uid){
		return messagePatchRepositoryMap.get(getIndexNumber(uid));
	}



	/*
	 * 转为对应的存储index实体，存如对应的分表
	 */
	private class TableMapping implements Function<MessageIndex,MessageIndex>{

		private final Supplier<MessageIndex> supplier ;

		private TableMapping(int index) {
			this.supplier = messagePatchEntityMap.get(index);
        }

        @Override
		public MessageIndex apply(MessageIndex index) {
			MessageIndex copy = supplier.get();
			copy.setUid(index.getUid());
			copy.setAction(index.getAction());
			copy.setMid(index.getMid());
			copy.setSender(index.getSender());
			copy.setCreateTime(index.getCreateTime());
			return copy;
		}
	}

}
