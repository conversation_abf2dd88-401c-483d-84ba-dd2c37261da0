
package com.bogo.boot.message.repository;

import com.bogo.boot.message.entity.Message;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MessageGroupRepository extends MessageRepository{

	@Override
	@Query("from GroupMessage where id in (:ids) order by id")
	List<Message> findList(Long[] ids);

	@Modifying
	@Query("delete from GroupMessage where groupId = ?1")
	void deleteAll(long groupId);

	@Modifying
	@Query("delete from GroupMessage where groupId = ?1 and uid = ?2 and action = ?3")
	void deleteAll(long groupId,long uid,String action);

	@Modifying
	@Query("delete from GroupMessage where groupId = ?1 and uid in (?2) and action = ?3")
	void deleteAll(long groupId,Long[] uidList,String action);

	@Override
	@Query("from GroupMessage where id = :id")
	Message findOne(long id);

	@Modifying
	@Query("update GroupMessage set readCount = readCount + 1 where id in (:ids)")
	void addReadCount(Set<Long> ids);
}
