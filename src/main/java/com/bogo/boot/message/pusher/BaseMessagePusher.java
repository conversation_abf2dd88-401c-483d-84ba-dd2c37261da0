
package com.bogo.boot.message.pusher;

import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.model.PushMessage;
import com.bogo.boot.message.model.SendCondition;
import com.bogo.boot.message.redis.SignalRedisTemplate;
import com.bogo.boot.message.service.MessageIndexService;
import com.bogo.boot.message.service.impl.RedisMidGenerator;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

public abstract class BaseMessagePusher {

	@Resource
	protected MessageIndexService messageIndexService;

	@Resource
	protected SignalRedisTemplate signalRedisTemplate;

	@Resource
	private RedisMidGenerator midGenerator;
	/**
	 * 消息发送之前的预处理
	 * @param message
	 */
	protected void preHandle(Message message){}

	/**
	 * 向用户发送消息
	 * @param message
	 */
	public void push(@Nonnull Message message) {
		this.push(message,Collections.singletonList(message.getReceiver()));
	}

	public final void push(@Nonnull Message message,@Nonnull SendCondition condition) {
		this.push(message,Collections.singletonList(message.getReceiver()),condition);
	}

	public final void push(@Nonnull Message message, @Nonnull List<Long> idList) {
		this.push(message,idList,new SendCondition());
	}

	public final void push(@Nonnull Message message, @Nonnull List<Long> idList, @Nonnull SendCondition condition) {

		message.setId(midGenerator.nextId());

		if (idList.isEmpty()){
			return;
		}

		messageIndexService.add(message,idList);

		PushMessage pushMessage = new PushMessage();
		pushMessage.setReceivers(idList);
		pushMessage.setMessage(message);
		pushMessage.setSender( message.getSender().toString());
			pushMessage.setCondition(condition);

		/*
		 * 通过发送redis广播，到集群中的每台实例，获得当前UID绑定了连接并推送
		 * @see com.bogo.boot.message.listener.PushMessageListener
		 */

		pushMessage.setTraceId(UUID.randomUUID().toString());

		signalRedisTemplate.push(pushMessage);
	}
}
