
package com.bogo.boot.message.pusher;

import com.bogo.boot.group.bo.GroupBO;
import com.bogo.boot.group.constant.GroupState;
import com.bogo.boot.group.entity.Group;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.group.service.GroupMemberService;
import com.bogo.boot.group.service.GroupService;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.exception.ChatForbiddenException;
import com.bogo.boot.message.model.EventMessageCompat;
import com.bogo.boot.message.model.MessageTitle;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 推送群消息
 */
@Component
public class GroupMessagePusher extends DefaultMessagePusher {

    @Resource
    private GroupService groupService;

    @Resource
    private GroupMemberService groupMemberService;

    @Resource
    private Consumer<Message> blackwordPredicate;


    /*
     * 检查是否群被禁用
     * @param id
     * @param uid
     * @return
     */
    private boolean isGroupBlocked(Long id, Long uid) {

        GroupBO group = groupService.findOne(id,false);

        if (group == null) {
            return true;
        }

        boolean isFounder = Objects.equals(uid, group.getUid());

        return group.getState() == GroupState.BLOCK.getValue() && !isFounder;

    }

    /*
     检查是成员
     */
    private boolean isMember(Long id, Long uid) {
        return groupMemberService.isMember(id, uid);
    }


    @Override
    public void preHandle(Message message) {

        /*
         敏感词检查
         */
        blackwordPredicate.accept(message);

        Long uid = message.getSender();

        Long groupId = message.getReceiver();

        boolean isMemberMessage = MessageAction.ACTION_3.equals(message.getAction());

        if (isGroupBlocked(groupId, uid)) {
            throw new ChatForbiddenException("群已经被禁止发言");
        }

        if (isMemberMessage && !isMember(groupId, uid)) {
            throw new ChatForbiddenException("无法发送,你已经不是该群成员");
        }

        MessageTitle title = MessageTitle.of(message.getTitle());

        /* 推送的消息体中增加消息发送人name信息 */
        if (isMemberMessage){
            title.setName(groupMemberService.findName(groupId, uid));
        }

        message.setTitle(title.toString());

    }

    @Override
    public void push(@NotNull final Message message) {

        Long sender = message.getSender();

        Long groupId = message.getReceiver();

        message.setSender(groupId);

        message.setExtra(sender.toString());

        List<Long> idList = groupMemberService.findUidList(groupId);
        idList.remove(sender);

        super.push(message, idList);

        if (!Objects.equals(message.getAction(), MessageAction.ACTION_3)) {
            return;
        }

        /*
         * 给自己其他在线设备同步一条发送的群聊消息
         * 排除当前设备 origin.getAppChannel()
         */
        super.push(EventMessageCompat.from()
                .setId(message.getId())
                .setSender(groupId)
                .setReceiver(sender)
                .setContent(message.getContent())
                .setFormat(message.getFormat())
                .setTitle(message.getTitle())
                .setAction(MessageAction.ACTION_320)
                .setCreateTime(message.getCreateTime())
                .setIgnoreCurrentChannel(true));

    }


    @EventListener
    @Order(0)
    public void onGroupEvent(GroupActionEvent event) {

        Message message = event.getMessage();

        long groupId = message.getSender();

        if (CollectionUtils.isEmpty(event.getUidList())) {
            event.setUidList(groupMemberService.findUidList(groupId));
        }

        if (event.getMakerUid() != null) {
            MessageTitle title = MessageTitle.of(message.getTitle());
            title.setName(groupMemberService.findName(groupId, event.getMakerUid()));
            message.setTitle(title.toString());
        }


        super.push(message, event.getUidList());
    }

}
