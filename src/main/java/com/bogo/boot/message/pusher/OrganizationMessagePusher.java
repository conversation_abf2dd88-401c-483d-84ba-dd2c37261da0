
package com.bogo.boot.message.pusher;

import com.bogo.boot.contact.service.OrganizationService;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.repository.MessageEventRepository;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Objects;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

/**
 * 推送组织消息
 */
@Component
public class OrganizationMessagePusher extends BaseMessagePusher {

    @Resource
    private OrganizationService organizationService;

    @Resource
    private MessageEventRepository messageEventRepository;

    @Override
    public void push(@NotNull final Message message) {

        this.onNotifyChange(message);

        Long organizationId = message.getSender();

        if (message.getReceiver() == null){
            super.push(message,organizationService.findUidList(organizationId));
        }else {
            super.push(message, Collections.singletonList(message.getReceiver()));
        }

    }

    /*
    之前未接收的删除 组织全量更新事件只保留一个
     */
    private void onNotifyChange(Message message){
        if (Objects.equals(MessageAction.ACTION_407, message.getAction())){
            messageEventRepository.deleteAll(message.getSender(),Collections.singleton(message.getAction()));
        }
    }

}
