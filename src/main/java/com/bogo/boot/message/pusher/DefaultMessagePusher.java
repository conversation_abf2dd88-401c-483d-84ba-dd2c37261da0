
package com.bogo.boot.message.pusher;

import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.model.EventMessageCompat;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

/**
 * 推送系统消息
 */
@Component
public class DefaultMessagePusher extends BaseMessagePusher{

	@Override
	public void push(@NotNull Message message) {
		if (message.getSender() == null) {
			message.setSender(Common.SYSTEM_ID);
		}
		super.push(message);
	}


	public void push(@NotNull EventMessageCompat builder) {
		super.push(builder.getMessage(),builder.getSendCondition());
	}

}
