
package com.bogo.boot.message.pusher;

import com.bogo.boot.infra.constant.ChangeType;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.moment.constant.CommentType;
import com.bogo.boot.moment.entity.Comment;
import com.bogo.boot.moment.entity.Moment;
import com.bogo.boot.moment.event.CommentActionEvent;
import com.bogo.boot.moment.event.MomentActionEvent;
import com.bogo.boot.moment.predicate.VisiblePredicate;
import com.bogo.boot.moment.repository.CommentRepository;
import com.bogo.boot.moment.repository.MomentRepository;
import com.bogo.boot.moment.service.MomentVisibleService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 推送圈子消息 过滤屏蔽我的人以及被我屏蔽的人
 */
@Component
public class MomentMessagePusher extends BaseMessagePusher {

    @Resource
    private CommentRepository commentRepository;

    @Resource
    private MomentRepository momentRepository;

    @Resource
    private MomentVisibleService momentVisibleService;

    @Override
    public void push(@NotNull Message message) {

        long uid = Long.parseLong(message.getContent());

        super.push(message, momentVisibleService.findNotifiableList(uid));
    }

    @EventListener
    @Order(0)
    public void onMomentEvent(MomentActionEvent event){

        Moment moment = event.getSource();
        event.setSender(moment.getId());
        event.setContent(String.valueOf(moment.getUid()));

        List<Long> receivers = momentVisibleService.findNotifiableList(moment.getUid()).stream().filter(VisiblePredicate.ForUid.of(moment)).collect(Collectors.toList());

        if (event.getType() == ChangeType.ADD){
            event.setAction(MessageAction.ACTION_500);
        }

        if (event.getType() == ChangeType.DELETE){
            event.setAction(MessageAction.ACTION_501);
            // 删除需要通知发布者
            receivers.add(moment.getUid());
        }

        event.setUidList(receivers);

        super.push(event.getMessage(),event.getUidList());
    }

    @EventListener
    @Order(0)
    public void onCommentEvent(CommentActionEvent event){

        Comment comment = event.getSource();

        Moment moment = momentRepository.findById(comment.getMomentId()).orElse(null);

        if (moment == null){
            return;
        }

        List<Long> idList = momentVisibleService.findVisibleList(moment.getUid(),comment.getUid()).stream().filter(VisiblePredicate.ForUid.of(moment)).collect(Collectors.toList());
        idList.add(moment.getUid());
        idList.remove(comment.getUid());

        if (event.getType() == ChangeType.ADD){
            notifyAddComment(event,moment.getUid(),idList);
        }

        if (event.getType() == ChangeType.DELETE){
            notifyDeleteComment(event,moment.getUid(),idList);
        }
    }

    /**
     * 有点赞时 | 有新的评论时 发送提醒消息
     */
    private void notifyAddComment(CommentActionEvent event, long momentUid, List<Long>  idList) {

        Comment comment = event.getSource();

        event.setSender(comment.getMomentId());
        event.setContent(String.valueOf(comment.getId()));
        event.setAction(MessageAction.ACTION_502);
        event.setExtra(String.valueOf(momentUid));

        /*
         * 如果回复别人的评论，传回被评论人ID，用于提醒
         */
        if (Objects.equals(comment.getType(), CommentType.COMMENT.getValue()) && comment.getParentId() != null) {
            Comment parent = commentRepository.findById(comment.getParentId()).orElse(null);
            event.setTitle(parent == null ? null : String.valueOf(parent.getUid()));
        }

        super.push(event.getMessage(),idList);

        /*
         *删除未接收的评论消息
         *@see EventMessageRemover.onCommentEvent
         */
        event.setUidList(idList);
    }


    /**
     * 评论被删除或者被取消点赞，只通知文章作者
     */
    private void notifyDeleteComment(CommentActionEvent event, long momentUid, List<Long>  idList) {

        Comment comment = event.getSource();

        /*
        评论ID
         */
        long id = comment.getId();

        /*
        朋友圈内容ID
         */
        long momentId = comment.getMomentId();

        event.setSender(momentId);
        event.setContent(String.valueOf(id));
        event.setAction(MessageAction.ACTION_503);
        event.setExtra(String.valueOf(momentUid));

        super.push(event.getMessage(),idList);

        /*
         *删除未接收的评论消息
         *@see EventMessageRemover.onCommentEvent
         */
        event.setUidList(idList);

    }

}
