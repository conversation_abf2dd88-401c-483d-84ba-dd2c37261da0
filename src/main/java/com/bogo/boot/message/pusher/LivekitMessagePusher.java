
package com.bogo.boot.message.pusher;

import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.model.EventMessageCompat;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

/**
 * livekit事件推送
 */
@Component
public class LivekitMessagePusher extends DefaultMessagePusher{

    /*
     同意、拒绝事件，同步到其他设备的消息action映射
     */
    private static final Map<String,String> actionMap = new HashMap<>();

    static {
        actionMap.put(MessageAction.ACTION_902,MessageAction.ACTION_928);
        actionMap.put(MessageAction.ACTION_903,MessageAction.ACTION_929);
    }


    /*
     推送多人会议事件
     */
    public final void push(Message message, Set<Long> idSet) {

        if (CollectionUtils.isEmpty(idSet)){
            return;
        }
        this.push(message,new LinkedList<>(idSet));
    }

    /*
    推送多人会议事件
    */
    public final void push(Message message, Map<Long,String> members) {
        this.push(message,members.keySet());
    }


    /**
     * 单人通话信令推送
     * @param message
     */
    @Override
    public void push(@NotNull Message message) {

        /*
         * 先给自己其他在线设备同步已接听或者挂断事件
         */
        String eventAction = actionMap.get(message.getAction());

        if (eventAction != null){
            super.push(EventMessageCompat.from()
                    .setId(message.getId())
                    .setReceiver(message.getSender())
                    .setContent(message.getReceiver().toString())
                    .setAction(eventAction)
                    .setIgnoreCurrentChannel(true));
        }

        super.push(message);
    }


}
