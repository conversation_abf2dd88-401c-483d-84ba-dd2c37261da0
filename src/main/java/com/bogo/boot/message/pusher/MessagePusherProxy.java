
package com.bogo.boot.message.pusher;

import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.exception.ChatForbiddenException;
import jakarta.annotation.Resource;
import java.util.HashMap;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
public class MessagePusherProxy implements ApplicationListener<ApplicationStartedEvent> {

	@Resource
	private ApplicationContext applicationContext;

	@Resource
	private DefaultMessagePusher defaultMessagePusher;

	private final HashMap<String,BaseMessagePusher> pusherMap = new HashMap<>();

	public ResponseEntity<Long> push(Message message) {

		BaseMessagePusher pusher = getMessagePusher(message.getAction());

		try {
			pusher.preHandle(message);

			pusher.push(message);

		}catch (ChatForbiddenException exception){
			return ResponseEntity.make(HttpStatus.FORBIDDEN,exception.getMessage());
		}

		ResponseEntity<Long> response = new ResponseEntity<>();

		response.setData(message.getId());

		response.setTimestamp(message.getCreateTime());

		return response;
	}



	private BaseMessagePusher getMessagePusher(String action) {

		BaseMessagePusher messagePusher = pusherMap.get(action);

		return messagePusher == null ? defaultMessagePusher : messagePusher;
	}

	@Override
	public void onApplicationEvent(@NotNull ApplicationStartedEvent startedEvent) {

		pusherMap.put(MessageAction.ACTION_0,applicationContext.getBean(FriendMessagePusher.class));

		pusherMap.put(MessageAction.ACTION_3, applicationContext.getBean(GroupMessagePusher.class));

		pusherMap.put(MessageAction.ACTION_4, applicationContext.getBean(GroupMessagePusher.class));

		pusherMap.put(MessageAction.ACTION_200, applicationContext.getBean(MicroServerMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_202, applicationContext.getBean(MicroServerMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_203, applicationContext.getBean(MicroServerMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_204, applicationContext.getBean(MicroServerMessagePusher.class));

		pusherMap.put(MessageAction.ACTION_400, applicationContext.getBean(OrganizationMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_401, applicationContext.getBean(OrganizationMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_402, applicationContext.getBean(OrganizationMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_404, applicationContext.getBean(OrganizationMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_405, applicationContext.getBean(OrganizationMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_406, applicationContext.getBean(OrganizationMessagePusher.class));
		pusherMap.put(MessageAction.ACTION_407, applicationContext.getBean(OrganizationMessagePusher.class));
	}
}
