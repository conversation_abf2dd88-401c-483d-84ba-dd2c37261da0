
package com.bogo.boot.message.pusher;

import com.bogo.boot.account.repository.SessionRepository;
import com.bogo.boot.account.repository.UserRepository;
import com.bogo.boot.message.entity.Message;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 推送批量用户消息
 */
@Component
public class BroadcastMessagePusher extends BaseMessagePusher {

	@Resource
	private UserRepository userRepository;

	@Resource
	private SessionRepository sessionRepository;

	/**
	 * 推送全部用户
	 */
	@Override
	@Async(value = "pushTaskExecutor")
	public void push(@NotNull Message message) {
		super.push(message,userRepository.findIdList());
	}

	/**
	 * 推送全部在线用户
	 */
	@Async(value = "pushTaskExecutor")
	public void pushOnline(Message message) {
		super.push(message,sessionRepository.findUidList());
	}
}
