
package com.bogo.boot.message.pusher;

import com.bogo.boot.app.event.AppActionEvent;
import com.bogo.boot.app.service.SubscriberService;
import com.bogo.boot.message.entity.Message;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 公众号向所有订阅用户发消息
 */
@Component
public class MicroServerMessagePusher extends BaseMessagePusher {

	@Resource
	private SubscriberService subscriberService;

	@Override
	public void push(@NotNull final Message message) {

		if (message.getReceiver() != null){
			/*
			 *公众号给某个订阅用户发消息
			 */
			super.push(message);
		}else {
			/*
			 *公众号给所有订阅用户发消息
			 */
			super.push(message,subscriberService.findUidList(message.getSender()));
		}

	}

	@EventListener
	public void onAppEvent(AppActionEvent event){
		this.push(event.getMessage());
	}
}
