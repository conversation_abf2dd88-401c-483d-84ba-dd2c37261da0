
package com.bogo.boot.message.pusher;

import com.bogo.boot.contact.event.FriendActionEvent;
import com.bogo.boot.contact.service.EventNotifiableService;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.exception.ChatForbiddenException;
import com.bogo.boot.message.model.EventMessageCompat;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.function.Consumer;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 好友聊天消息推送
 */
@Component
public class FriendMessagePusher extends DefaultMessagePusher {

    @Resource
    private EventNotifiableService eventNotifiableService;

    @Resource
    private Consumer<Message> blackwordPredicate;

    @Resource
    private BiPredicate<Long,Long> friendshipPredicate;

    @Override
    public void preHandle(Message message) {

        if (!friendshipPredicate.test(message.getReceiver(), message.getSender())) {
            throw new ChatForbiddenException("发送失败，你已不是对方好友");
        }

        blackwordPredicate.accept(message);

    }


    @Override
    public void push(@NotNull Message message) {
        super.push(message);

        if (!Objects.equals(message.getAction(),MessageAction.ACTION_0)){
            return;
        }

        /*
         * 给自己其他在线设备同步一条发送的单聊消息
         * 排除当前设备 currentChannel
         */

        super.push(EventMessageCompat.from()
                .setId(message.getId())
                .setSender(message.getSender())
                .setReceiver(message.getSender())
                .setContent(message.getContent())
                .setFormat(message.getFormat())
                .setCreateTime(message.getCreateTime())
                .setExtra(message.getReceiver().toString())
                .setAction(MessageAction.ACTION_109)
                .setIgnoreCurrentChannel(true));
    }


    @EventListener
    @Order(0)
    public void onFriendEvent(FriendActionEvent event){

        Message message = event.getMessage();

        if (message.getReceiver() != null) {
            this.push(message);
            return;
        }

        List<Long> uidList = eventNotifiableService.findNotifiableList(message.getSender());

        event.setReceivers(uidList);

        super.push(message,uidList);

    }

}
