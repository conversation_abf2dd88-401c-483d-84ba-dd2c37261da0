package com.bogo.boot.message.model;

import com.bogo.boot.infra.util.JSON;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class MessageTitle {

    // 引用的消息ID
    private Long qt;

    // 消息发送人名称
    private String name;

    // 是否AT所有人
    private Boolean atAll;

    // 被AT的用户ID集合
    private Set<Long> at;

    // 被AT的机器人ID集合
    private Set<Long> robotAt ;

    public static MessageTitle of(String title){
        if (StringUtils.isEmpty(title)){
            return new MessageTitle();
        }

        return JSON.parse(title,MessageTitle.class);
    }

    @Override
    public String toString(){
        return JSON.toJSONString(this);
    }
}
