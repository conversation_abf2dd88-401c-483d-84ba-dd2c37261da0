package com.bogo.boot.message.model;


import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.holder.ClientOriginHolder;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.entity.EventMessage;
import lombok.Getter;

public class EventMessageCompat {

    @Getter
    private final EventMessage message = new EventMessage();

    private EventMessageCompat(){
        message.setSender(Common.SYSTEM_ID);
        message.setFormat(MessageFormat.TEXT);
    }
    /*
     * 是否忽略当前请求客户端
     */
    private boolean ignoreCurrentChannel;

    public static EventMessageCompat from(){
        return new EventMessageCompat();
    }

    public EventMessageCompat setAction(String action) {
        message.setAction(action);
        return this;
    }

    public EventMessageCompat setSender(Long sender) {
        message.setSender(sender);
        return this;
    }

    public EventMessageCompat setReceiver(Long receiver) {
        message.setReceiver(receiver);
        return this;
    }

    public EventMessageCompat setContent(String content) {
        message.setContent(content);
        return this;
    }

    public EventMessageCompat setTitle(String title) {
        message.setTitle(title);
        return this;
    }

    public EventMessageCompat setExtra(String extra) {
        message.setExtra(extra);
        return this;
    }

    public EventMessageCompat setId(Long id) {
        message.setId(id);
        return this;
    }

    public EventMessageCompat setFormat(Byte format) {
        message.setFormat(format);
        return this;
    }

    public EventMessageCompat setCreateTime(Long createTime) {
        message.setCreateTime(createTime);
        return this;
    }

    public EventMessageCompat setIgnoreCurrentChannel(boolean ignoreCurrentChannel) {
        this.ignoreCurrentChannel = ignoreCurrentChannel;
        return this;
    }

    public SendCondition getSendCondition() {
        SendCondition sendCondition = new SendCondition();
        if (ignoreCurrentChannel){
            sendCondition.addExcludedChannel(ClientOriginHolder.getAppChannel());
        }
        return sendCondition;
    }
}
