package com.bogo.boot.message.model;


import java.util.LinkedList;
import java.util.List;
import lombok.Getter;

@Getter
public class SendCondition {

    /*
     * 忽略的设备类型，不给这些终端推送消息
     */
    private final List<String> excludedChannels = new LinkedList<>();

    public void addExcludedChannel(String channel) {
        if (channel == null){
            return;
        }
        excludedChannels.add(channel);
    }

}
