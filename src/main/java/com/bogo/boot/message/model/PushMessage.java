package com.bogo.boot.message.model;


import com.bogo.boot.message.predicate.ExcludeChannelPredicate;
import com.bogo.messaging.model.Message;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.netty.channel.Channel;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PushMessage {
    /*
     * 请求ID
     */
    private String traceId;

    /*
     * 接收消息的UID列表
     */
    private List<Long> receivers;

    /*
     * 消息发送条件
     */
    private SendCondition condition;

    private long id;
    private String action;
    private String title;
    private String content;
    private String sender;
    private String format;
    private String extra;
    private long timestamp;

    public void setMessage(com.bogo.boot.message.entity.Message message) {
        this.id = message.getId();
        this.action = message.getAction();
        this.title = message.getTitle();
        this.content = message.getContent();
        this.sender = message.getSender().toString();
        this.format = message.getFormat().toString();
        this.extra = message.getExtra();
        this.timestamp = message.getCreateTime();
    }



    /**
     * 返回构建好的 待发送消息列表
     * @return 消息列表
     */
    @JsonIgnore
    public List<Message> getMessageList(){
        return receivers.stream().map(uid -> {
            Message message = new Message();
            message.setId(this.id);
            message.setSender(this.sender);
            message.setReceiver(uid.toString());
            message.setAction(this.action);
            message.setContent(this.content);
            message.setExtra(this.extra);
            message.setTitle(this.title);
            message.setFormat(this.format);
            message.setTimestamp(this.timestamp);
            return message;
        }).collect(Collectors.toList());
    }

    /**
     * 返回消息条件，满足条件才写入消息
     * @return
     */
    @JsonIgnore
    public Predicate<Channel> getPredicate(){
        return ExcludeChannelPredicate.of(condition);
    }
}
