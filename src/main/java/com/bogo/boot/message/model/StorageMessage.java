package com.bogo.boot.message.model;


import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.AppMessage;
import com.bogo.boot.message.entity.ChatMessage;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.GroupMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.entity.SystemMessage;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StorageMessage {

    /*
     需要根据消息action反解析出对应类型的消息
     */
    private static final Map<String, Supplier<Message>> CREATOR_MAP = new HashMap<>();
    static {
        CREATOR_MAP.put(MessageAction.ACTION_0, ChatMessage::new);
        CREATOR_MAP.put(MessageAction.ACTION_2, SystemMessage::new);
        CREATOR_MAP.put(MessageAction.ACTION_3, GroupMessage::new);
        CREATOR_MAP.put(MessageAction.ACTION_4,  GroupMessage::new);
        CREATOR_MAP.put(MessageAction.ACTION_200, AppMessage::new);
    }

    private Long id;
    private String action;
    private String title;
    private String content;
    private Long sender;

    // 多人接收情况下可能为null
    private Long receiver;

    private Byte format;
    private Byte state;
    private String extra;
    private Long timestamp;

    // 接收消息的UID列表
    private List<Long> receivers;

    @JsonIgnore
    private transient Message message;

    public void removeUidList(Collection<Long> removeble) {
        receivers.removeAll(removeble);
    }

    public void setMessage(Message message) {
        this.message = message;
        this.id = message.getId();
        this.action = message.getAction();
        this.title = message.getTitle();
        this.content = message.getContent();
        this.sender = message.getSender();
        this.receiver = message.getReceiver();
        this.format = message.getFormat();
        this.extra = message.getExtra();
        this.state = message.getState();
        this.timestamp = message.getCreateTime();
    }

    public Message getMessage() {

        if (message != null){
            return message;
        }

        message = CREATOR_MAP.getOrDefault(action, EventMessage::new).get();

        message.setId(id);
        message.setSender(sender);
        message.setReceiver(receiver);
        message.setAction(action);
        message.setTitle(title);
        message.setContent(content);
        message.setFormat(format);
        message.setExtra(extra);
        message.setState(state);
        message.setCreateTime(timestamp);

        return message;

    }

}
