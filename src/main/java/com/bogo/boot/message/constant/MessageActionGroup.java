
package com.bogo.boot.message.constant;


import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

public class MessageActionGroup {

    private MessageActionGroup(){

    }

    public static final List<String> TRANSIENT = new LinkedList<>();

    static {
        TRANSIENT.add(MessageAction.ACTION_999);
        TRANSIENT.add(MessageAction.ACTION_902);
        TRANSIENT.add(MessageAction.ACTION_903);
        TRANSIENT.add(MessageAction.ACTION_904);
        TRANSIENT.add(MessageAction.ACTION_905);
        TRANSIENT.add(MessageAction.ACTION_906);

        TRANSIENT.add(MessageAction.ACTION_928);
        TRANSIENT.add(MessageAction.ACTION_929);
        TRANSIENT.add(MessageAction.ACTION_932);

        TRANSIENT.add(MessageAction.ACTION_603);
        TRANSIENT.add(MessageAction.ACTION_604);
        TRANSIENT.add(MessageAction.ACTION_108);
        TRANSIENT.add(MessageAction.ACTION_109);
        TRANSIENT.add(MessageAction.ACTION_320);
        TRANSIENT.add(MessageAction.ACTION_321);
        TRANSIENT.add(MessageAction.ACTION_322);
        TRANSIENT.add(MessageAction.ACTION_114);

    }


    public static final List<String> PUSHABLE = new LinkedList<>();
    static {
        PUSHABLE.add(MessageAction.ACTION_0);
        PUSHABLE.add(MessageAction.ACTION_3);
        PUSHABLE.add(MessageAction.ACTION_4);
        PUSHABLE.add(MessageAction.ACTION_2);
        PUSHABLE.add(MessageAction.ACTION_200);
        PUSHABLE.add(MessageAction.ACTION_900);
        PUSHABLE.add(MessageAction.ACTION_600);
        PUSHABLE.add(MessageAction.ACTION_601);
    }


    public static final Set<String> GROUP_EVENT = new HashSet<>();

    static {
        GROUP_EVENT.add(MessageAction.ACTION_300);
        GROUP_EVENT.add(MessageAction.ACTION_301);
        GROUP_EVENT.add(MessageAction.ACTION_302);
        GROUP_EVENT.add(MessageAction.ACTION_304);
        GROUP_EVENT.add(MessageAction.ACTION_305);
        GROUP_EVENT.add(MessageAction.ACTION_306);
        GROUP_EVENT.add(MessageAction.ACTION_307);
        GROUP_EVENT.add(MessageAction.ACTION_308);
        GROUP_EVENT.add(MessageAction.ACTION_309);
        GROUP_EVENT.add(MessageAction.ACTION_310);
        GROUP_EVENT.add(MessageAction.ACTION_311);
        GROUP_EVENT.add(MessageAction.ACTION_312);
        GROUP_EVENT.add(MessageAction.ACTION_313);
        GROUP_EVENT.add(MessageAction.ACTION_314);
        GROUP_EVENT.add(MessageAction.ACTION_315);
        GROUP_EVENT.add(MessageAction.ACTION_316);
        GROUP_EVENT.add(MessageAction.ACTION_317);
        GROUP_EVENT.add(MessageAction.ACTION_318);
        GROUP_EVENT.add(MessageAction.ACTION_319);
    }

    public static final Set<String> MOMENT_EVENT = new HashSet<>();

    static {
        MOMENT_EVENT.add(MessageAction.ACTION_500);
        MOMENT_EVENT.add(MessageAction.ACTION_501);
        MOMENT_EVENT.add(MessageAction.ACTION_502);
        MOMENT_EVENT.add(MessageAction.ACTION_503);
    }

    public static final List<String> CHATTING = new LinkedList<>();
    static {
        CHATTING.add(MessageAction.ACTION_0);
        CHATTING.add(MessageAction.ACTION_3);
    }
}
