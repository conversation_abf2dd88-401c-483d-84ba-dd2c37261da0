
package com.bogo.boot.message.constant;

public interface MessageAction {

    /****************************************************
     *            1位长度为聊天相关消息                   *
     ****************************************************/
    String ACTION_0 = "0";  // 用户之间的普通消息
    String ACTION_2 = "2";  // 系统向用户发送的普通消息
    String ACTION_3 = "3";  // 群用户发的消息
    String ACTION_4 = "4";  // 群机器人消息

    /****************************************************
     *            1开头好友相关消息                       *
     ****************************************************/
    String ACTION_100 = "100";  // 消息被阅读
    String ACTION_101 = "101";  // 消息被撤回
    String ACTION_102 = "102";  // 好友替换了头像
    String ACTION_103 = "103";  // 好友修改了名称
    String ACTION_104 = "104";  // 好友修改了签名
    String ACTION_105 = "105";  // 请求添加好友
    String ACTION_106 = "106";  // 同意添加好友
    String ACTION_107 = "107";  // 被删除好友
    String ACTION_108 = "108";  // 聊天窗口对方正在输入
    String ACTION_109 = "109";  // 同步自己发送的单人消息
    String ACTION_110 = "110";  // 好友修改了邮箱地址
    String ACTION_111 = "111";  // 好友修改了性别
    String ACTION_112 = "112";  // 自己信息被修改
    String ACTION_113 = "113";  // 好友修改了背景
    String ACTION_114 = "114";  // 自己在其他端阅读了单人消息
    String ACTION_115 = "115";  // 新增笔记
    String ACTION_116 = "116";  // 修改笔记
    String ACTION_117 = "117";  // 删除笔记

    /****************************************************
     *            2开头统一为公众号消息                   *
     ****************************************************/
    String ACTION_200 = "200";  //公众号向用户发消息
    String ACTION_202 = "202";  // 公众号信息更新
    String ACTION_203 = "203";  // 公众号菜单信息更新
    String ACTION_204 = "204";  // 公众号Logo更新

    /****************************************************
     *            3开头统一为群相关消息                   *
     ****************************************************/
    String ACTION_300 = "300";  // 自己被拉进群
    String ACTION_301 = "301";  // 自己被移出群
    String ACTION_302 = "302";  // 退出了群
    String ACTION_303 = "303";  // 群解散
    String ACTION_304 = "304";  // 群名称被修改
    String ACTION_305 = "305";  // 群公告被修改
    String ACTION_306 = "306";  // 群logo被修改
    String ACTION_307 = "307";  // 群主变更
    String ACTION_308 = "308";  // 群被禁言
    String ACTION_309 = "309";  // 群解除禁言
    String ACTION_310 = "310";  // 群消息被撤回
    String ACTION_311 = "311";  // 别人被拉进群
    String ACTION_312 = "312";  // 别人被移出群
    String ACTION_313 = "313";  // 创建群机器人
    String ACTION_314 = "314";  // 群机器人信息更新
    String ACTION_315 = "315";  // 群机器LOGO更新
    String ACTION_316 = "316";  // 删除群机器人
    String ACTION_317 = "317";  // 启用/禁用群机器人
    String ACTION_318 = "318";  // 成员修改了自己的群昵称
    String ACTION_319 = "319";  // 机器人被转让给群主
    String ACTION_320 = "320";  // 同步自己发送的群消息
    String ACTION_321 = "321";  // 群消息被阅读
    String ACTION_322 = "322";  // 自己在其他端阅读了群消息

    /****************************************************
     *             4开头统一为组织相关消息                 *
     ****************************************************/
    String ACTION_400 = "400";  // 被加入组织
    String ACTION_401 = "401";  // 被移出组织
    String ACTION_402 = "402";  // 修改组织名称
    String ACTION_403 = "403";  // 自己被移出组织
    String ACTION_404 = "404";  // 部门成员变动
    String ACTION_405 = "405";  // 部门信息修改
    String ACTION_406 = "406";  // 部门被删除
    String ACTION_407 = "407";  // 组织数据更新

    /***************************************************
     *             5开头统一为圈子相关消息                 *
     ****************************************************/
    String ACTION_500 = "500";  // 好友发布新动态
    String ACTION_501 = "501";  // 好友删除动态
    String ACTION_502 = "502";  // 动态被评论或者点赞
    String ACTION_503 = "503";  // 动态删除评论或取消点赞
    String ACTION_504 = "504";  // 修改了朋友圈背景图

    /***************************************************
     *             6开头统一为多人会议通知                 *
     ****************************************************/
    String ACTION_600 = "600";  // 立即入会邀请
    String ACTION_601 = "601";  // 预约入会邀请
    String ACTION_603 = "603";  // 会议在其他设备接听
    String ACTION_604 = "604";  // 会议在其他设备拒接

    /***************************************************
     *             9开头统一般为瞬时消息，不存储            *
     ****************************************************/
    String ACTION_999 = "999";  // 强制下线
    String ACTION_900 = "900";  // 语音通话请求
    String ACTION_902 = "902";  // 通话接受
    String ACTION_903 = "903";  // 通话拒绝
    String ACTION_904 = "904";  // 对方正忙
    String ACTION_905 = "905";  // 结束通话
    String ACTION_906 = "906";  // 取消呼叫
    String ACTION_928 = "928";  // 单人通话在其他设备接听
    String ACTION_929 = "929";  // 单人在其他设备接听拒接
    String ACTION_932 = "932";  // 单人通话，对方已经响铃

}
