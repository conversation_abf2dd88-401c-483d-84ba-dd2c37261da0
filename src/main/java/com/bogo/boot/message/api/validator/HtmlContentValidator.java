package com.bogo.boot.message.api.validator;

import com.bogo.boot.infra.configuration.properties.MessageProperties;
import com.bogo.boot.message.annotation.BanHtml;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;
import org.springframework.stereotype.Component;

@Component
public class HtmlContentValidator implements ConstraintValidator<BanHtml, ContentFormatRequest> {

    private final Pattern pattern = Pattern.compile("<[^>]*>");

    @Resource
    private MessageProperties messageProperties;

    @Override
    public boolean isValid(ContentFormatRequest request, ConstraintValidatorContext context) {

        if (messageProperties.getHtml().isAllowed()
                || request.getFormat() != MessageFormat.TEXT){
            return true;
        }

        return !pattern.matcher(request.getContent()).find();
    }
}
