
package com.bogo.boot.message.api.vo;

import com.bogo.boot.account.entity.SilentNotification;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 消息免提醒
 */

@Schema(title = "免打扰信息")
@Getter
@Setter
public class SilentNotificationVO {

	@Schema(title = "目标ID")
	private Long targetId;

	@Schema(title = "目标类型 1:联系人 2:群 3:系统 4:公众号")
	private Byte type;

	public static SilentNotificationVO of(SilentNotification reminder){
		SilentNotificationVO vo = new SilentNotificationVO();
		vo.setType(reminder.getType());
		vo.setTargetId(reminder.getTargetId());
		return vo;
	}
}
