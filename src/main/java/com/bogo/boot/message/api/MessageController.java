
package com.bogo.boot.message.api;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.annotation.UpdateAction;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.api.request.GroupMessageRequest;
import com.bogo.boot.message.api.request.GroupReadRequest;
import com.bogo.boot.message.api.request.MessageForwardRequest;
import com.bogo.boot.message.api.request.MessageRequest;
import com.bogo.boot.message.api.request.ReadRequest;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageActionGroup;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.event.ForwardMessageEvent;
import com.bogo.boot.message.pusher.MessagePusherProxy;
import com.bogo.boot.message.service.MessageIndexService;
import com.bogo.boot.message.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/message")
@Tag(name = "消息相关接口" )
public class MessageController {

	@Resource
	private MessagePusherProxy messagePusherProxy;

	@Resource
	private MessageService messageService;

	@Resource
	private MessageIndexService messageIndexService;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Operation( summary = "发送单人消息")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "403" ,description = "不是好友")
	})
	@PostMapping(value = "")
	public ResponseEntity<Long> send(@Parameter(hidden = true) @UID Long uid , @Validated(CreateAction.class) @RequestBody MessageRequest request) {

		Message message = request.ofMessage();

		message.setSender(uid);

		return messagePusherProxy.push(message);
	}

	@Operation( summary = "发送群聊消息")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200" ,description = "OK"),
			@ApiResponse(responseCode = "403" ,description = "群聊被禁言")})
	@PostMapping(value = "/group")
	public ResponseEntity<Long> sendGroup(@Parameter(hidden = true) @UID Long uid ,@Validated(CreateAction.class) @RequestBody GroupMessageRequest request) {

		Message message = request.ofMessage();
		message.setSender(uid);

		return messagePusherProxy.push(message);
	}


	@Operation( summary = "阅读私聊消息")
	@PostMapping(value = "/read")
	public ResponseEntity<Void> read(@Validated(UpdateAction.class) @RequestBody ReadRequest request) {

		messageService.read(request);

		return ResponseEntity.ok();
	}

	@Operation( summary = "阅读群聊消息")
	@PostMapping(value = "/read/group")
	public ResponseEntity<Void> read(@Validated(UpdateAction.class) @RequestBody GroupReadRequest request) {

		messageService.read(request);

		return ResponseEntity.ok();
	}

	@Operation( summary = "设置已接收状态")
	@Parameters({
			@Parameter(name = "id", description = "消息ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "action", description = "消息类型", in = ParameterIn.QUERY,example = "0")
	})
	@PostMapping(value = "/receive/{id}")
	public ResponseEntity<Void> receive(@Parameter(hidden = true) @UID Long uid ,@PathVariable Long id ,@RequestParam String action) {
		/*
		 * 瞬时消息不用处理接收回执
		 */
		if (MessageActionGroup.TRANSIENT.contains(action)){
			return ResponseEntity.ok();
		}

		messageIndexService.receive(uid,id,action);

		return ResponseEntity.ok();
	}


	@Operation( summary = "设置所有消息已接收状态")
	@PostMapping(value = "/receive/all")
	public ResponseEntity<Void> receiveAll(@Parameter(hidden = true) @UID Long uid) {
		messageIndexService.delete(uid);
		return ResponseEntity.ok();
	}

	@Operation( summary = "转发消息")
	@PostMapping(value = "/forward")
	public ResponseEntity<List<MessageForwardRequest.MessageReceiver>> forward(@Parameter(hidden = true) @UID Long uid,@Validated(CreateAction.class) @RequestBody MessageForwardRequest request) {

		List<MessageForwardRequest.MessageReceiver> receivers = request.getReceiver();

		for (MessageForwardRequest.MessageReceiver receiver : receivers){

			Message message = request.ofMessage(receiver.getAction());

			message.setSender(uid);

			message.setReceiver(receiver.getId());

			messagePusherProxy.push(message);

			applicationEventProducer.publishAsync(new ForwardMessageEvent(message));

			receiver.setMid(message.getId());
			receiver.setTimestamp(System.currentTimeMillis());
		}

		return ResponseEntity.ok(receivers);
	}

	@Operation( summary = "撤回消息")
	@Parameters({
			@Parameter(name = "id", description = "消息ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "action", description = "消息类型", in = ParameterIn.QUERY,example = "0")
	})
	@DeleteMapping(value = "/revoke/{id}")
	public ResponseEntity<Void> revoke(@Parameter(hidden = true) @UID Long uid,@PathVariable Long id ,@RequestParam String action) {

		messageIndexService.revoke(uid,id,action);

		return ResponseEntity.ok();
	}

	@Operation( summary = "发送正在输入事件",description = "对方聊天窗口显示正在输入中....")
	@Parameter(name = "uid", description = "好友UID", in = ParameterIn.QUERY,example = "0")
	@PostMapping(value = "/typing")
	public ResponseEntity<Void> typing(@Parameter(hidden = true) @UID Long selfId ,@RequestParam Long uid) {

		Message message = new EventMessage();
		message.setSender(selfId);
		message.setReceiver(uid);
		message.setAction(MessageAction.ACTION_108);

		messagePusherProxy.push(message);

		return ResponseEntity.ok();
	}
}
