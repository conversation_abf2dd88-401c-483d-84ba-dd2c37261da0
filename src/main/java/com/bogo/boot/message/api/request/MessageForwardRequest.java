
package com.bogo.boot.message.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.constant.MessageState;
import com.bogo.boot.message.entity.ChatMessage;
import com.bogo.boot.message.entity.GroupMessage;
import com.bogo.boot.message.entity.Message;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

@Schema
@Getter
@Setter
public class MessageForwardRequest implements Serializable {

	@Schema(title = "原消息content")
	@NotBlank(message = "content不能空",groups = CreateAction.class)
	private String content;

	@Schema(title = "原消息format")
	private Byte format = MessageFormat.TEXT;

	@Schema(title = "标题")
	private String title;

	@Schema(title = "接收者集合({id:action})")
	@Size(min = 1, message = "receiver不能空", groups = CreateAction.class)
	private List<MessageReceiver> receiver;

	public Message ofMessage(String action){
		Message message = create(action);
		message.setAction(action);
		message.setContent(content);
		message.setTitle(title);
		message.setState(MessageState.STATE_NOT_RECEIVED);
		message.setFormat(format);
		message.setCreateTime(new Date().getTime());
		return message;
	}

	private Message create(String action){

		if (Objects.equals(action, MessageAction.ACTION_0)){
			return new ChatMessage();
		}
		return new GroupMessage();
	}

	@Schema
	@Valid
	public static class MessageReceiver{

		@Schema(title = "消息接收者ID")
		@NotNull(message = "消息接收者ID不能空",groups = {CreateAction.class})
		private Long id;

		@Setter
        @Getter
        @Schema(title = "消息接类型")
		@NotBlank(message = "消息接类型不能空",groups = {CreateAction.class})
		private String action;

		@Setter
        @Getter
        @Schema(title = "待返回的消息ID")
		private long mid;

		@Setter
        @Getter
        @Schema(title = "待返回的消息时间戳")
		private long timestamp;

		public long getId() {
			return id;
		}

		public void setId(long id) {
			this.id = id;
		}

    }
}
