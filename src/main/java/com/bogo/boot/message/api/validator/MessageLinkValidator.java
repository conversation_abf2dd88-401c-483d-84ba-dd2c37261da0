package com.bogo.boot.message.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.annotation.MessageLinkFormat;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.ChatLink;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class MessageLinkValidator implements ConstraintValidator<MessageLinkFormat, ContentFormatRequest> {

    @Override
    public boolean isValid(ContentFormatRequest request, ConstraintValidatorContext context) {

        if (request.getFormat() != MessageFormat.LINK){
            return true;
        }

        ChatLink chatLink = JSON.parseNullable(request.getContent(), ChatLink.class);

        return chatLink != null
                && StringUtils.isNotBlank(chatLink.getTitle())
                && StringUtils.isNotBlank(chatLink.getUrl());
    }
}
