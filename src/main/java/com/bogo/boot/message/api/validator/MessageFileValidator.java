package com.bogo.boot.message.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.annotation.MessageFileFormat;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.CloudFile;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MessageFileValidator implements ConstraintValidator<MessageFileFormat, ContentFormatRequest> {

    @Override
    public boolean isValid(ContentFormatRequest request, ConstraintValidatorContext context) {

        if (request.getFormat() != MessageFormat.FILE){
            return true;
        }

        CloudFile file = JSON.parseNullable(request.getContent(), CloudFile.class);
        return file != null
                && file.getFile() != null
                && file.getName() != null;
    }
}
