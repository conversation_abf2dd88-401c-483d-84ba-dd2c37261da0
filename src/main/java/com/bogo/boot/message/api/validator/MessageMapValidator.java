package com.bogo.boot.message.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.annotation.MessageMapFormat;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.ChatMap;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MessageMapValidator implements ConstraintValidator<MessageMapFormat, ContentFormatRequest> {

    @Override
    public boolean isValid(ContentFormatRequest request, ConstraintValidatorContext context) {

        if (request.getFormat() != MessageFormat.MAP){
            return true;
        }

        ChatMap map = JSON.parseNullable(request.getContent(), ChatMap.class);

        return map != null
                && map.getAddress() != null
                && map.getLongitude() != 0
                && map.getLatitude() != 0;
    }
}
