package com.bogo.boot.message.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.annotation.MessageVoiceFormat;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.ChatVoice;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MessageVoiceValidator implements ConstraintValidator<MessageVoiceFormat, ContentFormatRequest> {

    @Override
    public boolean isValid(ContentFormatRequest request, ConstraintValidatorContext context) {

        if (request.getFormat() != MessageFormat.VOICE){
            return true;
        }

        ChatVoice voice = JSON.parseNullable(request.getContent(), ChatVoice.class);

        return voice != null
                && voice.getFile() != null
                && voice.getLength() != 0;
    }
}
