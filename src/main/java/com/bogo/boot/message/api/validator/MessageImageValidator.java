package com.bogo.boot.message.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.annotation.MessageImageFormat;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.CloudImage;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MessageImageValidator implements ConstraintValidator<MessageImageFormat, ContentFormatRequest> {

    @Override
    public boolean isValid(ContentFormatRequest request, ConstraintValidatorContext context) {
        if (request.getFormat() != MessageFormat.IMAGE){
            return true;
        }

        CloudImage image = JSON.parseNullable(request.getContent(),CloudImage.class);

        return image != null
                && image.getThumb() != null
                && image.getImage() != null
                && image.getBucket() != null
                && image.getTh() != 0
                && image.getOh() != 0
                && image.getTw() != 0
                && image.getOw() != 0;
    }

}
