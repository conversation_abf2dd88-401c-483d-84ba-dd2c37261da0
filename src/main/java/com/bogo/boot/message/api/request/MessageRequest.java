
package com.bogo.boot.message.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.message.annotation.BanHtml;
import com.bogo.boot.message.annotation.MessageFileFormat;
import com.bogo.boot.message.annotation.MessageImageFormat;
import com.bogo.boot.message.annotation.MessageLinkFormat;
import com.bogo.boot.message.annotation.MessageMapFormat;
import com.bogo.boot.message.annotation.MessageVideoFormat;
import com.bogo.boot.message.annotation.MessageVoiceFormat;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.entity.ChatMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.model.MessageTitle;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Schema(title =  "单人消息请求体",description = "前面带*字段为必传")
@MessageVideoFormat(message = "视频消息格式不正确",groups = CreateAction.class)
@MessageImageFormat(message = "图片消息格式不正确",groups = CreateAction.class)
@MessageMapFormat(  message = "地图消息格式不正确",groups = CreateAction.class)
@MessageFileFormat( message = "文件消息格式不正确",groups = CreateAction.class)
@MessageVoiceFormat(message = "语音消息格式不正确",groups = CreateAction.class)
@MessageLinkFormat( message = "链接消息格式不正确",groups = CreateAction.class)
@BanHtml( message = "消息内容禁止包含HTML标签",groups = CreateAction.class)
@Getter
@Setter
public class MessageRequest implements Serializable, ContentFormatRequest {

	@Schema(title =  "接收者ID")
	@NotNull(message = "接收者ID不可为空",groups = CreateAction.class)
	protected Long uid;

	@Schema(title = "标题")
	protected String title;

	@Schema(title = "内容")
	protected String content;

	@Schema(title = "附加信息")
	protected String extra;

	@Schema(title = "格式")
	protected Byte format = MessageFormat.TEXT;

	public Message ofMessage(){
		ChatMessage message = new ChatMessage();
		message.setReceiver(uid);
		message.setAction(MessageAction.ACTION_0);

		MessageTitle messageTitle = MessageTitle.of(title);
		message.setTitle(messageTitle.toString());

		message.setContent(content);
		message.setExtra(extra);
		message.setFormat(format);
		return message;
	}
}
