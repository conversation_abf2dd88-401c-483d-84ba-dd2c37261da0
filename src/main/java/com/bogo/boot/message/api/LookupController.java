
package com.bogo.boot.message.api;

import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.PaginationEntity;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.message.api.vo.MessagePatchVO;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.service.MessageLookupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/message")
@Tag(name = "历史消息查询接口" )
public class LookupController {

	@Resource
	private MessageLookupService messageLookupService;

	@Operation( summary = "获取历史群消息")
	@Parameters({
			@Parameter(name = "id", description = "群组ID", in = ParameterIn.QUERY,example = "0",required = true),
			@Parameter(name = "fromId", description = "当前消息ID(最小ID)", in = ParameterIn.QUERY),
	})
	@GetMapping(value = "/lookup/group")
	public PaginationEntity<Message> findGroupList(@RequestParam long id, @RequestParam(required = false) Long fromId) {

		Page<Message> page = messageLookupService.findGroup(id,fromId == null ? 0 : fromId);

		return PaginationEntity.make(page);
	}


	@Operation( summary = "获取历史公众号消息")
	@Parameters({
			@Parameter(name = "id", description = "公众号ID", in = ParameterIn.QUERY,example = "0",required = true),
			@Parameter(name = "fromId", description = "当前消息ID(最小ID)", in = ParameterIn.QUERY),
	})
	@GetMapping(value = "/lookup/app")
	public PaginationEntity<Message> findAppList(@RequestParam long id, @RequestParam(required = false) Long fromId) {

		Page<Message> page = messageLookupService.findApp(id,fromId == null ? 0 : fromId);

		return PaginationEntity.make(page);
	}

	@Operation( summary = "获取历史单聊消息")
	@Parameters({
			@Parameter(name = "id", description = "好友UID", in = ParameterIn.QUERY,example = "0",required = true),
			@Parameter(name = "fromId", description = "当前消息ID(最小ID)", in = ParameterIn.QUERY),
	})
	@GetMapping(value = "/lookup/p2p")
	public PaginationEntity<Message> findP2pList(@Parameter(hidden = true) @UID Long uid, @RequestParam long id, @RequestParam(required = false) Long fromId) {

		Page<Message> page = messageLookupService.findP2p(uid,id,fromId == null ? 0 : fromId);

		return PaginationEntity.make(page);
	}

	@Operation( summary = "获取历史系统消息")
	@Parameter(name = "fromId", description = "当前消息ID(最小ID)", in = ParameterIn.QUERY)
	@GetMapping(value = "/lookup/system")
	public PaginationEntity<Message> findSystemList(@RequestParam(required = false) Long fromId) {

		Page<Message> page = messageLookupService.findSystem(fromId == null ? 0 : fromId);

		return PaginationEntity.make(page);
	}


	@Operation( summary = "获取补偿消息",description = "获取离线消息和本设备离线期间(一段时间内)在其他端接收过的消息")
	@Parameter(name = "maxMessageId", description = "客户端接收到的最大消息ID", in = ParameterIn.QUERY,example = "0")
	@GetMapping(value = "/lookup/patch")
	public ResponseEntity<MessagePatchVO> getPatchMessages(@Parameter(hidden = true) @UID Long uid,@RequestParam long maxMessageId) {

		MessagePatchVO patchVO = new MessagePatchVO();

		List<Message> offlineMessages = messageLookupService.findOfflineList(uid);

		List<Message> patchMessages = messageLookupService.findPatchMessages(uid,maxMessageId);
		patchMessages.removeAll(offlineMessages);

		long lastMessageId = Math.max(offlineMessages.stream().map(Message::getId).max(Long::compare).orElse(0L),patchMessages.stream().map(Message::getId).max(Long::compare).orElse(0L));

		patchVO.setMaxMessageId(lastMessageId);

		patchVO.setOffline(offlineMessages);
		patchVO.setPatch(patchMessages);

		return ResponseEntity.ok(patchVO);
	}
}
