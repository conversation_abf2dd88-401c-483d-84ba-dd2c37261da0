package com.bogo.boot.message.api.validator;

import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.annotation.MessageVideoFormat;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.CloudVideo;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MessageVideoValidator implements ConstraintValidator<MessageVideoFormat, ContentFormatRequest> {

    @Override
    public boolean isValid(ContentFormatRequest request, ConstraintValidatorContext context) {

        if (request.getFormat() != MessageFormat.VIDEO){
            return true;
        }

        CloudVideo video = JSON.parseNullable(request.getContent(), CloudVideo.class);

        return video != null
                && video.getVideo() != null
                && video.getImage() != null
                && video.getBucket() != null;
    }
}
