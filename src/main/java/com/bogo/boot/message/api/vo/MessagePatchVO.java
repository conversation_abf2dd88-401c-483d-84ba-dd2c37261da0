
package com.bogo.boot.message.api.vo;

import com.bogo.boot.message.entity.Message;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "消息补偿响应体")
@Getter
@Setter
public class MessagePatchVO {

	@Schema(title = "离线消息",description = "未接收过的消息")
	private List<Message> offline;

	@Schema(title = "多端同步补偿消息",description = "在其他设备已经接收过的")
	private List<Message> patch;

	@Schema(title = "本次获取的最大消息ID")
	private Long maxMessageId;

}
