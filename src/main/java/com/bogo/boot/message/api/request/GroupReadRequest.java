
package com.bogo.boot.message.api.request;

import com.bogo.boot.infra.annotation.UpdateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "群聊消息阅读请求体")
@Getter
@Setter
public class GroupReadRequest implements Serializable {

	@Schema(title = "群ID")
	@NotNull(message = "群ID不能为空",groups = UpdateAction.class)
	private Long groupId;

	@Schema(title = "消息信息,key:发送者UID value:消息ID数组")
	@Size(min = 1, message = "消息信息不能空", groups = UpdateAction.class)
	private Map<Long,Set<Long>> content;


	public boolean isEmpty(){
		return content == null || content.isEmpty();
	}

	public Set<Long> getIdSet(){
		return content.entrySet().stream().flatMap((Function<Map.Entry<Long, Set<Long>>, Stream<Long>>) entry -> entry.getValue().stream()).collect(Collectors.toSet());
	}
}
