
package com.bogo.boot.message.api.request;

import com.bogo.boot.infra.annotation.UpdateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "单聊消息阅读请求体")
@Getter
@Setter
public class ReadRequest implements Serializable {

	@Schema(title = "消息发送者UID")
	@NotNull(message = "消息发送者UID不能为空",groups = UpdateAction.class)
	private Long uid;

	@Schema(title = "消息ID数组")
	@Size(min = 1, message = "消息ID数组不能为空", groups = UpdateAction.class)
	private Set<Long> idList;
}
