package com.bogo.boot.message.apns;

import com.bogo.boot.account.constant.NotificationType;
import com.bogo.boot.account.entity.SilentNotification;
import com.bogo.boot.account.service.SilentNotificationService;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.app.service.MicroServerService;
import com.bogo.boot.group.service.GroupService;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.ChatEmoticon;
import com.bogo.boot.message.model.ChatLink;
import com.bogo.boot.message.model.ChatLinkList;
import com.bogo.boot.message.model.ChatMap;
import com.bogo.boot.message.model.ChatProfile;
import com.bogo.boot.message.model.CloudFile;
import com.bogo.boot.message.model.MessageTitle;
import com.bogo.messaging.model.Message;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;


@Component
public class APNsAlertHelper {

    @Resource
    private GroupService groupService;

    @Resource
    private UserService userService;

    @Resource
    private MicroServerService microServerService;

    @Resource
    private SilentNotificationService silentNotificationService;

    /**
     * 设置了免打扰
     * @param message
     * @return
     */
    public boolean isKeepQuiet(Message message){

        long recUid = Long.parseLong(message.getReceiver());

        long targetId = Long.parseLong(message.getSender());


        if (message.getAction().equals(MessageAction.ACTION_0)){
            return silentNotificationService.isKeepQuiet(recUid, targetId, NotificationType.FRIEND.getValue());
        }

        if (message.getAction().equals(MessageAction.ACTION_200)){
            return silentNotificationService.isKeepQuiet(recUid,targetId, NotificationType.APP.getValue());
        }

        if (message.getAction().equals(MessageAction.ACTION_3) || message.getAction().equals(MessageAction.ACTION_4)){
            return silentNotificationService.isKeepQuiet(recUid,targetId, NotificationType.GROUP.getValue());
        }

        return false;
    }

    public String getTitle(Message message){

        String action = message.getAction();

        if (action.equals(MessageAction.ACTION_3) || action.equals(MessageAction.ACTION_4)){
            return groupService.getName(Long.parseLong(message.getSender()));
        }

        if (action.equals(MessageAction.ACTION_200)){
            return microServerService.getName(Long.parseLong(message.getSender()));
        }

        if (action.equals(MessageAction.ACTION_0)
                || action.equals(MessageAction.ACTION_900)
                || action.equals(MessageAction.ACTION_600)
                || action.equals(MessageAction.ACTION_601)){
            return userService.findName(Long.parseLong(message.getSender()));
        }

        return "系统";
    }

    public String getBody(Message message){
        String action = message.getAction();

        if (action.equals(MessageAction.ACTION_900)){
            return "请求与您通话";
        }

        if (action.equals(MessageAction.ACTION_600)){
            return "邀请您加入多人会议";
        }

        if (action.equals(MessageAction.ACTION_601)){
            return "通知您加入预约会议";
        }
        if (action.equals(MessageAction.ACTION_3) || action.equals(MessageAction.ACTION_4)){
            MessageTitle title = JSON.parse(message.getTitle(),MessageTitle.class);
            boolean isText = String.valueOf(MessageFormat.TEXT).equals(message.getFormat());
            return title.getName() + (isText ? ":" : "") + getMessageText(message);
        }

        return getMessageText(message);
    }

    private String getMessageText(Message message){
        byte format = Byte.parseByte(message.getFormat());
        if (MessageFormat.IMAGE == format) {
            return "发来一张图片";
        }

        if (MessageFormat.VOICE == format) {
            return "发来一段语音";
        }

        if (MessageFormat.VIDEO == format) {
            return "发来一个短视频";
        }

        if (MessageFormat.FILE == format) {
            CloudFile file = JSON.parse(message.getContent(), CloudFile.class);
            return "发来文件:"+file.getName();
        }

        if (MessageFormat.MAP == format) {
            ChatMap map = JSON.parse(message.getContent(), ChatMap.class);
            return "发来地图:" + map.getAddress();
        }

        if (MessageFormat.EMOTICON == format) {
            ChatEmoticon emoticon = JSON.parse(message.getContent(), ChatEmoticon.class);
            return "发来表情:[" + emoticon.getName()+"]";
        }

        if (MessageFormat.PROFILE_CARD == format || MessageFormat.APP_PROFILE_CARD == format) {
            ChatProfile profile = JSON.parse(message.getContent(), ChatProfile.class);
            return "发来名片:[" + profile.getName()+"]";
        }

        if (MessageFormat.LINK == format) {
            ChatLink link = JSON.parse(message.getContent(), ChatLink.class);
            return "发来链接:" + link.getTitle();
        }

        if (MessageFormat.LINK_LIST== format) {
            ChatLinkList link = JSON.parse(message.getContent(), ChatLinkList.class);
            return "发来链接:" + link.getTitle();
        }

        return message.getContent();
    }
}
