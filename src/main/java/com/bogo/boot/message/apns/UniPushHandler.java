package com.bogo.boot.message.apns;

import com.bogo.boot.account.entity.Session;
import com.bogo.boot.account.event.LogoutEvent;
import com.bogo.boot.infra.configuration.properties.UniPushProperties;
import com.bogo.boot.infra.redis.KeyValueRedisTemplate;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.event.ClientBindEvent;
import com.bogo.boot.message.event.ThirdMessagePushEvent;
import com.bogo.messaging.model.Message;
import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.Settings;
import com.getui.push.v2.sdk.dto.req.message.PushChannel;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.AndroidDTO;
import com.getui.push.v2.sdk.dto.req.message.android.ThirdNotification;
import com.getui.push.v2.sdk.dto.req.message.android.Ups;
import com.getui.push.v2.sdk.dto.req.message.ios.Alert;
import com.getui.push.v2.sdk.dto.req.message.ios.Aps;
import com.getui.push.v2.sdk.dto.req.message.ios.IosDTO;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Component
@ConditionalOnProperty(name = {"bogo.uni.push.enable"})
public class UniPushHandler{

    private static final String UNI_PUSH_CID = "UNI_PUSH_CID_%s";

    private static final String UNI_PUSH_UID = "UNI_PUSH_UID_%s";

    private static final String LOCK_KEY = "UNI_PUSH_LOCK_%s";

    private final Logger logger = LoggerFactory.getLogger(UniPushHandler.class);

    private final PushApi pushApi;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private KeyValueRedisTemplate keyValueRedisTemplate;

    @Resource
    private APNsAlertHelper apnsAlertHelper;


    @Autowired
    public UniPushHandler(UniPushProperties properties){

        GtApiConfiguration apiConfiguration = new GtApiConfiguration();

        apiConfiguration.setAppId(properties.getAppId());
        apiConfiguration.setAppKey(properties.getAppKey());
        apiConfiguration.setMasterSecret(properties.getMasterSecret());
        apiConfiguration.setDomain(properties.getDomain());
        pushApi = ApiHelper.build(apiConfiguration).creatApi(PushApi.class);

    }

    @EventListener(classes = LogoutEvent.class)
    public void onLogout(LogoutEvent event){
        keyValueRedisTemplate.delete(String.format(UNI_PUSH_CID,event.getUid()));
    }

    @EventListener(classes = ClientBindEvent.class)
    public void onUniPushBind(ClientBindEvent event) {

        /*
        账号在非uniapp终端登录，清除clientId
         */
        if (!event.isUniApp() || StringUtils.isBlank(event.getUniPushClientId())){
            keyValueRedisTemplate.delete(String.format(UNI_PUSH_CID,event.getUid()));
            return;
        }

        /*
         * 同设备多个账号登录，清除上一次登录的UID - CID关联记录
         */
        String lastUid = keyValueRedisTemplate.get(String.format(UNI_PUSH_UID,event.getUniPushClientId()));
        if (lastUid != null && !Objects.equals(lastUid,String.valueOf(event.getUid()))){
            keyValueRedisTemplate.delete(String.format(UNI_PUSH_CID,lastUid));
        }

        UniPushClient client = new UniPushClient();
        client.channel = event.getChannel();
        client.clientId = event.getUniPushClientId();
        keyValueRedisTemplate.set(String.format(UNI_PUSH_CID,event.getUid()), JSON.toJSONString(client));

        keyValueRedisTemplate.set(String.format(UNI_PUSH_UID,client.clientId),String.valueOf(event.getUid()));

    }

    @Async("apnsTaskExecutor")
    @EventListener(classes = ThirdMessagePushEvent.class,condition = "#root.args[0].isNotEmpty")
    public void accept(ThirdMessagePushEvent event){

        String key = String.format(LOCK_KEY, event.getTraceId());
        RLock lock = redissonClient.getLock(key);

        if (!lock.tryLock()) {
            return;
        }

        try {
            event.getMessageList().forEach(UniPushHandler.this::accept);
        } finally {
            lock.unlock();
        }

    }

    private void accept(Message message) {

        String data = keyValueRedisTemplate.get(String.format(UNI_PUSH_CID,message.getReceiver()));

        if(StringUtils.isBlank(data)) {
            return;
        }

        UniPushClient client = JSON.parse(data,UniPushClient.class);

        /*
         * 用户设置了该消息免打扰
         */
        if (apnsAlertHelper.isKeepQuiet(message)){
            return;
        }


        String title = apnsAlertHelper.getTitle(message);
        String body = apnsAlertHelper.getBody(message);

        PushDTO<Audience> pushDTO = new PushDTO<>();
        pushDTO.setRequestId(UUID.randomUUID().toString());
        Settings settings = new Settings();
        pushDTO.setSettings(settings);
        settings.setTtl(3600000);

        Audience audience = new Audience();
        audience.addCid(client.clientId);
        pushDTO.setAudience(audience);

        PushChannel pushChannel = new PushChannel();

        /*
         * 配置uni-android离线推送
         */
        if (Objects.equals(client.channel, Session.CHANNEL_UNI_ANDROID)){
            pushChannel.setAndroid(getAndroidDTO(message,title,body));
        }

        /*
         * 配置uni-iOS离线推送
         */
        if (Objects.equals(client.channel, Session.CHANNEL_UNI_IOS)){
            pushChannel.setIos(getApnsDTO(message, title, body));
        }

        PushMessage pushMessage = new PushMessage();
        pushMessage.setTransmission(String.format("{title:\"%s\",content:\"%s\"}",title,body));

        pushDTO.setPushMessage(pushMessage);
        pushDTO.setPushChannel(pushChannel);

        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByCid(pushDTO);
        if (apiResult.isSuccess()) {
            logger.info("UniPush推送成功,uid:{},channel:{}", message.getReceiver(),client.channel);
        } else {
            logger.warn("UniPush推送失败,uid:{},channel:{},code:{},message:{}",message.getReceiver(),client.channel,apiResult.getCode(),apiResult.getMsg());
        }

    }

    private AndroidDTO getAndroidDTO(Message message, String title, String body){
        AndroidDTO androidDTO = new AndroidDTO();
        Ups ups = new Ups();
        ThirdNotification notification = new ThirdNotification();
        notification.setTitle(title);
        notification.setBody(body);
        notification.setPayload(JSON.toJSONString(message));
        notification.setClickType("startapp");

        ups.setNotification(notification);

        /*
         华为推送配置
         */
        ups.addOption("HW","/message/android/category","IM");

        androidDTO.setUps(ups);

        return androidDTO;
    }

    private IosDTO getApnsDTO(Message message, String title, String body) {
        Alert alert = new Alert();
        alert.setTitle(title);
        alert.setBody(body);
        Aps aps = new Aps();
        aps.setContentAvailable(0);
        aps.setSound("default");
        aps.setAlert(alert);

        IosDTO iosDTO = new IosDTO();
        iosDTO.setAps(aps);
        iosDTO.setType("notify");
        iosDTO.setPayload(JSON.toJSONString(message));
        return iosDTO;
    }

    private static class UniPushClient{
        public String channel;
        public String clientId;
    }
}
