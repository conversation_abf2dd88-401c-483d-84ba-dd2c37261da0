package com.bogo.boot.message.event;

import com.bogo.boot.message.constant.MessageActionGroup;
import com.bogo.messaging.model.Message;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class ThirdMessagePushEvent extends ApplicationEvent {

    private final List<Message> messageList;

    private final String traceId;

    public ThirdMessagePushEvent(List<Message> messageList, String traceId) {
        super("");
        this.messageList = messageList
                .stream()
                .filter(message -> MessageActionGroup.PUSHABLE.contains(message.getAction()))
                .collect(Collectors.toList());
        this.traceId = traceId;
    }

    public boolean isNotEmpty() {
        return messageList != null && !messageList.isEmpty();
    }

}
