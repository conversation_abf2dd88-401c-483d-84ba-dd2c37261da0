package com.bogo.boot.message.event;

import com.bogo.boot.account.entity.Session;
import com.bogo.messaging.model.SentBody;
import org.springframework.context.ApplicationEvent;


public class ClientBindEvent extends ApplicationEvent {


    public ClientBindEvent(SentBody body) {
        super(body);
    }

    @Override
    public SentBody getSource() {
        return (SentBody) source;
    }

    public boolean isUniApp(){
        String channel = getChannel();
        return Session.CHANNEL_UNI_ANDROID.equals(channel) || Session.CHANNEL_UNI_IOS.equals(channel);
    }

    public String getChannel(){
        return getSource().get("channel");
    }

    public String getUniPushClientId(){
        return getSource().get("clientId");
    }

    public long getUid(){
        return Long.parseLong(getSource().get("uid"));
    }

}
