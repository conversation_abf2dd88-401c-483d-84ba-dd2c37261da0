package com.bogo.boot.message.event;

import com.bogo.boot.message.api.request.ReadRequest;
import java.util.Set;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class ReadMessageEvent extends ApplicationEvent {

    private final long uid;
    private final Set<Long> idList;

    public ReadMessageEvent(ReadRequest request) {
        super("");
        this.idList = request.getIdList();
        this.uid = request.getUid();
    }
}
