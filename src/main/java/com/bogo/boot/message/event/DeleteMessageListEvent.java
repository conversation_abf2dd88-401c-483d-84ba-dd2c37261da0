package com.bogo.boot.message.event;

import com.bogo.boot.message.entity.Message;
import java.util.List;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class DeleteMessageListEvent extends ApplicationEvent {

    private final List<Message> messages;

    public DeleteMessageListEvent(List<Message> messages) {
        super("");
        this.messages = messages;
    }

}
