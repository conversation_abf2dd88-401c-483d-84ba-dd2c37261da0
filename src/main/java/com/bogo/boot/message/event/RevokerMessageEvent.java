package com.bogo.boot.message.event;

import com.bogo.boot.message.entity.Message;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class RevokerMessageEvent extends ApplicationEvent {

    /*
     * 谁操作了撤回消息
     */
    private final long uid;
    private final Message message;

    public RevokerMessageEvent(long uid,Message message) {
        super("");
        this.uid = uid;
        this.message = message;
    }

}
