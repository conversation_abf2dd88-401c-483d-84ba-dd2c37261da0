package com.bogo.boot.message.event;

import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.entity.Message;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class ForwardMessageEvent extends ApplicationEvent {

    private final Message message;

    public ForwardMessageEvent(Message message) {
        super("");
        this.message = message;
    }
    /**
     * 是否是文件类消息
     * @return
     */
    public boolean isFileMessage(){
        byte format = message.getFormat();
        return MessageFormat.FILE == format
                || MessageFormat.IMAGE == format
                || MessageFormat.VIDEO == format
                || MessageFormat.VOICE == format
                || MessageFormat.MAP == format;
    }

}
