package com.bogo.boot.message.event;

import com.bogo.boot.message.api.request.GroupReadRequest;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class ReadGroupMessageEvent extends ApplicationEvent {

    private final long groupId;

    private final Map<Long, Set<Long>> content;

    public ReadGroupMessageEvent(GroupReadRequest request) {
        super("");
        this.groupId = request.getGroupId();
        this.content = request.getContent();
    }

    public Set<Long> getMessageIdSet(){
        return content.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
    }
}
