package com.bogo.boot.message.listener;

import com.bogo.boot.account.entity.Session;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.event.MakeSessionEvent;
import com.bogo.messaging.constant.ChannelAttr;
import com.bogo.messaging.group.SessionGroup;
import com.bogo.messaging.model.Message;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelOutboundInvoker;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Predicate;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 * 集群环境下，监控多设备登录情况，控制是否其余终端下线的逻辑
 */
@Component
public class BindMessageListener implements MessageListener {

    /*
     一个账号只能在同一个类型的终端登录
     如: 多个android或ios不能同时在线
         一个android或ios可以和web，桌面同时在线
     */
    private final Map<String,String[]> conflictMap = new HashMap<>();

    /*
     * web 和 H5 可能同一个终端 打开多 tab页面，可以同时保持连接
     */
    private final Set<String> keepLiveChannels = new HashSet<>();

    @Resource
    private SessionGroup sessionGroup;

    public BindMessageListener(){
        conflictMap.put(Session.CHANNEL_ANDROID,     new String[]{Session.CHANNEL_ANDROID,Session.CHANNEL_IOS,Session.CHANNEL_UNI_H5,Session.CHANNEL_UNI_IOS,Session.CHANNEL_UNI_ANDROID});
        conflictMap.put(Session.CHANNEL_IOS,         new String[]{Session.CHANNEL_ANDROID,Session.CHANNEL_IOS,Session.CHANNEL_UNI_H5,Session.CHANNEL_UNI_IOS,Session.CHANNEL_UNI_ANDROID});
        conflictMap.put(Session.CHANNEL_WINDOWS,     new String[]{Session.CHANNEL_WINDOWS,Session.CHANNEL_WEB,Session.CHANNEL_MAC});
        conflictMap.put(Session.CHANNEL_WEB,         new String[]{Session.CHANNEL_WINDOWS,Session.CHANNEL_WEB,Session.CHANNEL_MAC});
        conflictMap.put(Session.CHANNEL_MAC,         new String[]{Session.CHANNEL_WINDOWS,Session.CHANNEL_WEB,Session.CHANNEL_MAC});

        conflictMap.put(Session.CHANNEL_UNI_ANDROID, new String[]{Session.CHANNEL_ANDROID,Session.CHANNEL_IOS,Session.CHANNEL_UNI_H5,Session.CHANNEL_UNI_IOS,Session.CHANNEL_UNI_ANDROID});
        conflictMap.put(Session.CHANNEL_UNI_IOS,     new String[]{Session.CHANNEL_ANDROID,Session.CHANNEL_IOS,Session.CHANNEL_UNI_H5,Session.CHANNEL_UNI_IOS,Session.CHANNEL_UNI_ANDROID});
        conflictMap.put(Session.CHANNEL_UNI_H5,      new String[]{Session.CHANNEL_ANDROID,Session.CHANNEL_IOS,Session.CHANNEL_UNI_H5,Session.CHANNEL_UNI_IOS,Session.CHANNEL_UNI_ANDROID});

        keepLiveChannels.add(Session.CHANNEL_WEB);
        keepLiveChannels.add(Session.CHANNEL_UNI_H5);

    }

    @Override
    public void onMessage(org.springframework.data.redis.connection.Message redisMessage, byte[] bytes) {
        Session session = JSON.parse(redisMessage.getBody(), Session.class);
        this.handle(session);
    }

    @EventListener
    public void onMessage(MakeSessionEvent event) {
        this.handle(event.getSource());
    }

    private void handle(Session session){

        String uid = session.getUid().toString();

        String[] conflictChannels = conflictMap.get(session.getChannel());

        if (ArrayUtils.isEmpty(conflictChannels)){
            return;
        }

        Collection<Channel> channelList = sessionGroup.find(uid,conflictChannels);

        channelList.removeIf(new KeepLivePredicate(session));

        /*
         * 同设备仅关闭连接，无需通知客户端
         */
        channelList.stream().filter(new SameDevicePredicate(session)).forEach(ChannelOutboundInvoker::close);

        /*
         * 不同设备关闭连接， 通知客户端账号在其他设备登录
         */
        channelList.stream().filter(new DifferentDevicePredicate(session)).forEach(new BreakOffMessageConsumer(uid,session.getDeviceName()));

    }


    private static class BreakOffMessageConsumer implements Consumer<Channel>{

        private final Message message;

        private BreakOffMessageConsumer(String uid,String deviceName) {
            message = new Message();
            message.setAction(MessageAction.ACTION_999);
            message.setReceiver(uid);
            message.setSender(String.valueOf(Common.SYSTEM_ID));
            message.setContent(deviceName);
        }

        @Override
        public void accept(Channel channel) {
            channel.writeAndFlush(message).addListener(ChannelFutureListener.CLOSE);
        }
    }
    private static class SameDevicePredicate implements Predicate<Channel>{

        private final String deviceId;

        private SameDevicePredicate(Session session) {
            this.deviceId = session.getDeviceId();
        }

        @Override
        public boolean test(Channel channel) {
            return Objects.equals(this.deviceId,channel.attr(ChannelAttr.DEVICE_ID).get());
        }
    }

    private static class DifferentDevicePredicate implements Predicate<Channel>{

        private final SameDevicePredicate predicate;

        private DifferentDevicePredicate(Session session) {
            this.predicate = new SameDevicePredicate(session);
        }

        @Override
        public boolean test(Channel channel) {
            return !predicate.test(channel);
        }
    }


    private class KeepLivePredicate implements Predicate<Channel>{
        private final Session session;

        private KeepLivePredicate(Session session) {
            this.session = session;
        }

        @Override
        public boolean test(Channel ioChannel) {

            if (Objects.equals(session.getNid(),ioChannel.attr(ChannelAttr.ID).get())){
                return true;
            }

            String deviceId = ioChannel.attr(ChannelAttr.DEVICE_ID).toString();

            String channel = ioChannel.attr(ChannelAttr.CHANNEL).toString();

            return keepLiveChannels.contains(channel) && Objects.equals(session.getDeviceId(),deviceId);
        }
    }
}
