package com.bogo.boot.message.listener;

import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.event.PushMessageEvent;
import com.bogo.boot.message.event.ThirdMessagePushEvent;
import com.bogo.boot.message.model.PushMessage;
import com.bogo.messaging.group.SessionGroup;
import com.bogo.messaging.model.Message;
import io.netty.channel.Channel;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.function.Predicate;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;


/**
 * 集群环境下，监听redis队列，广播消息到每个实例进行推送
 * 如果使用MQ的情况下，最好替换为MQ消息队列
 */
@Component
public class PushMessageListener implements MessageListener {

    @Resource
    private SessionGroup sessionGroup;

    @Resource
    private ApplicationEventProducer applicationEventProducer;

    @Override
    public void onMessage(org.springframework.data.redis.connection.Message redisMessage, byte[] bytes) {
        PushMessage batchMessage = JSON.parse(redisMessage.getBody(), PushMessage.class);
        this.handle(batchMessage);
    }

    @EventListener
    public void onMessage(PushMessageEvent event) {
        this.handle(event.getSource());
    }

    private void handle(PushMessage pushMessage){

        List<Message> messageList = pushMessage.getMessageList();

        Predicate<Channel> writePredicate = pushMessage.getPredicate();

        messageList.forEach(message -> sessionGroup.write(message.getReceiver(), message, writePredicate));

        messageList.removeIf(message -> sessionGroup.containsKey(message.getReceiver()));

        applicationEventProducer.publish(new ThirdMessagePushEvent(messageList, pushMessage.getTraceId()));
    }


}
