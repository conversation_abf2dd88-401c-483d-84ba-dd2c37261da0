package com.bogo.boot.message.listener;

import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.event.ReadGroupMessageEvent;
import com.bogo.boot.message.event.ReadMessageEvent;
import com.bogo.boot.message.model.EventMessageCompat;
import com.bogo.boot.message.pusher.DefaultMessagePusher;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 处理阅读消息事件
 */
@Component
public class ReadMessageObserver {

    @Resource
    private DefaultMessagePusher defaultMessagePusher;



    @EventListener
    public void onMessageReadEvent(ReadMessageEvent event){
        Message message = new EventMessage();
        message.setSender(UidHolder.getUid());
        message.setContent(StringUtils.join(event.getIdList(),","));
        message.setReceiver(event.getUid());
        message.setAction(MessageAction.ACTION_100);
        defaultMessagePusher.push(message);

        /*
        给自己登录的其他客户端发送已经阅读了消息，可消除未读标记
         */
        defaultMessagePusher.push(
                EventMessageCompat.from()
                        .setAction(MessageAction.ACTION_114)
                        .setContent(StringUtils.join(event.getIdList(),","))
                        .setReceiver(UidHolder.getUid())
                        .setIgnoreCurrentChannel(true)
        );
    }

    @EventListener
    public void onMessageReadEvent(ReadGroupMessageEvent event){
        for (Map.Entry<Long, Set<Long>> entry : event.getContent().entrySet()){
            Message message = new EventMessage();
            message.setSender(event.getGroupId());
            message.setContent(StringUtils.join(entry.getValue(),","));
            message.setExtra(UidHolder.getUid().toString());
            message.setReceiver(entry.getKey());
            message.setAction(MessageAction.ACTION_321);
            defaultMessagePusher.push(message);
        }

        /*
        给自己登录的其他客户端发送已经阅读了消息，可消除未读标记
         */

        defaultMessagePusher.push(
                EventMessageCompat.from().setAction(MessageAction.ACTION_322)
                        .setContent(StringUtils.join(event.getMessageIdSet(),","))
                        .setReceiver(UidHolder.getUid())
                        .setIgnoreCurrentChannel(true)
        );

    }
}
