package com.bogo.boot.message.listener;

import com.bogo.boot.contact.event.FriendActionEvent;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.event.RevokerMessageEvent;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class MessageRevoker {

    private static final Consumer<RevokerMessageEvent> EMPTY_HANDLER = message -> {};

    private final Map<String, Consumer<RevokerMessageEvent>> revokerMap = new HashMap<>();

    @Resource
    private ApplicationEventProducer applicationEventProducer;


    public MessageRevoker(){
        revokerMap.put(MessageAction.ACTION_0,new ChatMessageRevoker());
        revokerMap.put(MessageAction.ACTION_3,new GroupMessageRevoker());
    }

    @EventListener
    public void onMessageRevokeEvent(RevokerMessageEvent event){
        String action = event.getMessage().getAction();
        revokerMap.getOrDefault(action,EMPTY_HANDLER).accept(event);
    }

    private class ChatMessageRevoker implements Consumer<RevokerMessageEvent>{

        @Override
        public void accept(RevokerMessageEvent event) {
            FriendActionEvent friendEvent = new FriendActionEvent();
            friendEvent.setAction(MessageAction.ACTION_101);
            friendEvent.setSender(event.getMessage().getSender());
            friendEvent.setReceiver(event.getMessage().getReceiver());
            friendEvent.setContent(event.getMessage().getId().toString());
            applicationEventProducer.publish(friendEvent);
        }
    }

    private class GroupMessageRevoker implements Consumer<RevokerMessageEvent>{

        @Override
        public void accept(RevokerMessageEvent event) {

            /*
             * 有可能是管理员撤回了消息
             */
            long makerUid = event.getUid();

            GroupActionEvent groupEvent = new GroupActionEvent();
            groupEvent.setMakerUid(makerUid);
            groupEvent.setSender(event.getMessage().getSender());
            groupEvent.setAction(MessageAction.ACTION_310);
            groupEvent.setContent(event.getMessage().getId().toString());
            groupEvent.setExtra(String.valueOf(makerUid));
            groupEvent.setReplaceable(false);
            groupEvent.addIgnored(makerUid);

            applicationEventProducer.publish(groupEvent);
        }
    }
}
