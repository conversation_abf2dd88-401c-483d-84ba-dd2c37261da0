package com.bogo.boot.message.listener;

import com.bogo.boot.contact.event.FriendActionEvent;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.infra.constant.ChangeType;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.redis.LastMessageIdTemplate;
import com.bogo.boot.message.repository.MessageEventRepository;
import com.bogo.boot.message.service.MessageIndexService;
import com.bogo.boot.moment.entity.Comment;
import com.bogo.boot.moment.entity.Moment;
import com.bogo.boot.moment.event.CommentActionEvent;
import com.bogo.boot.moment.event.MomentActionEvent;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
public class EventMessageRemover {

    @Resource
    private MessageIndexService messageIndexService;

    @Resource
    private LastMessageIdTemplate lastMessageIdTemplate;

    @Resource
    private MessageEventRepository messageEventRepository;

    @EventListener
    @Order(1)
    public void onGroupEvent(GroupActionEvent event){

        if (!event.isReplaceable()){
            return;
        }

        Message message = event.getMessage();

        this.deleteEventMessage(message,event.getUidList());

    }

    @EventListener
    @Order(1)
    public void onFriendEvent(FriendActionEvent event){
        Message message = event.getMessage();
        this.deleteEventMessage(message,event.getReceivers());
    }

    private void deleteEventMessage(Message message, List<Long> receivers){

         /*
         删除掉未接收的更新信息通知
         */
        Long lastMid = lastMessageIdTemplate.get(message.getSender(),message.getAction());

        this.delete(receivers,lastMid);

        lastMessageIdTemplate.set(message.getSender(),message.getAction(),message.getId());

        messageEventRepository.deleteAll(message.getSender(), Collections.singleton(message.getAction()));
    }

    @EventListener
    @Order(1)
    public void onMomentEvent(MomentActionEvent event){
        Moment moment = event.getSource();
        Message message = event.getMessage();

        if (event.getType() == ChangeType.ADD){
            lastMessageIdTemplate.set(moment.getId(),message.getAction(),message.getId());
        }

        if (event.getType() == ChangeType.DELETE){
            Long lastMid = lastMessageIdTemplate.get(moment.getId(),MessageAction.ACTION_500);
            this.delete(event.getUidList(),lastMid);
        }
    }

    @EventListener
    @Order(1)
    public void onCommentEvent(CommentActionEvent event){
        Comment comment = event.getSource();
        Message message = event.getMessage();

        if (event.getType() == ChangeType.ADD){
            lastMessageIdTemplate.set(comment.getId(),message.getAction(),message.getId());
        }

        if (event.getType() == ChangeType.DELETE){
            Long lastMid = lastMessageIdTemplate.get(comment.getId(),MessageAction.ACTION_502);
            this.delete(event.getUidList(),lastMid);
        }

    }

    private void delete(List<Long> uidList,Long mid){
        if (mid == null || CollectionUtils.isEmpty(uidList)){
            return;
        }
        messageIndexService.delete(uidList,mid);
    }

}
