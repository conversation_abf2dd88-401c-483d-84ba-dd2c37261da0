
package com.bogo.boot.message.feign;

import com.bogo.boot.message.feign.fallback.BlackwordHookServiceFallbackFactory;
import com.bogo.boot.message.feign.request.BlackwordHookRequest;
import java.net.URI;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(value = "blackword-hook-service",url = "*",fallbackFactory = BlackwordHookServiceFallbackFactory.class)
public interface IBlackwordHookService {

	@PostMapping(produces = "application/json")
	void notify(URI uri, @RequestBody BlackwordHookRequest request);
}
