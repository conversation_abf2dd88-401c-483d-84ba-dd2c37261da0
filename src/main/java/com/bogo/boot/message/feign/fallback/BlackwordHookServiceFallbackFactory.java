package com.bogo.boot.message.feign.fallback;


import com.bogo.boot.message.feign.IBlackwordHookService;
import com.bogo.boot.message.feign.request.BlackwordHookRequest;
import java.net.URI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class BlackwordHookServiceFallbackFactory implements FallbackFactory<IBlackwordHookService>, IBlackwordHookService {

	private final Logger logger = LoggerFactory.getLogger(BlackwordHookServiceFallbackFactory.class);

	@Override
	public IBlackwordHookService create(Throwable throwable) {
		logger.warn("blackword-hook-service接口熔断",throwable);
		return this;
	}


	@Override
	public void notify(URI uri, BlackwordHookRequest request) {

	}
}
