
package com.bogo.boot.emoji.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/*
表情图
 */
@Entity
@Table(name = "t_bogo_emoticon_item")
@Getter
@Setter
public class EmoticonItem{

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

    /*
     表情包ID
     */
	@Column(name = "emoticon_id")
	private Long emoticonId;

	@Column(name = "name", length = 16, nullable = false)
	private String name;

    /**
     * 类型
     *
     * @see com.bogo.boot.emoji.constant.EmoticonType
     */
	@Column(name = "type",nullable = false)
	private byte type;

}
