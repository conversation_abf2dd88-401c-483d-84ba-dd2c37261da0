
package com.bogo.boot.emoji.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/*
表情包
 */
@Entity
@Table(name = "t_bogo_emoticon")
@Getter
@Setter
public class Emoticon {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "name", nullable = false ,length = 16)
	private String name;

    /**
     * 类型
     *
     * @see com.bogo.boot.emoji.constant.EmoticonType
     */
	@Column(name = "type",nullable = false)
	private byte type;

    @Column(name = "description", length = 200, nullable = false)
    private String description;
}
