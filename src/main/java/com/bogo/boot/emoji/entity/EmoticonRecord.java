
package com.bogo.boot.emoji.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/*
用户购买的表情包
 */
@Entity
@Table(name = "t_bogo_emoticon_record")
@Getter
@Setter
public class EmoticonRecord{

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "uid")
	private Long uid;

	@Column(name = "emoticon_id")
	private Long emoticonId;

    /**
     * @see com.bogo.boot.emoji.constant.EmoticonState
     */
	@Column(name = "state")
	private byte state;

	@Column(name = "create_time",nullable = false)
	private Date createTime;

}
