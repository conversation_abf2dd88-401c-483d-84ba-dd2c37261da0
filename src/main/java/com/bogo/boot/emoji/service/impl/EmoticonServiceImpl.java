
package com.bogo.boot.emoji.service.impl;

import com.bogo.boot.emoji.api.vo.EmoticonVO;
import com.bogo.boot.emoji.constant.EmoticonState;
import com.bogo.boot.emoji.entity.Emoticon;
import com.bogo.boot.emoji.entity.EmoticonItem;
import com.bogo.boot.emoji.entity.EmoticonRecord;
import com.bogo.boot.emoji.repository.EmoticonItemRepository;
import com.bogo.boot.emoji.repository.EmoticonRecordRepository;
import com.bogo.boot.emoji.repository.EmoticonRepository;
import com.bogo.boot.emoji.service.EmoticonService;
import com.bogo.boot.infra.util.PageX;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class EmoticonServiceImpl implements EmoticonService {

	@Resource
	private EmoticonRepository emoticonRepository;

	@Resource
	private EmoticonRecordRepository recordRepository;

	@Resource
	private EmoticonItemRepository itemRepository;

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void add(long uid, long emoticonId) {

		Emoticon emoticon = emoticonRepository.findById(emoticonId).orElse(null);
		if (emoticon == null){
			return;
		}

		EmoticonRecord record = new EmoticonRecord();
		record.setCreateTime(new Date());
		record.setEmoticonId(emoticonId);
		record.setUid(uid);
		record.setState(EmoticonState.NORMAL.getValue());

		recordRepository.saveAndFlush(record);
	}

	@Override
	public List<EmoticonVO> findList(long uid) {

		List<EmoticonRecord> records = recordRepository.findList(uid);

		List<Long> idList = records.stream().map(EmoticonRecord::getEmoticonId).collect(Collectors.toList());

		Map<Long,Byte> stateMap = new HashMap<>();

		for (EmoticonRecord record : records){
			stateMap.put(record.getEmoticonId(), record.getState());
		}

		List<EmoticonVO> voList = PageX.map(EmoticonVO::of,emoticonRepository.findList(idList));

		for (EmoticonVO emoticon : voList){
			emoticon.setState(stateMap.get(emoticon.getId()));
			emoticon.setItemList(itemRepository.findList(emoticon.getId()));
		}

		return voList;
	}

	@Override
	public EmoticonVO findOne(long id) {
		return EmoticonVO.of(emoticonRepository.findById(id).orElse(null));
	}

	@Override
	public List<EmoticonItem> findItemList(long id) {
		return itemRepository.findList(id);
	}

	@Override
	public Page<EmoticonVO> queryPage(Pageable pageable) {
		Page<Emoticon> page = emoticonRepository.findAll(pageable);
		return PageX.map(EmoticonVO::of,page);
	}

	@Override
	public void disable(long uid, long emoticonId) {
		recordRepository.updateState(uid,emoticonId, EmoticonState.DISABLED.getValue());
	}

	@Override
	public void enable(long uid, long emoticonId) {
		recordRepository.updateState(uid,emoticonId, EmoticonState.NORMAL.getValue());
	}

}
