
package com.bogo.boot.emoji.service;

import com.bogo.boot.emoji.api.vo.EmoticonVO;
import com.bogo.boot.emoji.entity.EmoticonItem;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface EmoticonService {

	void add(long uid, long emoticonId);

	List<EmoticonVO> findList(long uid);

	EmoticonVO findOne(long id);

	List<EmoticonItem> findItemList(long id);

	Page<EmoticonVO> queryPage(Pageable pageable);

	void disable(long uid, long emoticonId);

	void enable(long uid, long emoticonId);

}
