
package com.bogo.boot.emoji.api.vo;

import com.bogo.boot.emoji.entity.Emoticon;
import com.bogo.boot.emoji.entity.EmoticonItem;
import com.bogo.boot.infra.model.Sortable;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/*
表情包
 */

@Schema(title = "表情包")
@Getter
@Setter
public class EmoticonVO implements Sortable {

	@Schema(title = "ID")
	private Long id;

	@Schema(title = "名称")
	private String name;

	@Schema(title = "简介")
	private String description;

	@Schema(title = "类型 0静态 1动态")
	private byte type;

	@Schema(title = "状态 0禁用 1正常")
	private byte state;

	@Schema(title = "表情列表")
	private List<EmoticonItem> itemList;

	public static EmoticonVO of(Emoticon emoticon){
		if (emoticon == null){
			return null;
		}
		EmoticonVO vo = new EmoticonVO();
		vo.description = emoticon.getDescription();
		vo.type = emoticon.getType();
		vo.id = emoticon.getId();
		vo.name = emoticon.getName();
		return vo;
	}
}
