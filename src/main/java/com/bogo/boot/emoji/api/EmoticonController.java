
package com.bogo.boot.emoji.api;

import com.bogo.boot.emoji.api.vo.EmoticonVO;
import com.bogo.boot.emoji.entity.EmoticonItem;
import com.bogo.boot.emoji.service.EmoticonService;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.model.PaginationEntity;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/emoticon")
@Tag(name = "表情包接口" )
public class EmoticonController {

    @Resource
    private EmoticonService emoticonService;

    @Operation( summary = "获取表情包列表")
    @Parameter(name = "currentPage", description = "当前页数", in = ParameterIn.PATH,example = "0",required = true)
    @GetMapping(value = "/mall/list/{currentPage}")
    public PaginationEntity<EmoticonVO> list(@PathVariable int currentPage) {
        Pageable pageable = PageRequest.of(currentPage, Common.API_PAGE_SIZE, Sort.by(Sort.Order.desc("id")));
        Page<EmoticonVO> page = emoticonService.queryPage(pageable);
        return PaginationEntity.make(page);
    }

    @Operation( summary = "获取表情包详情")
    @Parameter(name = "id", description = "表情包ID", in = ParameterIn.PATH,example = "0",required = true)
    @GetMapping(value = "/{id}")
    public ResponseEntity<EmoticonVO> get(@PathVariable long id) {
        return ResponseEntity.ok(emoticonService.findOne(id));
    }


    @Operation( summary = "添加表情包")
    @Parameter(name = "id", description = "表情包ID", in = ParameterIn.PATH,example = "0",required = true)
    @PostMapping(value = "/{id}")
    public ResponseEntity<Void> add(@Parameter(hidden = true) @UID Long uid, @PathVariable long id) {
        emoticonService.add(uid,id);
        return ResponseEntity.ok();
    }

    @Operation( summary = "禁用表情包")
    @Parameter(name = "id", description = "表情包ID", in = ParameterIn.PATH,example = "0",required = true)
    @PostMapping(value = "/disable/{id}")
    public ResponseEntity<Void> disable(@Parameter(hidden = true) @UID Long uid,@PathVariable long id) {
        emoticonService.disable(uid,id);
        return ResponseEntity.ok();
    }

    @Operation( summary = "启用表情包")
    @Parameter(name = "id", description = "表情包ID", in = ParameterIn.PATH,example = "0",required = true)
    @PostMapping(value = "/enable/{id}")
    public ResponseEntity<Void> enable(@Parameter(hidden = true) @UID Long uid,@PathVariable long id) {
        emoticonService.enable(uid,id);
        return ResponseEntity.ok();
    }

    @Operation( summary = "获取表情列表")
    @Parameter(name = "id", description = "表情包ID", in = ParameterIn.PATH,example = "0",required = true)
    @GetMapping(value = "/item/list/{id}")
    public ResponseEntity<List<EmoticonItem>> list(@PathVariable long id) {
        return ResponseEntity.ok(emoticonService.findItemList(id));
    }

}
