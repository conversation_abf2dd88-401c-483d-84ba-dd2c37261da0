
package com.bogo.boot.emoji.repository;

import com.bogo.boot.emoji.entity.Emoticon;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
public interface EmoticonRepository extends JpaRepository<Emoticon, Long> {

    @Query("from Emoticon where id in (?1)")
    List<Emoticon> findList(List<Long> idList);
}
