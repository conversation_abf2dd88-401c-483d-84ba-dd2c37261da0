
package com.bogo.boot.emoji.repository;

import com.bogo.boot.emoji.entity.EmoticonItem;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
public interface EmoticonItemRepository extends JpaRepository<EmoticonItem, Long> {
	@Query("from EmoticonItem where emoticonId =?1 order by id desc")
	List<EmoticonItem> findList(long emoticonId);
}
