
package com.bogo.boot.emoji.repository;

import com.bogo.boot.emoji.entity.EmoticonRecord;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
public interface EmoticonRecordRepository extends JpaRepository<EmoticonRecord, Long> {

    @Query("from EmoticonRecord where uid =?1 order by id desc")
    List<EmoticonRecord> findList(long uid);

    @Modifying
    @Query("update EmoticonRecord set state = ?3 where uid = ?1 and emoticonId = ?2")
    void updateState(long uid, long emoticonId,byte state);
}
