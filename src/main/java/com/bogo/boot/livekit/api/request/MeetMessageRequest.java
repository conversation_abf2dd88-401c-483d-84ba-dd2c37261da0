
package com.bogo.boot.livekit.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.message.annotation.MessageImageFormat;
import com.bogo.boot.message.api.request.ContentFormatRequest;
import com.bogo.boot.message.constant.MessageFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

@Schema(title = "会议聊天消息求体")
@MessageImageFormat(message = "图片消息格式不正确",groups = CreateAction.class)
@Getter
@Setter
public class MeetMessageRequest implements ContentFormatRequest {

	@NotBlank(message = "tag不能为空",groups = CreateAction.class)
	@Schema(title = "房间号")
	private String roomTag;

	@Schema(title =  "消息格式,0:文字 1:图片",example = "0")
	@Range(min = 0,max = 1,message = "消息格式取值范围0,1", groups = CreateAction.class)
	private byte format = MessageFormat.TEXT;

	@Schema(title = "消息内容")
	@NotBlank(message = "消息内容不能为空",groups = CreateAction.class)
	private String content;

	@Override
	public Byte getFormat() {
		return format;
	}

	@Override
	public String getContent() {
		return content;
	}
}
