
package com.bogo.boot.livekit.api;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.livekit.api.request.MeetControlRequest;
import com.bogo.boot.livekit.api.request.MeetCreateRequest;
import com.bogo.boot.livekit.api.request.MeetDingRequest;
import com.bogo.boot.livekit.api.request.MeetInviteRequest;
import com.bogo.boot.livekit.api.request.MeetMessageRequest;
import com.bogo.boot.livekit.api.request.MeetRingRequest;
import com.bogo.boot.livekit.api.vo.MeetingRoomVO;
import com.bogo.boot.livekit.service.LivekitService;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.model.EventMessageCompat;
import com.bogo.boot.message.pusher.LivekitMessagePusher;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/livekit/meeting")
@Tag(name = "Livekit会议接口")
public class LivekitMeetingController {

    @Resource
    private LivekitService livekitService;

    @Resource
    private LivekitMessagePusher livekitMessagePusher;


    @Operation( summary = "创建房间",description = "返回房间Tag")
    @PostMapping(value = "/create")
    public ResponseEntity<String> createRoom(@Parameter(hidden = true) @UID Long uid,@Validated @RequestBody(required = false) MeetCreateRequest request) {
        String roomTag = livekitService.createRoom(uid,request);
        if (roomTag == null){
            return ResponseEntity.make(HttpStatus.INTERNAL_SERVER_ERROR,"创建会议房间失败");
        }
        return ResponseEntity.ok(roomTag);
    }

    @Operation( summary = "获取房间token")
    @Parameter(name = "roomTag", description = "房间Tag", in = ParameterIn.QUERY)
    @GetMapping(value = "/token")
    public ResponseEntity<String> getRoomToken(@Parameter(hidden = true) @UID Long uid,@RequestParam String roomTag) {
        
        String token = livekitService.createToken(uid,roomTag);
        /*
         * 通知自己其他客户端，已经接听
         */
        livekitMessagePusher.push(EventMessageCompat.from()
                .setReceiver(uid)
                .setContent(roomTag)
                .setAction(MessageAction.ACTION_603)
                .setIgnoreCurrentChannel(true));

        return ResponseEntity.ok(token);
    }

    @Operation( summary = "多人会议-发起呼叫",description = "立即唤醒接听")
    @PostMapping(value = "/ring")
    public ResponseEntity<Void> ring(@Validated(CreateAction.class) @RequestBody MeetRingRequest request) {
        livekitService.ring(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "多人会议，预约通知",description = "预约通知，准时入会")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200" ,description = "OK"),
            @ApiResponse(responseCode = "400" ,description = "预约发送失败，提示message字段")
    })
    @PostMapping(value = "/ding")
    public ResponseEntity<Void> ding(@Validated(CreateAction.class) @RequestBody MeetDingRequest request) {
        livekitService.ding(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "拒绝加入")
    @Parameter(name = "roomTag", description = "房间Tag", in = ParameterIn.QUERY)
    @PostMapping(value = "/reject")
    public ResponseEntity<Void> reject(@Parameter(hidden = true) @UID Long uid,@RequestParam String roomTag) {
        livekitService.reject(uid,roomTag);
        return ResponseEntity.ok();
    }

    @Operation( summary = "邀请加入")
    @PostMapping(value = "/invite")
    public ResponseEntity<Void> invite(@Validated(CreateAction.class) @RequestBody MeetInviteRequest request) {
        livekitService.invite(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "踢出会议")
    @PostMapping(value = "/kick-out")
    public ResponseEntity<Void> kickOut(@Validated(CreateAction.class) @RequestBody MeetControlRequest request) {
        livekitService.kickOut(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "关闭成员麦克风",description = "直接关闭对方麦克风")
    @PostMapping(value = "/member/audio-off")
    public ResponseEntity<Void> setMemberMuted(@Validated(CreateAction.class) @RequestBody MeetControlRequest request) {
        livekitService.setMemberMuted(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "开启成员麦克风",description = "提醒对方开启麦克风")
    @PostMapping(value = "/member/audio-on")
    public ResponseEntity<Void> setMemberUnmuted(@Validated(CreateAction.class) @RequestBody MeetControlRequest request) {
        livekitService.setMemberUnmuted(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "关闭成员麦克风",description = "直接关闭对方摄像头")
    @PostMapping(value = "/member/camera-off")
    public ResponseEntity<Void> setMemberCameraOff(@Validated(CreateAction.class) @RequestBody MeetControlRequest request) {
        livekitService.setMemberCameraOff(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "开启成员摄像头",description = "提醒对方开启摄像头")
    @PostMapping(value = "/member/camera-on")
    public ResponseEntity<Void> setMemberCameraOn(@Validated(CreateAction.class) @RequestBody MeetControlRequest request) {
        livekitService.setMemberCameraOn(request);
        return ResponseEntity.ok();
    }
    @Operation( summary = "发聊天消息")
    @PostMapping(value = "/message")
    public ResponseEntity<Void> send(@Validated(CreateAction.class) @RequestBody MeetMessageRequest request) {
        livekitService.send(request);
        return ResponseEntity.ok();
    }

    @Operation( summary = "解除全员禁言")
    @Parameter(name = "roomTag", description = "房间Tag", required = true,in = ParameterIn.QUERY)
    @DeleteMapping(value = "/room/mute")
    public ResponseEntity<Void> setRoomUnmuted(@RequestParam String roomTag) {
        livekitService.setRoomMuted(roomTag,false);
        return ResponseEntity.ok();
    }


    @Operation( summary = "启用全员禁言")
    @Parameter(name = "roomTag", description = "房间Tag", required = true,in = ParameterIn.QUERY)
    @PostMapping(value = "/room/mute")
    public ResponseEntity<Void> setRoomMuted(@RequestParam String roomTag) {
        livekitService.setRoomMuted(roomTag,true);
        return ResponseEntity.ok();
    }
    @Operation( summary = "获取房间信息")
    @Parameter(name = "roomTag", description = "房间Tag", in = ParameterIn.QUERY)
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200" ,description = "OK"),
            @ApiResponse(responseCode = "400" ,description = "房间号不存在，提示message字段")
    })
    @GetMapping(value = "/room")
    public ResponseEntity<MeetingRoomVO> getRoom(@RequestParam String roomTag) {
        MeetingRoomVO roomVo = livekitService.getRoom(roomTag);
        if (roomVo == null){
            return ResponseEntity.make(HttpStatus.BAD_REQUEST,"房间号不存在");
        }
        return ResponseEntity.ok(roomVo);
    }

    @Operation( summary = "结束会议",description = "解散房间")
    @Parameter(name = "roomTag", description = "房间Tag", required = true,in = ParameterIn.QUERY)
    @PostMapping(value = "/finish")
    public ResponseEntity<Void> finish(@RequestParam String roomTag) {
        livekitService.finish(roomTag);
        return ResponseEntity.ok();
    }
}
