
package com.bogo.boot.livekit.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "会议控制请求体")
@Getter
@Setter
public class MeetControlRequest implements Serializable {

	@NotBlank(message = "tag不能为空",groups = CreateAction.class)
	@Schema(title = "房间号")
	private String roomTag;

	@Schema(title = "UID")
	@NotNull(message = "UID不能为空",groups = CreateAction.class)
	private Long uid;

}
