
package com.bogo.boot.livekit.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "P2P创建房间信息")
@Getter
@Setter
public class P2PRoomVO implements Serializable {

	@Schema(title = "房间号")
	private String tag;

	@Schema(title = "房间Token")
	private String token;

	@Schema(title = "产生的事件消息ID")
	private Long messageId;

}
