
package com.bogo.boot.livekit.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "会议房间信息")
@Getter
@Setter
public class MeetingRoomVO implements Serializable {

	@Schema(title = "房间号")
	private String tag;

	@Schema(title = "创建时间(13位时间戳)")
	private Long createAt;

	@Schema(title = "会议主题")
	private String title;

	@Schema(title = "会议详情")
	private String description;

	@Schema(title = "预约会议时间")
	private String dueTime;

	@Schema(title = "创建者UID")
	private long uid;

	@Schema(title = "创建者名称")
	private String name;

	@Schema(title = "是否全员禁言，true:是 false:否")
	private boolean muted;

	@Schema(title = "正在会议中的成员信息，key:用户ID  value:用户名称")
	private Map<Long,String> member;

}
