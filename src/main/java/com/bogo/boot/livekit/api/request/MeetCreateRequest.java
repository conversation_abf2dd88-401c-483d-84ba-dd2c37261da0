
package com.bogo.boot.livekit.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema(title = "会议创建请求体")
@Getter
@Setter
public class MeetCreateRequest implements Serializable {

	@Schema(title = "会议主题")
	@Length(max = 128,message = "主题长度不能超过128个字符")
	private String title;

	@Schema(title = "会议详情")
	private String description;

	@Schema(title = "会议开始时间。如：2023-12-20 15:30")
	private String dueTime;

}
