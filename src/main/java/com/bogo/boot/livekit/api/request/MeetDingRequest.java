
package com.bogo.boot.livekit.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "会议通知请求体")
@Getter
@Setter
public class MeetDingRequest implements Serializable {

	@Schema(title = "成员信息，key:用户ID  value:用户名称")
	@Size(min = 1, message = "成员信息不能空", groups = CreateAction.class)
	private Map<Long,String> member;

	@Schema(title = "房间号")
	@NotBlank(message = "房间号不能为空", groups = CreateAction.class)
	private String roomTag;

}
