
package com.bogo.boot.livekit.api;

import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.livekit.api.vo.P2PRoomVO;
import com.bogo.boot.livekit.model.P2PCallRequest;
import com.bogo.boot.livekit.service.LivekitService;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.pusher.LivekitMessagePusher;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Pattern;
import java.util.function.BiPredicate;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/livekit/p2p")
@Tag(name = "Livekit单人通话接口" )
public class LivekitP2PController {

	@Resource
	private BiPredicate<Long,Long> friendshipPredicate;

	@Resource
	private LivekitMessagePusher livekitMessagePusher;


	@Resource
	private LivekitService livekitService;


	@Schema( title = "发起单人通话")
	@Parameters({
			@Parameter(name = "targetId", description = "对方用户ID", in = ParameterIn.QUERY),
			@Parameter(name = "type", description = "通话类型,audio或者video", in = ParameterIn.QUERY),
	})
	@ApiResponses(value = {@ApiResponse(responseCode = "200" ,description = "OK"),@ApiResponse(responseCode = "403" ,description = "相互不是好友或没在同组织")})
	@PostMapping(value = {"/call"})
	public ResponseEntity<P2PRoomVO> call(@Parameter(hidden = true) @UID Long uid,
										   @RequestParam Long targetId,
										   @Validated @Pattern(regexp = "audio|video",message = "类型取值范围为[audio或者video]") @RequestParam String type) {

		if (!friendshipPredicate.test(uid,targetId)){
			return ResponseEntity.make(HttpStatus.FORBIDDEN,"相互不是好友或没在同组织");
		}

		String roomTag = livekitService.createRoom(uid);

		if (roomTag == null){
			return ResponseEntity.make(HttpStatus.INTERNAL_SERVER_ERROR,"发起呼叫失败");
		}

		P2PCallRequest request = new P2PCallRequest();
		request.setRoomTag(roomTag);
		request.setType(type);

		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_900);
		message.setSender(uid);
		message.setReceiver(targetId);
		message.setContent(request.toString());

		livekitMessagePusher.push(message);

		P2PRoomVO roomVO = new P2PRoomVO();
		roomVO.setTag(roomTag);
		roomVO.setToken(livekitService.createToken(uid,roomTag));
		roomVO.setMessageId(message.getId());

		return ResponseEntity.ok(roomVO);
	}


	@Schema( title = "拒绝通话")
	@Parameter(name = "targetId", description = "对方用户ID", in = ParameterIn.QUERY)
	@PostMapping(value =  {"/reject"})
	public ResponseEntity<Void> reject(@Parameter(hidden = true) @UID Long uid,
									   @RequestParam Long targetId) {

		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_903);
		message.setSender(uid);
		message.setReceiver(targetId);
		livekitMessagePusher.push(message);

		return ResponseEntity.ok();
	}

	@Schema( title = "接受通话",description = "返回房间Token")
	@Parameters({
			@Parameter(name = "targetId", description = "对方用户ID", in = ParameterIn.QUERY),
			@Parameter(name = "roomTag", description = "房间Tag", required = true,in = ParameterIn.QUERY)
	})
	@PostMapping(value =  {"/accept"})
	public ResponseEntity<String> accept(@Parameter(hidden = true) @UID Long uid,
										 @RequestParam Long targetId,
									     @RequestParam String roomTag) {

		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_902);
		message.setSender(uid);
		message.setReceiver(targetId);
		livekitMessagePusher.push(message);

		String roomToken = livekitService.createToken(uid,roomTag);

		return ResponseEntity.ok(roomToken);
	}

	@Schema( title = "反馈正忙")
	@Parameter(name = "targetId", description = "对方用户ID", in = ParameterIn.QUERY)
	@PostMapping(value =  {"/busy"})
	public ResponseEntity<Void> busy(@Parameter(hidden = true) @UID Long uid,
									 @RequestParam Long targetId) {

		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_904);
		message.setSender(uid);
		message.setReceiver(targetId);
		livekitMessagePusher.push(message);

		return ResponseEntity.ok();
	}


	@Schema( title = "取消呼叫")
	@Parameter(name = "targetId", description = "对方用户ID", in = ParameterIn.QUERY)
	@PostMapping(value =  {"/cancel"})
	public ResponseEntity<Void> cancel(@Parameter(hidden = true) @UID Long uid,
									   @RequestParam Long targetId) {

		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_906);
		message.setSender(uid);
		message.setReceiver(targetId);
		livekitMessagePusher.push(message);

		return ResponseEntity.ok();
	}


	@Schema( title = "通知已经响铃")
	@Parameter(name = "targetId", description = "主叫用户ID", in = ParameterIn.QUERY)
	@PostMapping(value =  {"/ring"})
	public ResponseEntity<Void> ring(@Parameter(hidden = true) @UID Long uid,
									 @RequestParam Long targetId) {

		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_932);
		message.setSender(uid);
		message.setReceiver(targetId);
		livekitMessagePusher.push(message);

		return ResponseEntity.ok();
	}

	@Schema( title = "结束通话",description = "主被叫均可挂断通话")
	@Parameters({
			@Parameter(name = "targetId", description = "对方用户ID", in = ParameterIn.QUERY),
			@Parameter(name = "roomTag", description = "房间Tag", required = true,in = ParameterIn.QUERY)
	})
	@PostMapping(value = "/finish")
	public ResponseEntity<Void> finish(@Parameter(hidden = true) @UID Long uid,
									   @RequestParam Long targetId,
									   @RequestParam String roomTag) {

		livekitService.finish(roomTag);

		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_905);
		message.setSender(uid);
		message.setReceiver(targetId);
		livekitMessagePusher.push(message);

		return ResponseEntity.ok();
	}

}
