
package com.bogo.boot.livekit.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 会议房间信息
 */
@Entity
@Table(name = "t_bogo_meeting")
@Getter
@Setter
public class Meeting {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "uid", nullable = false)
	private Long uid;

	@Column(name = "tag")
	private String tag;

	@Column(name = "title")
	private String title;

	@Column(name = "description")
	private String description;

	@Column(name = "due_time")
	private String dueTime;

	@Column(name = "create_time")
	private Date createTime;
}
