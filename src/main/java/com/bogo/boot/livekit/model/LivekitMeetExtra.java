package com.bogo.boot.livekit.model;

import com.bogo.boot.infra.util.JSON;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LivekitMeetExtra {

    private String name;

    private String dueTime;

    private String description;

    private Map<Long,String> member;

    @Override
    public String toString(){
        return JSON.toJSONString(this);
    }
}
