package com.bogo.boot.livekit.model;

import com.bogo.boot.infra.util.JSON;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class RoomMetadata {

    private boolean muted;

    public static RoomMetadata of(String metadata){
        if (StringUtils.isBlank(metadata)){
            return new RoomMetadata();
        }
        return JSON.parse(metadata,RoomMetadata.class);
    }

    @Override
    public String toString(){
        return JSON.toJSONString(this);
    }
}
