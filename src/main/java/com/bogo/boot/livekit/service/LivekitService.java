package com.bogo.boot.livekit.service;

import com.bogo.boot.livekit.api.request.MeetControlRequest;
import com.bogo.boot.livekit.api.request.MeetCreateRequest;
import com.bogo.boot.livekit.api.request.MeetDingRequest;
import com.bogo.boot.livekit.api.request.MeetInviteRequest;
import com.bogo.boot.livekit.api.request.MeetMessageRequest;
import com.bogo.boot.livekit.api.request.MeetRingRequest;
import com.bogo.boot.livekit.api.vo.MeetingRoomVO;

public interface LivekitService {

    String createRoom(long uid);

    String createRoom(long uid, MeetCreateRequest request);

    String createToken(long uid, String roomTag);

    void ring(MeetRingRequest request);

    void ding(MeetDingRequest request);

    void reject(long uid, String roomTag);

    void kickOut(MeetControlRequest request);

    void setMemberMuted(MeetControlRequest request);

    void setMemberUnmuted(MeetControlRequest request);

    void setMemberCameraOn(MeetControlRequest request);
    void setMemberCameraOff(MeetControlRequest request);

    void invite(MeetInviteRequest request);

    void finish(String roomTag);

    void setRoomMuted(String roomTag, boolean muted);

    MeetingRoomVO getRoom(String roomTag);

    void send(MeetMessageRequest request);

}
