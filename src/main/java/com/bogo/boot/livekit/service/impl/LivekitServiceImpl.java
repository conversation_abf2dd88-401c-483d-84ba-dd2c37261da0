package com.bogo.boot.livekit.service.impl;

import com.bogo.boot.account.service.UserService;
import com.bogo.boot.infra.configuration.properties.LivekitProperties;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.livekit.api.request.MeetControlRequest;
import com.bogo.boot.livekit.api.request.MeetCreateRequest;
import com.bogo.boot.livekit.api.request.MeetDingRequest;
import com.bogo.boot.livekit.api.request.MeetInviteRequest;
import com.bogo.boot.livekit.api.request.MeetMessageRequest;
import com.bogo.boot.livekit.api.request.MeetRingRequest;
import com.bogo.boot.livekit.api.vo.MeetingRoomVO;
import com.bogo.boot.livekit.constant.RoomActon;
import com.bogo.boot.livekit.entity.Meeting;
import com.bogo.boot.livekit.model.LivekitMeetExtra;
import com.bogo.boot.livekit.model.RoomMessage;
import com.bogo.boot.livekit.model.RoomMetadata;
import com.bogo.boot.livekit.repository.MeetingRepository;
import com.bogo.boot.livekit.service.LivekitService;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.model.EventMessageCompat;
import com.bogo.boot.message.pusher.LivekitMessagePusher;
import io.livekit.server.AccessToken;
import io.livekit.server.RoomJoin;
import io.livekit.server.RoomName;
import io.livekit.server.RoomServiceClient;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import livekit.LivekitModels;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@Service
public class LivekitServiceImpl implements LivekitService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LivekitServiceImpl.class);

    private static final int ROOM_EMPTY_TIMEOUT = (int) Duration.ofDays(360).toMillis();

    private static final long ROOM_TOKEN_AGE = Duration.ofDays(360).toMillis();

    private final LivekitProperties properties;

    private final RoomServiceClient serviceClient;

    @Resource
    private LivekitMessagePusher livekitMessagePusher;

    @Resource
    private UserService userService;

    @Resource
    private MeetingRepository meetingRepository;

    @Autowired
    public LivekitServiceImpl(LivekitProperties properties) {
        this.properties = properties;
        this.serviceClient = RoomServiceClient.createClient(
                properties.getUri(),
                properties.getAppId(),
                properties.getSecret());
    }

    @Override
    public String createRoom(long uid) {

        String roomTag = UUID.randomUUID().toString();

        Call<LivekitModels.Room> call = serviceClient.createRoom(roomTag, (int) Duration.ofHours(1).toMillis(),2,null,null);

        try {
            Response<LivekitModels.Room> response = call.execute();
            if (response.isSuccessful()){
                return roomTag;
            }
            if (response.errorBody() != null){
                LOGGER.error("创建P2P通话失败,uid:{},response:{}",uid,response.errorBody().string());
            }
        } catch (Exception e) {
            LOGGER.error("创建P2P通话失败,uid:{}",uid,e);
        }
        return null;
    }

    @Override
    public String createRoom(long uid, MeetCreateRequest request) {
        String roomTag = String.format("%s-%s-%s",
                Long.toUnsignedString(uid,16),
                Long.toUnsignedString(Long.parseLong(DateFormatUtils.format(new Date(),"yyMMdd")),16),
                Long.toUnsignedString(Long.parseLong(DateFormatUtils.format(new Date(),"HHmmss")),16)
                ).toUpperCase();
        Call<LivekitModels.Room> call = serviceClient.createRoom(roomTag, ROOM_EMPTY_TIMEOUT,null,null,null);

        try {
            Response<LivekitModels.Room> response = call.execute();
            if (response.isSuccessful()){
                Meeting meeting = new Meeting();
                meeting.setUid(uid);
                meeting.setTag(roomTag);
                meeting.setTitle(request == null ? null : request.getTitle());
                meeting.setDescription(request == null ? null :request.getDescription());
                meeting.setDueTime(request == null ? null :request.getDueTime());
                meeting.setCreateTime(new Date());
                meetingRepository.saveAndFlush(meeting);
                return roomTag;
            }
            if (response.errorBody() != null){
                LOGGER.error("创建会议房间失败,uid:{},response:{}",uid,response.errorBody().string());
            }
        } catch (Exception e) {
            LOGGER.error("创建会议房间失败,uid:{}",uid,e);
        }
        return null;
    }


    @Override
    public String createToken(long uid,String roomTag) {
        String name = userService.findName(uid);
        AccessToken token = new AccessToken(properties.getAppId(), properties.getSecret());
        token.setName(name);
        token.setIdentity(String.valueOf(uid));
        token.addGrants(new RoomJoin(true), new RoomName(roomTag));
        token.setTtl(ROOM_TOKEN_AGE);

        return token.toJwt();
    }

    @Override
    public void ring(MeetRingRequest request) {

        Map<Long, String> member = request.getMember();
        member.remove(UidHolder.getUid());
        if (member.isEmpty()){
            return;
        }

        Message message = new EventMessage();
        message.setAction(MessageAction.ACTION_600);
        message.setSender(UidHolder.getUid());
        message.setTitle(meetingRepository.getTitle(request.getRoomTag()));
        message.setContent(request.getRoomTag());

        livekitMessagePusher.push(message, member);
    }


    @Override
    public void ding(MeetDingRequest request) {

        Meeting meeting = meetingRepository.findOne(request.getRoomTag());

        Message message = new EventMessage();
        message.setAction(MessageAction.ACTION_601);
        message.setSender(meeting.getUid());
        message.setTitle(meeting.getTitle());
        message.setContent(request.getRoomTag());

        LivekitMeetExtra extra = new LivekitMeetExtra();
        extra.setName(userService.findName(meeting.getUid()));
        extra.setMember(request.getMember());
        extra.setDescription(meeting.getDescription());
        extra.setDueTime(meeting.getDueTime());

        message.setExtra(extra.toString());

        livekitMessagePusher.push(message, request.getMember());
    }

    @Override
    public void reject(long uid, String roomTag) {

        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.REJECT.name());
        message.setFromUid(UidHolder.getUid());

        this.sendRoomData(roomTag,message);



        /*
         * 通知自己其他客户端，已经拒接
         */
        livekitMessagePusher.push(EventMessageCompat.from()
                .setReceiver(uid)
                .setContent(roomTag)
                .setAction(MessageAction.ACTION_604)
                .setIgnoreCurrentChannel(true));

    }

    @Override
    public void setRoomMuted(String roomTag, boolean muted) {

        RoomMetadata metadata = new RoomMetadata();
        metadata.setMuted(muted);
        serviceClient.updateRoomMetadata(roomTag,metadata.toString()).enqueue(new Callback<>() {
            @Override
            public void onResponse(@NotNull Call<LivekitModels.Room> call, @NotNull Response<LivekitModels.Room> response) {
            }

            @Override
            public void onFailure(@NotNull Call<LivekitModels.Room> call, @NotNull Throwable throwable) {
            }
        });
    }

    @Override
    public void kickOut(MeetControlRequest request) {

        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.REMOVE.name());
        message.setFromUid(UidHolder.getUid());
        this.sendRoomData(request.getRoomTag(),message,request.getUid());

        serviceClient.removeParticipant(request.getRoomTag(),request.getUid().toString());

        RoomMessage leaveMessage = new RoomMessage();
        leaveMessage.setAction(RoomActon.LEAVE.name());
        leaveMessage.setFromUid(UidHolder.getUid());
        leaveMessage.setContent(request.getUid().toString());
        this.sendRoomData(request.getRoomTag(),leaveMessage);
    }

    @Override
    public void setMemberUnmuted(MeetControlRequest request) {
        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.AUDIO_ON.name());
        message.setFromUid(UidHolder.getUid());

        this.sendRoomData(request.getRoomTag(),message,request.getUid());
    }

    @Override
    public void setMemberMuted(MeetControlRequest request) {
        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.AUDIO_OFF.name());
        message.setFromUid(UidHolder.getUid());

        this.sendRoomData(request.getRoomTag(),message,request.getUid());
    }

    @Override
    public void setMemberCameraOn(MeetControlRequest request) {
        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.CAMERA_ON.name());
        message.setFromUid(UidHolder.getUid());

        this.sendRoomData(request.getRoomTag(),message,request.getUid());
    }

    @Override
    public void setMemberCameraOff(MeetControlRequest request) {
        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.CAMERA_OFF.name());
        message.setFromUid(UidHolder.getUid());

        this.sendRoomData(request.getRoomTag(),message,request.getUid());
    }


    @Override
    public void invite(MeetInviteRequest request) {

        MeetRingRequest ringRequest = new MeetRingRequest();
        ringRequest.setMember(request.getMember());
        ringRequest.setRoomTag(request.getRoomTag());

        /* 呼叫被邀请人 */
        this.ring(ringRequest);

        /* 给在房间的人推送邀请信息 */

        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.INVITE.name());
        message.setFromUid(UidHolder.getUid());
        message.setContent(JSON.toJSONString(request.getMember()));

        this.sendRoomData(request.getRoomTag(),message);

    }

    @Override
    public void finish(String roomTag) {
        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.FINISH.name());
        message.setFromUid(UidHolder.getUid());

        serviceClient.sendData(roomTag,
                        message.toString().getBytes(),
                        LivekitModels.DataPacket.Kind.RELIABLE)
                .enqueue(new Callback<>() {
                    @Override
                    public void onResponse(@NotNull Call<Void> call, @NotNull Response<Void> response) {
                        serviceClient.deleteRoom(roomTag).enqueue(voidCallback);
                    }

                    @Override
                    public void onFailure(@NotNull Call<Void> call, @NotNull Throwable throwable) {
                    }
                });

    }

    @Override
    public MeetingRoomVO getRoom(String roomTag){

        Meeting meeting = meetingRepository.findOne(roomTag);
        if (meeting == null){
            return null;
        }

        MeetingRoomVO roomVO = new MeetingRoomVO();
        roomVO.setUid(meeting.getUid());
        roomVO.setName(userService.findName(meeting.getUid()));
        roomVO.setTag(roomTag);
        roomVO.setTitle(meeting.getTitle());
        roomVO.setDescription(meeting.getDescription());
        roomVO.setDueTime(meeting.getDueTime());
        roomVO.setCreateAt(meeting.getCreateTime().getTime());
        Call<List<LivekitModels.Room>> roomCall = serviceClient.listRooms(Collections.singletonList(roomTag));
        try {

            Response<List<LivekitModels.Room>> roomResponse = roomCall.execute();
            if (CollectionUtils.isEmpty(roomResponse.body())){
                roomVO.setMuted(false);
            }else {
                LivekitModels.Room room = roomResponse.body().get(0);
                RoomMetadata metadata = RoomMetadata.of(room.getMetadata());
                roomVO.setMuted(metadata.isMuted());
                roomVO.setMember(getRoomMembers(room));
            }

            return roomVO;

        }catch (Exception throwable){
            LOGGER.error("查询房间失败,roomTag:{}",roomTag,throwable);
        }

        return null;
    }

    @Override
    public void send(MeetMessageRequest request) {

        RoomMessage message = new RoomMessage();
        message.setAction(RoomActon.MESSAGE.name());
        message.setFromUid(UidHolder.getUid());
        message.setFormat(request.getFormat());
        message.setContent(request.getContent());

        this.sendRoomData(request.getRoomTag(),message);
    }


    private void sendRoomData(String roomTag,RoomMessage message){
       this.sendRoomData(roomTag,message,null);
    }

    private void sendRoomData(String roomTag,RoomMessage message,Long uid){
        if (uid == null){
            serviceClient.sendData(roomTag,
                            message.toString().getBytes(),
                            LivekitModels.DataPacket.Kind.RELIABLE)
                    .enqueue(voidCallback);
        }else {
            serviceClient.sendData(roomTag,
                            message.toString().getBytes(),
                            LivekitModels.DataPacket.Kind.RELIABLE,
                            Collections.emptyList(),
                            Collections.singletonList(uid.toString())
                    )
                    .enqueue(voidCallback);
        }
    }

    private Map<Long,String> getRoomMembers(LivekitModels.Room room){
        if (room == null || room.getNumParticipants() == 0){
            return Collections.emptyMap();
        }

        Call<List<LivekitModels.ParticipantInfo>> membersCall = serviceClient.listParticipants(room.getName());
        try {
            Response<List<LivekitModels.ParticipantInfo>> memberResponse = membersCall.execute();
            if (memberResponse.body() != null){
                return (memberResponse.body().stream().collect(Collectors.toMap(participantInfo -> NumberUtils.toLong(participantInfo.getIdentity()), LivekitModels.ParticipantInfo::getName)));
            }
        }catch (Exception exception){
            LOGGER.error("查询房间成员信息失败,tag:{} ",room.getName(),exception);
        }
        return Collections.emptyMap();
    }

    private final Callback<Void> voidCallback = new Callback<>() {
        @Override
        public void onResponse(Call<Void> call, @Nonnull Response<Void> response) {
            LOGGER.info("接口调用成功,uri:{} ,response:{}", call.request().url(), response);
        }

        @Override
        public void onFailure(Call<Void> call, @Nonnull Throwable throwable) {
            LOGGER.error("接口调用失败,uri:{}", call.request().url(), throwable);
        }
    };

}
