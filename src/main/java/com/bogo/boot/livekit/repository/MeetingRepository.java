
package com.bogo.boot.livekit.repository;

import com.bogo.boot.livekit.entity.Meeting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MeetingRepository extends JpaRepository<Meeting, Long> {

	@Query("from Meeting where tag= ?1 ")
	Meeting findOne(String tag);

	@Query("select title from Meeting where tag= ?1 ")
	String getTitle(String tag);

}
