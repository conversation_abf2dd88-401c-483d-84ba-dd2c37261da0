package com.bogo.boot.infra.util;

import org.springframework.security.crypto.bcrypt.BCrypt;

public class Password {

    /**
     * 生成加密密码，统一将客户端传的MD5串先转换大写
     * @param md5Hash
     * @return
     */
    public static String create(String md5Hash){
        return BCrypt.hashpw(md5Hash.toUpperCase().getBytes(), BCrypt.gensalt(4));
    }

    /**
     * 验证密码，统一将客户端传的MD5串先转换大写
     * @param md5Hash 客户端传的MD5密码
     * @param hashedPassword 数据库存的加密密码
     * @return
     */
    public static boolean check(String md5Hash, String hashedPassword){
        return BCrypt.checkpw(md5Hash.toUpperCase(), hashedPassword);
    }
}
