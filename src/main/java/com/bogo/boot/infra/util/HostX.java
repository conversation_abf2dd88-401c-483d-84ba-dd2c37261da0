
package com.bogo.boot.infra.util;

import java.net.InetAddress;
import java.net.UnknownHostException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 获取当前服务器内网IP地址
 */
public class HostX {

	private static final String DEFAULT_HOST = "127.0.0.1";

	private static final Logger LOGGER = LoggerFactory.getLogger(HostX.class);

	public static String getLocalHost(){
		try {
			return InetAddress.getLocalHost().getHostAddress();
		} catch (UnknownHostException e) {
			LOGGER.warn("获取本机IP地址失败,vi /etc/hosts 新增映射 {当前IP} {hostname}",e);
			return DEFAULT_HOST;
		}
	}
}
