
package com.bogo.boot.infra.util;

import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

public class PageX {

	public static  <R,T> Page<R> map(Function<T,R> mapper,Page<T> page) {
		return new PageImpl<>(page.getContent().stream().map(mapper).collect(Collectors.toList()),
				page.getPageable(),
				page.getTotalElements());
	}


	public static  <R,T> Page<R> map(Function<T,R> mapper, List<T> list, Pageable pageable,long count) {
		return new PageImpl<>(list.stream().map(mapper).collect(Collectors.toList()),
				pageable,
				count);
	}

	public static  <R,T> List<R> map(Function<T,R> mapper, List<T> list) {
		return list.stream().map(mapper).collect(Collectors.toList());
	}

	public static  <T> List<T> filter(Predicate<T> filter, List<T> list) {
		return list.stream().filter(filter).collect(Collectors.toList());
	}

	public static  <T,M> List<M> filter(Predicate<T> filter, List<T> list,Function<T,M> mapper) {
		return list.stream().filter(filter).map(mapper).collect(Collectors.toList());
	}
}
