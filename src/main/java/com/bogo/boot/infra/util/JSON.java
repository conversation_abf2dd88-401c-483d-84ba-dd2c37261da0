
package com.bogo.boot.infra.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.json.JsonMapper;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class JSON {
    private static final JsonMapper OBJECT_MAPPER = JsonMapper.builder()
            .enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS)
            .enable(JsonReadFeature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .build();


    public static String toJSONString(Object data) {

        try {
            return OBJECT_MAPPER.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }

    }

    public static <T> T parse(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            throw new IllegalArgumentException(e);
        }
    }

    public static <T> T parseNullable(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()){
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (IOException ignore) {
            return null;
        }
    }

    public static <T> T parseNullable(byte[] data, Class<T> clazz) {
        if (data == null){
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(data, clazz);
        } catch (IOException e) {
            return null;
        }
    }


    public static <T> T parse(byte[] data, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(data, clazz);
        } catch (IOException e) {
            throw new IllegalArgumentException(e);
        }
    }


    public static <T> List<T> parseList(String json, Class<T> clazz) {
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructCollectionLikeType(List.class,clazz);
        try {
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            throw new IllegalArgumentException(e);
        }
    }

    public static <K,V> Map<K,V> toMap(String json, Class<K> keyClazz, Class<V> vClazz) {
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructMapType(HashMap.class,keyClazz,vClazz);
        try {
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            return Collections.emptyMap();
        }
    }

    public static <T> List<T> parseListNullable(String json, Class<T> clazz) {
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructCollectionLikeType(List.class,clazz);
        try {
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (IOException ignore) {
            return Collections.emptyList();
        }
    }
}
