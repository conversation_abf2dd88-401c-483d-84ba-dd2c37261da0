package com.bogo.boot.infra.util;


import jakarta.annotation.Resource;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class ApplicationEventProducer {

    @Resource
    private ApplicationContext applicationContext;

    public void publish(ApplicationEvent event) {
        this.applicationContext.publishEvent(event);
    }

    @Async("eventTaskExecutor")
    public void publishAsync(ApplicationEvent event) {
        this.applicationContext.publishEvent(event);
    }
}
