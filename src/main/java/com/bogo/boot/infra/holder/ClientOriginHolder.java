package com.bogo.boot.infra.holder;


import com.bogo.boot.infra.model.Origin;

public class ClientOriginHolder {

    private ClientOriginHolder(){}

    private static final ThreadLocal<Origin> holder = new InheritableThreadLocal<>();

    public static Origin get(){
        return holder.get() == null ? Origin.UNKNOWN : holder.get();
    }

    public static String getAppChannel(){
        return get().getAppChannel();
    }

    public static void set(Origin origin){
        holder.set(origin);
    }

    public static void remove(){
        holder.remove();
    }
}
