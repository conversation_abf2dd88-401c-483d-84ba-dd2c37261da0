package com.bogo.boot.infra.holder;

import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class EnvironmentHolder {

    private static final String DEV = "dev";

    private static final String PRO = "pro";

    private final String env;


    @Autowired
    public EnvironmentHolder(@Value("${spring.profiles.active}") String env){
        this.env = env;
    }

    public boolean isDev(){
        return Objects.equals(env,DEV);
    }


    public boolean isPro(){
        return Objects.equals(env,PRO);
    }

    public String getEnvName(){
        return env;
    }

    public boolean eq(String env){
        return Objects.equals(env,this.env);
    }

}
