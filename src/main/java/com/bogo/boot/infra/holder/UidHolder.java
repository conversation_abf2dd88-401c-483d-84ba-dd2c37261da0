package com.bogo.boot.infra.holder;


import java.util.Objects;

public class UidHolder {

    private UidHolder(){}

    private static final ThreadLocal<Long> holder = new InheritableThreadLocal<>();

    public static Long getUid(){
        return holder.get();
    }


    public static void setUid(long uid){
        holder.set(uid);
    }

    public static void remove(){
        holder.remove();
    }


    public static boolean isCurrentUid(long uid){
        return Objects.equals(uid,getUid());
    }

    public static boolean isNotCurrentUid(long uid){
        return !isCurrentUid(uid);
    }
}
