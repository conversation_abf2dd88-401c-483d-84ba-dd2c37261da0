package com.bogo.boot.infra.api.open;

import com.bogo.boot.group.api.request.RobotMessageRequest;
import com.bogo.boot.group.service.GroupRobotService;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/open/robot")
@Tag(name = "群机器人消息接口" )
public class OpenRobotController {

    @Resource
    private GroupRobotService groupRobotService;

    @Operation( summary = "发送机器人消息",description = "公共接口")
    @Parameter(name = "uuid", description = "机器人UUID", in = ParameterIn.QUERY,example = "0")
    @PostMapping(value = "/message")
    public ResponseEntity<?> send(@RequestParam String uuid, @Validated(CreateAction.class) @RequestBody RobotMessageRequest request) {
        return groupRobotService.send(uuid,request);
    }
}
