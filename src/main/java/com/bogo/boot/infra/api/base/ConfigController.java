
package com.bogo.boot.infra.api.base;

import com.bogo.boot.infra.annotation.GreenFunction;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.service.ConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/config")
@Tag(name = "参数配置接口" )
public class ConfigController {

	@Resource
	private ConfigService configService;

	@Operation( summary = "获取指定域下的参数")
	@Parameter(name = "domain", description = "域", in = ParameterIn.PATH)
	@GetMapping(value = "/map/{domain}")
	@GreenFunction
	public ResponseEntity<Map<String, String>> map(@PathVariable String domain) {
		return ResponseEntity.ok(configService.queryMap(domain));
	}

}
