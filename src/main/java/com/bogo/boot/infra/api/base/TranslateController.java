
package com.bogo.boot.infra.api.base;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.GreenFunction;
import com.bogo.boot.infra.feign.request.TextTranslateRequest;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.service.TranslateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/translate")
@Tag(name = "文字翻译接口" )
public class TranslateController {

	@Resource
	private TranslateService translateService;

	@Operation( summary = "翻译文本")
	@Parameters({
			@Parameter(name = "text", description = "待翻译文本", in = ParameterIn.QUERY),
			@Parameter(name = "language", description = "目标语言", in = ParameterIn.QUERY)
	})
	@GreenFunction
	@GetMapping(value = "/text")
	public ResponseEntity<String> translate(@RequestParam String text,
											@RequestParam String language) {
		TextTranslateRequest request = new TextTranslateRequest();
		request.setText(text);
		request.setLanguage(language);
		return ResponseEntity.ok(translateService.translate(request));
	}

	@Operation( summary = "翻译文本")
	@GreenFunction
	@PostMapping(value = "/text")
	public ResponseEntity<String> translate(@Validated(CreateAction.class) @RequestBody TextTranslateRequest request) {
		return ResponseEntity.ok(translateService.translate(request));
	}
}
