
package com.bogo.boot.infra.api.inner;

import com.bogo.boot.group.constant.GroupState;
import com.bogo.boot.group.service.GroupMemberService;
import com.bogo.boot.group.service.GroupNoticeService;
import com.bogo.boot.group.service.GroupService;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("InnerGroupController")
@RequestMapping("/inner/group")
@Tag(name = "内部群组接口" )
public class GroupController {

	@Resource
	private GroupService groupService;

	@Resource
	private GroupNoticeService groupNoticeService;

	@Resource
	private GroupMemberService groupMemberService;

	@Operation( summary = "解散群组")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> disband(@PathVariable long id) {
		groupService.delete(id);
		return ResponseEntity.ok();
	}

	@Operation( summary = "群组禁言")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@PostMapping(value = "/block/{id}")
	public ResponseEntity<Void> block(@PathVariable Long id) {
		groupService.updateState(id, GroupState.BLOCK);
		return ResponseEntity.ok();
	}

	@Operation( summary = "解除群组禁言")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/block/{id}")
	public ResponseEntity<Void> unblock(@PathVariable Long id) {
		groupService.updateState(id, GroupState.NORMAL);
		return ResponseEntity.ok();
	}

	@Operation( summary = "发布公告")
	@PostMapping(value = "/notice")
	public ResponseEntity<Void> updateNotice(@RequestParam long id,@RequestParam String notice) {
		groupNoticeService.update(id, notice);
		return ResponseEntity.ok();
	}

	@Operation( summary = "删除成员")
	@Parameter(name = "id", description = "成员ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/member/{id}")
	public ResponseEntity<Void> deleteMember(@PathVariable long id) {
		groupMemberService.delete(id);
		return ResponseEntity.ok();
	}

}
