
package com.bogo.boot.infra.api.base;

import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.api.base.request.FeedbackRequest;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/feedback")
@Tag(name = "用户反馈接口" )
public class FeedbackController {

	@Operation( summary = "提交反馈")
	@PostMapping(value = "")
	public ResponseEntity<Void> map(@Parameter(hidden = true) @UID Long uid,@RequestBody FeedbackRequest request) {
		return ResponseEntity.ok();
	}

}
