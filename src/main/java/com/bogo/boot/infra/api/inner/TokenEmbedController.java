
package com.bogo.boot.infra.api.inner;

import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.service.AccessTokenService;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.api.inner.request.TokenAddRequest;
import com.bogo.boot.infra.api.inner.request.TokenRemoveRequest;
import com.bogo.boot.infra.model.Origin;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/inner/token")
@Tag(name = "内部共享Token接口" )
public class TokenEmbedController {

	@Resource
	private UserService userService;

	@Resource
	private AccessTokenService accessTokenService;

	@Operation( summary = "免登录Token同步接口",description = "使用现有系统的token认证，调用此内部接口，设置用户的token信息用于免登录鉴权")
	@PostMapping(value = "/add", produces = "application/json")
	public ResponseEntity<Void> add(@Validated(value = CreateAction.class) @RequestBody TokenAddRequest request) {

		User user = userService.findOne(request.getTelephone());
		if(user == null) {
			return ResponseEntity.make(HttpStatus.BAD_REQUEST, "用户不存在:" + request.getTelephone());
		}

		Origin origin = new Origin();
		origin.setType(request.getTerminalId());

		accessTokenService.generate(origin,user.getId(), request.getToken(), request.getExpireAt());

		return ResponseEntity.ok();
	}

	@Operation( summary = "清除免登录Token接口",description = "使用此接口清除共享Token")
	@PostMapping(value = "/remove", produces = "application/json")
	public ResponseEntity<Void> remove(@Validated(value = CreateAction.class) @RequestBody TokenRemoveRequest request) {

		Origin origin = new Origin();
		origin.setType(request.getTerminalId());

		accessTokenService.delete(origin,request.getToken());

		return ResponseEntity.ok();
	}
}
