
package com.bogo.boot.infra.api.inner.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;


@Schema(title =  "管理后台用户事件信息")
@Getter
@Setter
public class UserEventRequest {

	@Schema(title =  "UID")
	@NotNull(message = "用户ID不可为空",groups = CreateAction.class)
	private Long uid;

	@Schema(title = "消息类型")
	@NotBlank(message = "action不可为空",groups = CreateAction.class)
	private String action;

	@Schema(title = "内容")
	protected String content;

}
