
package com.bogo.boot.infra.api.inner.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;


@Schema(title =  "系统内用户新增请求")
@Getter
@Setter
public class UserAddRequest {

	@Schema(title = "名称")
	@NotBlank(message = "name不能为空",groups = CreateAction.class)
	private String name;

	@Schema(title = "手机号码")
	@NotBlank(message = "telephone不能为空",groups = CreateAction.class)
	private String telephone;

	@Schema(title = "密码(md5)")
	@NotBlank(message = "password不能为空",groups = CreateAction.class)
	private String password;

	@Schema(title = "性别 0女 1男")
	private Byte gender;

}
