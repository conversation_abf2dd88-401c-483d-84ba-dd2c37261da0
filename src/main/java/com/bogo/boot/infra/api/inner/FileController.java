
package com.bogo.boot.infra.api.inner;

import com.bogo.boot.file.service.FileStoreService;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

@RestController
@RequestMapping("/inner/file")
@Tag(name = "文件存储接口" )
public class FileController extends ResourceHttpRequestHandler {

	@Resource
	private FileStoreService fileStoreService;

	@Operation( summary = "上传文件")
	@Parameters({
			@Parameter(name = "bucket", description = "文件库名", in = ParameterIn.PATH),
			@Parameter(name = "key", description = "文件KEY", in = ParameterIn.PATH),
	})
	@PostMapping(value = "/{bucket}/{key:.+}",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
	public ResponseEntity<Void> upload(@RequestPart("file") MultipartFile file, @PathVariable String bucket, @PathVariable String key) {
		fileStoreService.upload(file, bucket, key);
		return ResponseEntity.ok();
	}

	@Operation( summary = "删除文件")
	@Parameters({
			@Parameter(name = "bucket", description = "文件库名", in = ParameterIn.PATH),
			@Parameter(name = "key", description = "文件KEY", in = ParameterIn.PATH)
	})
	@DeleteMapping(value = "/{bucket}/{key:.+}")
	public ResponseEntity<Void> delete(@PathVariable String bucket, @PathVariable String key) {
		fileStoreService.delete(bucket, key);
		return ResponseEntity.ok();
	}

}
