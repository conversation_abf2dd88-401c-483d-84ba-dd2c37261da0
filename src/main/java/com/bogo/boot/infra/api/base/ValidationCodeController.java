
package com.bogo.boot.infra.api.base;

import com.bogo.boot.infra.annotation.GreenFunction;
import com.bogo.boot.infra.constant.ValidationCodeAction;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.service.ValidationCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/code")
@Tag(name = "验证码接口" )
public class ValidationCodeController {

	@Resource
	private ValidationCodeService validationCodeService;

	@Operation( summary = "获取注册验证码")
	@Parameter(name = "telephone", description = "手机号", in = ParameterIn.PATH)
	@GreenFunction
	@GetMapping(value = "/register/{telephone}")
	public ResponseEntity<Void> register(@PathVariable String telephone) {
		validationCodeService.send(telephone, ValidationCodeAction.REGISTER);
		return ResponseEntity.ok();
	}

	@Operation( summary = "获取登录验证码")
	@Parameter(name = "telephone", description = "手机号", in = ParameterIn.PATH)
	@GreenFunction
	@GetMapping(value = "/login/{telephone}")
	public ResponseEntity<Void> login(@PathVariable String telephone) {
		validationCodeService.send(telephone, ValidationCodeAction.LOGIN);
		return ResponseEntity.ok();
	}

	@Operation( summary = "获取重置密码验证码")
	@Parameter(name = "telephone", description = "手机号", in = ParameterIn.PATH)
	@GreenFunction
	@GetMapping(value = "/forgot/{telephone}")
	public ResponseEntity<Void> forgot(@PathVariable String telephone) {
		validationCodeService.send(telephone, ValidationCodeAction.FORGOT);
		return ResponseEntity.ok();
	}
}
