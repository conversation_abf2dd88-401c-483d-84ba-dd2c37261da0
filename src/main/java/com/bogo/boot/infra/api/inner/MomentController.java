
package com.bogo.boot.infra.api.inner;

import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.moment.service.MomentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("InnerMomentController")
@RequestMapping("/inner/moment")
@Tag(name = "内部朋友圈接口" )
public class MomentController {

	@Resource
	private MomentService momentService;

	@Operation( summary = "删除朋友圈内容")
	@Parameter(name = "id", description = "朋友圈内容ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> delete(@PathVariable long id) {
		momentService.delete(id);
		return ResponseEntity.ok();
	}

}
