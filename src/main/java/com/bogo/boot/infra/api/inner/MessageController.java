
package com.bogo.boot.infra.api.inner;

import com.bogo.boot.infra.api.inner.request.InnerMessageRequest;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.message.pusher.BroadcastMessagePusher;
import com.bogo.boot.message.pusher.MessagePusherProxy;
import com.bogo.boot.message.service.MessageIndexService;
import com.bogo.boot.message.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("InnerMessageController")
@RequestMapping("/inner/message")
@Tag(name = "内部消息相关接口" )
public class MessageController {

	@Resource
	private BroadcastMessagePusher broadcastMessagePusher;

	@Resource
	private MessagePusherProxy messagePusherProxy;

	@Resource
	private MessageService messageService;

	@Resource
	private MessageIndexService messageIndexService;

	/**
	 * 开放给其他系统，内部调用发送消息
	 */
	@Operation( summary = "发送消息")
	@PostMapping(value = "", produces = "application/json")
	public ResponseEntity<?> send(@RequestBody InnerMessageRequest request) {
		return messagePusherProxy.push(request.ofMessage());
	}

	@Operation( summary = "全部成员系统通知")
	@PostMapping(value = "/broadcast")
	public ResponseEntity<Void> broadcast(@RequestBody InnerMessageRequest request) {
		broadcastMessagePusher.push(request.ofMessage());
		return ResponseEntity.ok();
	}

	@Operation( summary = "在线成员系统通知")
	@PostMapping(value = "/broadcast/online")
	public ResponseEntity<Void> broadcastOnline(@RequestBody InnerMessageRequest request) {
		broadcastMessagePusher.pushOnline(request.ofMessage());
		return ResponseEntity.ok();
	}

	@Operation( summary = "删除单个消息")
	@Parameters({
			@Parameter(name = "id", description = "消息ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "action", description = "消息类型", in = ParameterIn.QUERY,example = "0")
	})
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> delete(@PathVariable Long id ,@RequestParam String action) {

		messageService.delete(id,action);

		return ResponseEntity.ok();
	}

	@Operation( summary = "删除消未接收消息")
	@Parameters({
			@Parameter(name = "uid", description = "用户ID", in = ParameterIn.QUERY,  example = "0"),
			@Parameter(name = "action", description = "消息类型", in = ParameterIn.QUERY,  example = "0")
	})
	@DeleteMapping(value = "/index")
	public ResponseEntity<Void> deleteIndex(@RequestParam Long uid ,@RequestParam String action) {

		messageIndexService.delete(uid,action);

		return ResponseEntity.ok();
	}
}
