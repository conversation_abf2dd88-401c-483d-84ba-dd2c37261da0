
package com.bogo.boot.infra.api.inner.request;

import com.bogo.boot.message.api.request.MessageRequest;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;


@Schema(title = "内部消息发送请求")
@Getter
@Setter
public class InnerMessageRequest extends MessageRequest {

	@Schema(title =  "发送者ID")
	private Long sender;

	@Schema(title = "消息类型")
	private String action;

	public Message ofMessage(){

		Message message = new EventMessage();

		message.setReceiver(uid);
		message.setAction(action);
		message.setContent(content);
		message.setExtra(extra);
		message.setFormat(format);
		message.setSender(sender);
		return message;
	}
}
