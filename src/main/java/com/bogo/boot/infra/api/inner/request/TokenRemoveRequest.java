
package com.bogo.boot.infra.api.inner.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;


@Schema(title =  "内部Token清除请求")
@Getter
@Setter
public class TokenRemoveRequest {

	@Schema(title = "Token")
	@NotBlank(message = "Token不可为空",groups = CreateAction.class)
	private String token;

	@Schema(title =  "终端类型ID",description = "0:Android 1:iOS 2:Web 3:Windows 4:Macos 5:Uni-Android 6:Uni-iOS 7:Uni-H5")
	@NotNull(message = "终端类型ID不能为空",groups = CreateAction.class)
	@Range(min = 0,max = 7,message = "终端类型ID取值范围[0,7]")
	private Integer terminalId;
}
