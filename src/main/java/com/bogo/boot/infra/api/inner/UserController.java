
package com.bogo.boot.infra.api.inner;

import com.bogo.boot.account.constant.UserState;
import com.bogo.boot.account.entity.User;
import com.bogo.boot.account.service.UserService;
import com.bogo.boot.contact.event.FriendActionEvent;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.api.inner.request.UserAddRequest;
import com.bogo.boot.infra.api.inner.request.UserEventRequest;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.pusher.DefaultMessagePusher;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("innerUserController")
@RequestMapping("/inner/user")
@Tag(name = "内部用户相关接口" )
public class UserController {

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private DefaultMessagePusher defaultMessagePusher;

	@Resource
	private UserService userService;


	@Operation( summary = "添加用户")
	@PostMapping(value = "/add", produces = "application/json")
	@ApiResponse(responseCode = "200", description = "成功,并返回用户ID")
	public ResponseEntity<Long> add(@Validated(value = CreateAction.class) @RequestBody UserAddRequest request) {

		User user = new User();
		user.setName(request.getName());
		user.setTelephone(request.getTelephone());
		user.setPassword(request.getPassword());
		user.setState(UserState.NORMAL.getValue());
		userService.save(user);

		return ResponseEntity.ok(user.getId());
	}


	@Operation( summary = "发送信息更新事件")
	@PostMapping(value = "/changed", produces = "application/json")
	public ResponseEntity<Void> onChanged(@RequestParam long uid) {
		Message message = new EventMessage();
		message.setAction(MessageAction.ACTION_112);
		message.setReceiver(uid);
		defaultMessagePusher.push(message);
		return ResponseEntity.ok();
	}

	@Operation( summary = "发送自定义事件")
	@PostMapping(value = "/event", produces = "application/json")
	public ResponseEntity<Void> send(@Validated(value = CreateAction.class) @RequestBody UserEventRequest request) {

		FriendActionEvent friendEvent = new FriendActionEvent();
		friendEvent.setSender(request.getUid());
		friendEvent.setContent(request.getContent());
		friendEvent.setAction(request.getAction());
		applicationEventProducer.publish(friendEvent);

		return ResponseEntity.ok();
	}
}
