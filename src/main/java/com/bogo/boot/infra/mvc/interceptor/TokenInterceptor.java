
package com.bogo.boot.infra.mvc.interceptor;

import com.bogo.boot.account.service.AccessTokenService;
import com.bogo.boot.infra.annotation.AccessToken;
import com.bogo.boot.infra.annotation.ClientOrigin;
import com.bogo.boot.infra.annotation.GreenFunction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.model.Origin;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.util.JSON;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 来自客户端的接口请求拦截器，验证token
 */
@Component
public class TokenInterceptor implements HandlerInterceptor {

	@Resource
	private AccessTokenService accessTokenService;

	private static final String TAG = "ATTR-THREAD-NAME";


	@Override
	public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {

		if (!(handler instanceof HandlerMethod)){
			response.setStatus(HttpStatus.NOT_FOUND.value());
			return false;
		}

		if (((HandlerMethod)handler).hasMethodAnnotation(GreenFunction.class)){
			return true;
		}

		Origin origin = (Origin) request.getAttribute(ClientOrigin.class.getName());

		String token = request.getHeader(Common.MVC_HEADER_TOKEN);

		Long uid = accessTokenService.getUid(origin,token);

		/*
		 * 直接拒绝无token的接口调用请求或者token没有查询到对应的登录用户
		 */
		if (uid == null) {
			response.setStatus(HttpStatus.UNAUTHORIZED.value());
			response.getWriter().println(JSON.toJSONString(ResponseEntity.make(HttpStatus.UNAUTHORIZED)));
			return false;
		}

		request.setAttribute(UID.class.getName(), uid);
		request.setAttribute(AccessToken.class.getName(), token);

		UidHolder.setUid(uid);

		request.setAttribute(TAG,Thread.currentThread().getName());

		Thread.currentThread().setName("http-uid-" + uid);

		return true;

	}

	@Override
	public void afterCompletion(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, @Nullable Exception ex) {

		UidHolder.remove();

		Object initialName = request.getAttribute(TAG);

		String currName = Thread.currentThread().getName();

		if (initialName != null && !Objects.equals(initialName,currName)){
			Thread.currentThread().setName(initialName.toString());
		}
	}
}
