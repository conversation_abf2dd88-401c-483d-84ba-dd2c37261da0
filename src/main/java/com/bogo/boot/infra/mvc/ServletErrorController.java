
package com.bogo.boot.infra.mvc;

import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 公共http错误信息处理
 */
@Hidden
@RestControllerAdvice
public class ServletErrorController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ServletErrorController.class);

	@ExceptionHandler(MethodArgumentNotValidException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public ResponseEntity<Void> handleError400(MethodArgumentNotValidException exception) {
		StringBuilder builder = new StringBuilder();
		for (ObjectError error : exception.getBindingResult().getAllErrors()) {
			builder.append(error.getDefaultMessage()).append(",");
		}
		return ResponseEntity.make(HttpStatus.BAD_REQUEST, builder.deleteCharAt(builder.length() - 1).toString());
	}

	@ExceptionHandler(MissingServletRequestParameterException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public ResponseEntity<Void> handleError400(MissingServletRequestParameterException exception) {
		return ResponseEntity.make(HttpStatus.BAD_REQUEST.value(), "缺少必要的参数:" + exception.getParameterName());
	}

	@ExceptionHandler(HttpMessageNotReadableException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public ResponseEntity<Void> handleError400(HttpMessageNotReadableException ignore) {
		return ResponseEntity.make(HttpStatus.BAD_REQUEST.value(), "输入的数据格式不正确，请检查");
	}

	@ExceptionHandler({NoHandlerFoundException.class, NoResourceFoundException.class})
	@ResponseStatus(HttpStatus.NOT_FOUND)
	public ResponseEntity<Void> handleError404() {
		return ResponseEntity.make(HttpStatus.NOT_FOUND);
	}

	@ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
	@ExceptionHandler(HttpRequestMethodNotSupportedException.class)
	public ResponseEntity<Void> handleError405(HttpRequestMethodNotSupportedException exception) {
		return ResponseEntity.make(HttpStatus.METHOD_NOT_ALLOWED.value(), "请求method不支持,仅支持:" + StringUtils.join(exception.getSupportedMethods(),','));
	}

	@ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
	@ExceptionHandler(HttpMediaTypeNotSupportedException.class)
	public ResponseEntity<Void> handleError415(HttpMediaTypeNotSupportedException exception) {
		return ResponseEntity.make(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(), "contentType不支持:" + exception.getContentType());
	}

	@ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
	@ExceptionHandler(MaxUploadSizeExceededException.class)
	public ResponseEntity<Void> handleError413(MaxUploadSizeExceededException exception) {
		return ResponseEntity.make(HttpStatus.PAYLOAD_TOO_LARGE.value(),"文件超过了最大限制:" + exception.getMaxUploadSize() +"字节");
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(Exception.class)
	public ResponseEntity<Void> handleError500(Exception exception, HttpServletRequest request, HttpServletResponse response) {

		response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE) ;

		LOGGER.error("请求处理异常,uri:{}",request.getRequestURI(),exception);

		return ResponseEntity.make(HttpStatus.INTERNAL_SERVER_ERROR);
	}
}
