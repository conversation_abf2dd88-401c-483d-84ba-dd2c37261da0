
package com.bogo.boot.infra.mvc.interceptor;

import com.bogo.boot.infra.annotation.ClientOrigin;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.holder.ClientOriginHolder;
import com.bogo.boot.infra.model.Origin;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 获取客户端信息 拦截器
 */
@Component
public class OriginInterceptor implements HandlerInterceptor {

	@Override
	public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {

		Origin origin = new Origin();
		origin.setType(request.getIntHeader(Common.MVC_HEADER_APP_TYPE));
		origin.setVersion(request.getIntHeader(Common.MVC_HEADER_APP_VERSION));
		origin.setLanguage(LocaleContextHolder.getLocale().toLanguageTag());

		request.setAttribute(ClientOrigin.class.getName(),origin);

		ClientOriginHolder.set(origin);

		return true;

	}

	@Override
	public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, @Nullable Exception ex) {
		ClientOriginHolder.remove();
	}
}
