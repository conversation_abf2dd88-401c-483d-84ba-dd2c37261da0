
package com.bogo.boot.infra.mvc.interceptor;

import com.bogo.boot.file.config.properties.FileUploadProperties;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Locale;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class FileInterceptor implements HandlerInterceptor {

	@Resource
	private FileUploadProperties properties;

	@Override
	public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object arg2) {
		if (isMultipartContent(request) && request.getContentLength() > properties.getMaxSize().toBytes()) {
			throw new MaxUploadSizeExceededException(properties.getMaxSize().toBytes());
		}
		return true;
	}


	private boolean isMultipartContent(HttpServletRequest request){
		String method = request.getMethod();
		String contentType = request.getContentType();
		return HttpMethod.POST.name().equalsIgnoreCase(method) && contentType != null && contentType.toLowerCase(Locale.ENGLISH).startsWith(MediaType.MULTIPART_FORM_DATA_VALUE);
	}



}
