
package com.bogo.boot.infra.mvc.interceptor;

import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.util.JSON;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

/**
 * 用于打印接口请求日志,
 * 如果不需要打印，可以删除该类即可
 */
@ControllerAdvice
@ConditionalOnProperty(value = "bogo.api.logcat.enable")
public class LogcatInterceptor implements ResponseBodyAdvice<ResponseEntity<Object>> {
	private final Logger logger = LoggerFactory.getLogger(LogcatInterceptor.class);

	private static final String HEADER_FORMAT = "[header]%s: %s%n";


	@Override
	public boolean supports(MethodParameter parameter, @NotNull Class aClass) {
		Class<?> valueClass =  parameter.getParameterType();
		return ResponseEntity.class.isAssignableFrom(valueClass);
	}


	@Override
	public ResponseEntity<Object> beforeBodyWrite(ResponseEntity<Object> body, @NotNull MethodParameter returnType, @NotNull MediaType selectedContentType, @NotNull Class<? extends HttpMessageConverter<?>> selectedConverterType, @NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response) {

		HttpServletRequest httpRequest = ((ServletServerHttpRequest) request).getServletRequest();
		HttpServletResponse httpResponse = ((ServletServerHttpResponse) response).getServletResponse();

		StringBuilder builder = new StringBuilder();

		if (httpRequest instanceof ContentCachingRequestWrapper){

			String requestLog = getRequestLog((ContentCachingRequestWrapper) httpRequest);

			builder.append(requestLog);

		}

		if (httpResponse instanceof ContentCachingResponseWrapper){

			String responseLog = getResponseLog(body);

			builder.append(responseLog);

		}

		if (!builder.isEmpty()){
			logger.info(builder.toString());
		}

		return body;
	}


	private String getRequestLog(ContentCachingRequestWrapper wrapper){


		StringBuilder builder = new StringBuilder();
		builder.append("\n>----------------------Request-----------------------<\n");

		builder.append(String.format("%s %s %n",wrapper.getMethod(), wrapper.getRequestURI()));

		builder.append(getHeaderLine(Common.MVC_HEADER_TOKEN, wrapper.getHeader(Common.MVC_HEADER_TOKEN)));
		builder.append(getHeaderLine(Common.MVC_HEADER_APP_TYPE, wrapper.getHeader(Common.MVC_HEADER_APP_TYPE)));
		builder.append(getHeaderLine(Common.MVC_HEADER_APP_VERSION, wrapper.getHeader(Common.MVC_HEADER_APP_VERSION)));
		builder.append(getHeaderLine(HttpHeaders.ACCEPT_LANGUAGE, wrapper.getHeader(HttpHeaders.ACCEPT_LANGUAGE)));
		builder.append(getHeaderLine(HttpHeaders.ORIGIN, wrapper.getHeader(HttpHeaders.ORIGIN)));
		builder.append(getHeaderLine(HttpHeaders.USER_AGENT, wrapper.getHeader(HttpHeaders.USER_AGENT)));
		builder.append(getHeaderLine(HttpHeaders.CONTENT_TYPE, wrapper.getHeader(HttpHeaders.CONTENT_TYPE)));

		for (Map.Entry<String,String[]> params :wrapper.getParameterMap().entrySet()){
			builder.append(String.format("[parameter]%s: %s%n", params.getKey(),StringUtils.join(params.getValue(),",")));
		}

		String contentType = wrapper.getHeader(HttpHeaders.CONTENT_TYPE);

		if (MediaType.APPLICATION_JSON_VALUE.equals(contentType)){
			String body = StringUtils.toEncodedString(wrapper.getContentAsByteArray(),Charset.forName(wrapper.getCharacterEncoding()));
			builder.append(String.format("[payload]%n%s",body));
		}

		return builder.toString();
	}

	private String getResponseLog(ResponseEntity<Object> body){

		StringBuilder builder = new StringBuilder();

		builder.append("\n<----------------------Response----------------------->\n");

		builder.append(JSON.toJSONString(body));

		return builder.toString();
	}
	
	private String getHeaderLine(String name ,String value){
		if (value == null){
			return "";
		}

		return String.format(HEADER_FORMAT,name, value);
	}
}
