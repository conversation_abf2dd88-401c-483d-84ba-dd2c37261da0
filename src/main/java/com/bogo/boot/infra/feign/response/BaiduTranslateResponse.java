package com.bogo.boot.infra.feign.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BaiduTranslateResponse {

    @JsonProperty(value = "trans_result")
    private Result[] transResult;

    @Getter
    @Setter
    public static class Result{
        private String dst;
    }


    public String getText(){

        StringBuilder builder = new StringBuilder();

        if (transResult == null || transResult.length == 0){
            return null;
        }

        for (Result result : transResult){
            builder.append(result.dst).append("\n\n");
        }

        builder.deleteCharAt(builder.length() - 1);
        builder.deleteCharAt(builder.length() - 1);

        return builder.toString();
    }
}
