
package com.bogo.boot.infra.feign.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "文本翻译请求体")
@Getter
@Setter
public class TextTranslateRequest{

	@Schema(title = "翻译文本")
	@NotBlank(message = "待翻译文本不能为空",groups = CreateAction.class)
	private String text;

	@Schema(title = "目标语言")
	@NotBlank(message = "目标语言不能为空",groups = CreateAction.class)
	private String language;

}
