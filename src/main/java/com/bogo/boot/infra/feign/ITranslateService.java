
package com.bogo.boot.infra.feign;

import com.bogo.boot.infra.feign.fallback.TranslateServiceFallbackFactory;
import com.bogo.boot.infra.feign.response.BaiduTranslateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(value = "translate-service",url = "http://api.fanyi.baidu.com",fallbackFactory = TranslateServiceFallbackFactory.class)
public interface ITranslateService {

	@GetMapping(path = "/api/trans/vip/translate?from=auto")
	BaiduTranslateResponse translate(@RequestParam String q,
									 @RequestParam String to,
									 @RequestParam String appid,
									 @RequestParam String salt,
									 @RequestParam String sign);
}
