package com.bogo.boot.infra.feign.fallback;


import com.bogo.boot.infra.feign.ITranslateService;
import com.bogo.boot.infra.feign.response.BaiduTranslateResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class TranslateServiceFallbackFactory implements FallbackFactory<ITranslateService>, ITranslateService {

	private final Logger logger = LoggerFactory.getLogger(TranslateServiceFallbackFactory.class);

	@Override
	public ITranslateService create(Throwable throwable) {
		logger.warn("translate-service接口熔断",throwable);
		return this;
	}

	@Override
	public BaiduTranslateResponse translate(String q, String to, String appid, String salt, String sign) {
		return new BaiduTranslateResponse();
	}
}
