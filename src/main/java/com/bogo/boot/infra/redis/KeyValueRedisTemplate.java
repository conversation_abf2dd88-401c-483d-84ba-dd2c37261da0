
package com.bogo.boot.infra.redis;

import java.time.Duration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class KeyValueRedisTemplate extends StringRedisTemplate {

	public KeyValueRedisTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void setIfAbsent(String key ,String value) {
		BoundValueOperations<String,String> operations = super.boundValueOps(key);
		operations.setIfAbsent(value);
	}

	public void set(String key ,String value) {
		this.set(key,value,null);
	}

	public void set(String key , String value, Duration duration) {
		if (value == null){
			return;
		}

		BoundValueOperations<String,String> operations = super.boundValueOps(key);
		operations.set(value);
		if (duration != null){
			operations.expire(duration);
		}
	}


	public String get(String key) {
		return super.boundValueOps(key).get();
	}

	public long increment(String key){
		BoundValueOperations<String,String> operations = super.boundValueOps(key);
		Long value = operations.increment();
		return value == null ? 0L : value;
	}

}
