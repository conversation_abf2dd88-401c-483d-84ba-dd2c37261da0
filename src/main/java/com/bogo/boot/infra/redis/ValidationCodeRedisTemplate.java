
package com.bogo.boot.infra.redis;

import com.bogo.boot.infra.constant.ValidationCodeAction;
import java.time.Duration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class ValidationCodeRedisTemplate extends StringRedisTemplate {

	private static final String CACHE_PREFIX = "V_CODE_%s_%d";

	public ValidationCodeRedisTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void save(String telephone, ValidationCodeAction action,String code) {

		String key = String.format(CACHE_PREFIX,telephone,action.getAction()) ;

		BoundValueOperations<String,String> operations = super.boundValueOps(key);
		operations.set(code);
		operations.expire(Duration.ofMinutes(10));

	}

	public String get(String telephone, ValidationCodeAction action) {

		String key = String.format(CACHE_PREFIX,telephone,action.getAction()) ;
		return super.boundValueOps(key).get();
	}
}
