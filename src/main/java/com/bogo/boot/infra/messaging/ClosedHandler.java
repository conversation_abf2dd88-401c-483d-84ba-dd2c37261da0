
package com.bogo.boot.infra.messaging;

import com.bogo.boot.account.constant.SessionState;
import com.bogo.boot.account.entity.Session;
import com.bogo.boot.account.service.SessionService;
import com.bogo.boot.infra.constant.Common;
import com.bogo.messaging.constant.ChannelAttr;
import com.bogo.messaging.handler.RequestHandler;
import com.bogo.messaging.model.SentBody;
import io.netty.channel.Channel;
import jakarta.annotation.Resource;
import java.util.Objects;

/**
 * 连接断开时，更新用户相关状态
 */
@MessagingHandler(key = "client_closed")
public class ClosedHandler implements RequestHandler {

	@Resource
	private SessionService sessionService;

	@Override
	public void process(Channel channel, SentBody message) {

		Long sessionId = channel.attr(Common.SESSION_ID).get();

		if (sessionId == null){
			return;
		}

		/*
		 * ios开启了apns也需要显示在线，因此不删记录
		 */
		if (Objects.equals(channel.attr(ChannelAttr.CHANNEL).get(), Session.CHANNEL_IOS)){
			sessionService.updateState(sessionId, SessionState.INACTIVE);
			return;
		}

		sessionService.delete(sessionId);
	}

}
