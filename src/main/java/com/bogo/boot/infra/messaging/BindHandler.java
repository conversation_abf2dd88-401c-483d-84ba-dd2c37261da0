
package com.bogo.boot.infra.messaging;

import com.bogo.boot.account.entity.Session;
import com.bogo.boot.account.service.SessionService;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.message.event.ClientBindEvent;
import com.bogo.boot.message.redis.SignalRedisTemplate;
import com.bogo.messaging.constant.ChannelAttr;
import com.bogo.messaging.group.SessionGroup;
import com.bogo.messaging.handler.RequestHandler;
import com.bogo.messaging.model.ReplyBody;
import com.bogo.messaging.model.SentBody;
import io.netty.channel.Channel;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.HttpStatus;

/**
 * 客户长连接 账户绑定实现
 */
@MessagingHandler(key = "client_bind")
public class BindHandler implements RequestHandler {

	@Resource
	private SessionService sessionService;

	@Resource
	private SessionGroup sessionGroup;

	@Resource
	private SignalRedisTemplate signalRedisTemplate;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Override
	public void process(Channel channel, SentBody body) {

		if (sessionGroup.isManaged(channel)){
			return;
		}

		ReplyBody reply = new ReplyBody();
		reply.setKey(body.getKey());
		reply.setCode(HttpStatus.OK.value());
		reply.setTimestamp(System.currentTimeMillis());

		String uid = body.get("uid");

		if (StringUtils.isBlank(uid)){
			reply.setCode(HttpStatus.BAD_REQUEST.value());
			reply.setMessage("UID参数不可为空");
			channel.write(reply);
			return;
		}

		if (!NumberUtils.isCreatable(uid)){
			reply.setCode(HttpStatus.BAD_REQUEST.value());
			reply.setMessage("UID参数必须是整数");
			channel.write(reply);
			return;
		}


		Session session = new Session();
		session.setUid(NumberUtils.createLong(uid));
		session.setNid(channel.attr(ChannelAttr.ID).get());
		session.setDeviceId(body.get("deviceId"));
		session.setChannel(body.get("channel"));
		session.setDeviceName(body.get("deviceName"));
		session.setAppVersion(body.get("appVersion"));
		session.setOsVersion(body.get("osVersion"));
		session.setLanguage(body.get("language"));

		channel.attr(ChannelAttr.UID).set(uid);
		channel.attr(ChannelAttr.CHANNEL).set(session.getChannel());
		channel.attr(ChannelAttr.DEVICE_ID).set(session.getDeviceId());
		channel.attr(ChannelAttr.LANGUAGE).set(session.getLanguage());

		/*
		 *存储到数据库
		 */
		sessionService.add(session);

		channel.attr(Common.SESSION_ID).set(session.getId());

		/*
		 * 添加到内存管理
		 */
		sessionGroup.add(channel);

		/*
		 *向客户端发送bind响应
		 */
		channel.writeAndFlush(reply);

		/*
		 * 发送上线事件到集群中的其他实例，控制其他设备下线
		 */
		signalRedisTemplate.bind(session);


		applicationEventProducer.publish(new ClientBindEvent(body));
	}
}
