
package com.bogo.boot.infra.repository;

import com.bogo.boot.infra.entity.Config;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface ConfigRepository extends JpaRepository<Config, Long> {

	@Query("from Config where domain= ?1 ")
	List<Config> queryListByDomain(String domain);

	@Query("select count(id) from Config where domain= ?2 and name = ?3 and id <> ?1")
	long count(long id, String domain, String key);

	@Query("select count(id) from Config where domain= ?1 and name = ?2")
	long count(String domain, String key);
}
