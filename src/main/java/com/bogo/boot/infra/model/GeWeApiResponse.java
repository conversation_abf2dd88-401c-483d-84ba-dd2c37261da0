package com.bogo.boot.infra.model;

import lombok.Getter;
import lombok.Setter;

/**
 * GeWe API通用响应模型
 */
@Getter
@Setter
public class GeWeApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private Integer ret;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ret != null && ret == 200;
    }
}