package com.bogo.boot.infra.model;

import lombok.Getter;
import lombok.Setter;

/**
 * GeWe好友信息模型
 */
@Getter
@Setter
public class GeWeFriendInfo {
    
    /**
     * 微信ID
     */
    private String userName;
    
    /**
     * 昵称
     */
    private String nickName;
    
    /**
     * 拼音首字母
     */
    private String pyInitial;
    
    /**
     * 全拼
     */
    private String quanPin;
    
    /**
     * 性别 1-男 2-女
     */
    private Integer sex;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 备注拼音首字母
     */
    private String remarkPyInitial;
    
    /**
     * 备注全拼
     */
    private String remarkQuanPin;
    
    /**
     * 个性签名
     */
    private String signature;
    
    /**
     * 微信号
     */
    private String alias;
    
    /**
     * 朋友圈背景图
     */
    private String snsBgImg;
    
    /**
     * 国家
     */
    private String country;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 大头像URL
     */
    private String bigHeadImgUrl;
    
    /**
     * 小头像URL
     */
    private String smallHeadImgUrl;
    
    /**
     * 是否为群聊
     */
    private Boolean isGroup = false;
}