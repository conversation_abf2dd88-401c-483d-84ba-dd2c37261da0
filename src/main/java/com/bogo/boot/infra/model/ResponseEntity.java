
package com.bogo.boot.infra.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Setter
@Getter
@Schema(title = "接口响应体")
public class ResponseEntity<T> {
	private int code = HttpStatus.OK.value();

	@Schema(title = "描述信息")
	private String message;

	@Schema(title = "数据")
	private T data;

	@Schema(title = "登录、注册返回的token")
	private String token;

	@Schema(title = "响应时间戳")
	private Long timestamp = System.currentTimeMillis();


    public static ResponseEntity<Void> make(int code){
		return make(code,null);
	}

	public static <T> ResponseEntity<T> make(int code,String message){
		ResponseEntity<T> result = new ResponseEntity<>();
		result.setCode(code);
		result.setMessage(message);
		return result;
	}

	public static ResponseEntity<Void> make(HttpStatus status){
		ResponseEntity<Void> result = new ResponseEntity<>();
		result.setCode(status.value());
		result.setMessage(status.getReasonPhrase());
		return result;
	}

	public static <Q> ResponseEntity<Q> make(HttpStatus status,String message){
		ResponseEntity<Q> result = new ResponseEntity<>();
		result.setCode(status.value());
		result.setMessage(message);
		return result;
	}


	public static <Q> ResponseEntity<Q> ok(Q data){
		ResponseEntity<Q> result = new ResponseEntity<>();
		result.setData(data);
		return result;
	}

	public static <Q> ResponseEntity<Q> ok(){
		return new ResponseEntity<>();
	}

}
