package com.bogo.boot.infra.model;

import com.bogo.boot.account.entity.Session;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Origin {

    public static final Origin UNKNOWN = new Origin();

    static {
        UNKNOWN.language = "zh-CN";
        UNKNOWN.type = -1 ;
        UNKNOWN.version = 0 ;
    }

    private static final Map<Integer,String> channelMap = new HashMap<>();

    private static final int TYPE_ANDROID = 0;

    private static final int TYPE_IOS = 1;

    private static final int TYPE_WEB = 2;

    private static final int TYPE_WINDOWS = 3;

    private static final int TYPE_MACOS = 4;

    private static final int TYPE_UNI_ANDROID = 5;

    private static final int TYPE_UNI_IOS = 6;

    private static final int TYPE_UNI_H5 = 7;


    static {
        channelMap.put(TYPE_ANDROID, Session.CHANNEL_ANDROID);
        channelMap.put(TYPE_IOS, Session.CHANNEL_IOS);
        channelMap.put(TYPE_WEB, Session.CHANNEL_WEB);
        channelMap.put(TYPE_WINDOWS, Session.CHANNEL_WINDOWS);
        channelMap.put(TYPE_MACOS, Session.CHANNEL_MAC);
        channelMap.put(TYPE_UNI_ANDROID, Session.CHANNEL_UNI_ANDROID);
        channelMap.put(TYPE_UNI_IOS, Session.CHANNEL_UNI_IOS);
        channelMap.put(TYPE_UNI_H5, Session.CHANNEL_UNI_H5);
    }

    private int type;
    private int version;
    private String language;

    public String getAppChannel(){
        return channelMap.get(type);
    }
}
