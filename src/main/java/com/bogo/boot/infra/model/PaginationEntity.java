
package com.bogo.boot.infra.model;


import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "分页接口响应体")
public class PaginationEntity<T extends Sortable>  extends ResponseEntity<List<T>> {

	/**
	 * 数据总条数
	 */
	private static final String KEY_COUNT = "count";

	/**
	 * 数据总页数
	 */
	private static final String KEY_COUNT_PAGE = "countPage";

	/**
	 * 每页几条数据
	 */
	private static final String KEY_PAGE_SIZE = "size";

	/**
	 * 当前第几页
	 */
	private static final String KEY_CURRENT_PAGE = "currentPage";

	@Getter
    @Schema(title = "分页信息")
	private final Map<String,Number> page = new HashMap<>();

	@Schema(title = "列表数据")
	private List<T> data;

	/**
	 * 用于分页时，前端传上来，按ID 过滤提升查询效率
	 */
	@Setter
    @Getter
    @Schema(title = "本页最小数据ID")
	private Long minId;

	@Setter
    @Getter
    @Schema(title = "本页最大数据ID")
	private Long maxId;

    @Override
	public List<T> getData() {
		return data;
	}

	@Override
	public void setData(List<T> data) {
		this.data = data;
	}

    public static <T extends Sortable> PaginationEntity<T> make(org.springframework.data.domain.Page<T> dto) {
		PaginationEntity<T> result = new PaginationEntity<>();
		result.page.put(KEY_COUNT,dto.getTotalElements());
		result.page.put(KEY_COUNT_PAGE,dto.getTotalPages());
		result.page.put(KEY_PAGE_SIZE,dto.getSize());
		result.page.put(KEY_CURRENT_PAGE,dto.getNumber());
		result.data = (dto.getContent());

		result.minId = dto.getContent().stream().min(Comparator.comparingLong(Sortable::getId)).map(Sortable::getId).orElse(null);
		result.maxId = dto.getContent().stream().max(Comparator.comparingLong(Sortable::getId)).map(Sortable::getId).orElse(null);

		return result;
	}

}
