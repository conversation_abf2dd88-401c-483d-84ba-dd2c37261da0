
package com.bogo.boot.infra.constant;

import io.netty.util.AttributeKey;

public interface Common {

	long SYSTEM_ID = 0;

	String LOCAL_BUCKET = "bucket";

	int API_PAGE_SIZE = 20;

	String MVC_HEADER_TOKEN = "access-token";

	String MVC_HEADER_APP_TYPE = "x-app-type";

	String MVC_HEADER_APP_VERSION = "x-app-version";

	int VALIDATION_CODE_LENGTH = 6;

	String PUSH_MESSAGE_INNER_QUEUE = "signal/channel/PUSH_MESSAGE_INNER_QUEUE";

	String BIND_MESSAGE_INNER_QUEUE = "signal/channel/BIND_MESSAGE_INNER_QUEUE";

	String STORAGE_MESSAGE_INNER_QUEUE = "signal/channel/STORAGE_MESSAGE_INNER_QUEUE";

	AttributeKey<Long> SESSION_ID = AttributeKey.valueOf("session_id");

}
