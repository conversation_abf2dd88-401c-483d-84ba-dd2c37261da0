
package com.bogo.boot.infra.constant;

import lombok.Getter;

@Getter
public enum ValidationCodeAction {

	/**
	 * 验证码类型-用户登录事件
	 */
	LOGIN(100,"登录短信验证码","[和信]: Your login validation code is : %s"),

	/**
	 * 验证码类型-用户注册事件
	 */
	REGISTER(101,"注册短信验证码","[和信]: Your register validation code is : %s"),

	/**
	 * 验证码类型-找回密码事件
	 */
	FORGOT(102,"重置密码短信验证码","[和信]: Your reset password validation code is : %s");

	private final int action;

	private final String template;

	private final String description;

	ValidationCodeAction(int action , String description, String template){
		this.action = action;
		this.description = description;
		this.template = template;
	}

}
