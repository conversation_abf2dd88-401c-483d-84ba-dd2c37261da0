
package com.bogo.boot.infra.configuration.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "bogo.livekit")
public class LivekitProperties {


    private String appId;
    private String secret;

    private String uri;

}
