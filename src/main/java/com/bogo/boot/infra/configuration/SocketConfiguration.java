
package com.bogo.boot.infra.configuration;

import com.bogo.boot.account.service.SessionService;
import com.bogo.boot.infra.configuration.properties.APNsProperties;
import com.bogo.boot.infra.configuration.properties.AppSocketProperties;
import com.bogo.boot.infra.configuration.properties.MessageProperties;
import com.bogo.boot.infra.configuration.properties.UniPushProperties;
import com.bogo.boot.infra.configuration.properties.WebsocketProperties;
import com.bogo.boot.infra.messaging.MessagingHandler;
import com.bogo.messaging.acceptor.AppSocketAcceptor;
import com.bogo.messaging.acceptor.WebsocketAcceptor;
import com.bogo.messaging.acceptor.config.SocketConfig;
import com.bogo.messaging.acceptor.config.WebsocketConfig;
import com.bogo.messaging.group.SessionGroup;
import com.bogo.messaging.group.TagSessionGroup;
import com.bogo.messaging.handler.RequestHandler;
import com.bogo.messaging.model.SentBody;
import io.netty.channel.Channel;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

@org.springframework.context.annotation.Configuration
@ComponentScan(includeFilters = @ComponentScan.Filter(MessagingHandler.class))
@EnableConfigurationProperties({
		APNsProperties.class,
		UniPushProperties.class,
		WebsocketProperties.class,
		AppSocketProperties.class,
		MessageProperties.class
})
public class SocketConfiguration implements RequestHandler, ApplicationListener<ApplicationStartedEvent> {

	@Resource
	private ApplicationContext applicationContext;

	@Resource
	private SessionService sessionService;

	private final Map<String, RequestHandler> handlerMap = new HashMap<>();

	@Bean(destroyMethod = "destroy",initMethod = "bind")
	@ConditionalOnProperty(name = {"messaging.websocket.enable"},matchIfMissing = true)
	public WebsocketAcceptor websocketAcceptor(WebsocketProperties properties) {
		WebsocketConfig config = new WebsocketConfig();
		config.setPath(properties.getPath());
		config.setPort(properties.getPort());
		config.setProtocol(properties.getProtocol());
		config.setOuterRequestHandler(this);
		config.setEnable(properties.isEnable());
		config.setReadIdle(properties.getReadIdle());
		config.setWriteIdle(properties.getWriteIdle());
		config.setMaxPongTimeout(properties.getMaxPongTimeout());
		return new WebsocketAcceptor(config);
	}

	@Bean(destroyMethod = "destroy",initMethod = "bind")
	@ConditionalOnProperty(name = {"messaging.app.enable"},matchIfMissing = true)
	public AppSocketAcceptor appSocketAcceptor(AppSocketProperties properties) {

		SocketConfig config = new SocketConfig();
		config.setPort(properties.getPort());
		config.setOuterRequestHandler(this);
		config.setEnable(properties.isEnable());
		config.setReadIdle(properties.getReadIdle());
		config.setWriteIdle(properties.getWriteIdle());
		config.setMaxPongTimeout(properties.getMaxPongTimeout());

		return new AppSocketAcceptor(config);
	}

	@Bean
	public SessionGroup sessionGroup() {
		return new SessionGroup();
	}

	@Bean
	public TagSessionGroup tagSessionGroup() {
		return new TagSessionGroup();
	}

	@Override
	public void process(Channel channel, SentBody body) {

        RequestHandler handler = handlerMap.get(body.getKey());

		if (handler != null){
			handler.process(channel, body);
		}

	}

	/**
	 * springboot启动完成之后再启动Messaging服务的，避免服务正在重启时，客户端会立即开始连接导致意外异常发生.
	 */
	@Override
	public void onApplicationEvent(@NotNull ApplicationStartedEvent startedEvent) {

		Map<String, RequestHandler> beans =  applicationContext.getBeansOfType(RequestHandler.class);

		for (Map.Entry<String, RequestHandler> entry : beans.entrySet()) {

			RequestHandler handler = entry.getValue();

			MessagingHandler annotation = handler.getClass().getAnnotation(MessagingHandler.class);

			if (annotation != null){
				handlerMap.put(annotation.key(),handler);
			}
		}

        /*
		 删除本机的连接记录
		 */
		sessionService.delete();
	}
}