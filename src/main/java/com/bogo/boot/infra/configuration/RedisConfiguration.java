
package com.bogo.boot.infra.configuration;

import com.bogo.boot.infra.constant.Common;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
@AutoConfigureBefore(RedisAutoConfiguration.class)
public class RedisConfiguration{

    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(LettuceConnectionFactory connectionFactory,
                                                                       MessageListener pushMessageListener,
                                                                       MessageListener bindMessageListener,
                                                                       MessageListener asyncStorageHandler){
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(pushMessageListener,new ChannelTopic(Common.PUSH_MESSAGE_INNER_QUEUE));
        container.addMessageListener(bindMessageListener,new ChannelTopic(Common.BIND_MESSAGE_INNER_QUEUE));
        container.addMessageListener(asyncStorageHandler,new ChannelTopic(Common.STORAGE_MESSAGE_INNER_QUEUE));
        connectionFactory.setValidateConnection(true);
        return container;
    }

    @Bean
    public RedissonClient redissonClient(RedisProperties properties) {
        Config config = new Config();
        SingleServerConfig serverConfig = config.useSingleServer();
        serverConfig.setAddress("redis://" + properties.getHost() + ":" + properties.getPort());
        serverConfig.setDatabase(1);
        if (StringUtils.isNotBlank(properties.getPassword())){
            serverConfig.setPassword(properties.getPassword());
        }
        return Redisson.create(config);
    }

}