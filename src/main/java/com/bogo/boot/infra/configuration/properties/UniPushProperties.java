
package com.bogo.boot.infra.configuration.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "bogo.uni.push")
public class UniPushProperties {

    private boolean enable;

    private String appId;
    private String appKey;

    private String masterSecret;

    private String domain;

}
