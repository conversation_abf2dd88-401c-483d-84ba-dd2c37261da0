
package com.bogo.boot.infra.configuration;

import com.bogo.boot.infra.constant.Common;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;

@Configuration

public class SwaggerConfiguration {

    @Bean
    public OpenAPI springDocOpenAPI() {
        return new OpenAPI()
                .components(new Components())
                .info(new Info()
                        .title("Bogo Service APIs.")
                        .description("系统业务接口服务")
                        .version("3.0.0")
                );

    }

    /**
     * 添加全局的请求头参数
     */
    @Bean
    public OpenApiCustomizer apiHeaderCustomizer() {
        return openApi -> {
            openApi.getComponents().addSecuritySchemes(Common.MVC_HEADER_TOKEN, new SecurityScheme()
                    .type(SecurityScheme.Type.APIKEY)
                    .in(SecurityScheme.In.HEADER)
                    .name(Common.MVC_HEADER_TOKEN)
                    .description("用户Token")
            ).addSecuritySchemes(Common.MVC_HEADER_APP_TYPE, new SecurityScheme()
                    .type(SecurityScheme.Type.APIKEY)
                    .in(SecurityScheme.In.HEADER)
                    .name(Common.MVC_HEADER_APP_TYPE)
                    .description("客户端类型")
            ).addSecuritySchemes(Common.MVC_HEADER_APP_VERSION, new SecurityScheme()
                    .type(SecurityScheme.Type.APIKEY)
                    .in(SecurityScheme.In.HEADER)
                    .name(Common.MVC_HEADER_APP_VERSION)
                    .description("客户端版本号")
            ).addSecuritySchemes(HttpHeaders.ACCEPT_LANGUAGE, new SecurityScheme()
                    .type(SecurityScheme.Type.APIKEY)
                    .in(SecurityScheme.In.HEADER)
                    .name(HttpHeaders.ACCEPT_LANGUAGE)
                    .description("客户端语言")
            );

            openApi.addSecurityItem(new SecurityRequirement().addList(HttpHeaders.ACCEPT_LANGUAGE).addList(Common.MVC_HEADER_TOKEN).addList(Common.MVC_HEADER_APP_TYPE).addList(Common.MVC_HEADER_APP_VERSION));
        };
    }

    @Bean
    public GroupedOpenApi userApiDocket(OpenApiCustomizer apiHeaderCustomizer) {
        return GroupedOpenApi.builder()
                .group("0、APP接口-账号相关")
                .packagesToScan("com.bogo.boot.account.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi groupApiDocket(OpenApiCustomizer apiHeaderCustomizer) {

        return GroupedOpenApi.builder()
                .group("1、APP接口-群组相关")
                .packagesToScan("com.bogo.boot.group.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi contactApiDocket(OpenApiCustomizer apiHeaderCustomizer) {
        return GroupedOpenApi.builder()
                .group("2、APP接口-联系人相关")
                .packagesToScan("com.bogo.boot.contact.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi messageApiDocket(OpenApiCustomizer apiHeaderCustomizer) {
        return GroupedOpenApi.builder()
                .group("3、APP接口-消息相关")
                .packagesToScan("com.bogo.boot.message.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi fileApiDocket(OpenApiCustomizer apiHeaderCustomizer) {
        return GroupedOpenApi.builder()
                .group("4、APP接口-文件相关")
                .packagesToScan("com.bogo.boot.file.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi momentApiDocket(OpenApiCustomizer apiHeaderCustomizer) {

        return GroupedOpenApi.builder()
                .group("5、APP接口-朋友圈相关")
                .packagesToScan("com.bogo.boot.moment.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi livekitApiDocket(OpenApiCustomizer apiHeaderCustomizer) {

        return GroupedOpenApi.builder()
                .group("6、APP接口-Livekit通话相关")
                .packagesToScan("com.bogo.boot.livekit.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi emojiApiDocket(OpenApiCustomizer apiHeaderCustomizer) {
        return GroupedOpenApi.builder()
                .group("7、APP接口-表情相关")
                .packagesToScan("com.bogo.boot.emoji.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi appApiDocket(OpenApiCustomizer apiHeaderCustomizer) {

        return GroupedOpenApi.builder()
                .group("8、APP接口-公众号相关")
                .packagesToScan("com.bogo.boot.app.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi baseApiDocket(OpenApiCustomizer apiHeaderCustomizer) {

        return GroupedOpenApi.builder()
                .group("9、APP接口-基础能力相关")
                .packagesToScan("com.bogo.boot.infra.api.base")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi noteApiDocket(OpenApiCustomizer apiHeaderCustomizer) {

        return GroupedOpenApi.builder()
                .group("A、APP接口-笔记相关")
                .packagesToScan("com.bogo.boot.note.api")
                .addOpenApiCustomizer(apiHeaderCustomizer)
                .build();
    }

    @Bean
    public GroupedOpenApi innerApiDocket() {
        return GroupedOpenApi.builder()
                .group("B、系统内部接口")
                .packagesToScan("com.bogo.boot.infra.api.inner")
                .build();
    }

    @Bean
    public GroupedOpenApi openApiDocket() {
        return GroupedOpenApi.builder()
                .group("C、公共开放接口")
                .packagesToScan("com.bogo.boot.infra.api.open")
                .build();
    }

}
