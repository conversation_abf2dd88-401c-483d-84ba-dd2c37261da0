package com.bogo.boot.infra.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * GeWe API配置类
 */
@Configuration
public class GeWeApiConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}