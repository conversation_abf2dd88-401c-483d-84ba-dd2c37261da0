package com.bogo.boot.infra.configuration;

import com.bogo.boot.file.config.properties.FileLocalProperties;
import com.bogo.boot.file.config.properties.FileOssProperties;
import com.bogo.boot.file.config.properties.FileUploadProperties;
import com.bogo.boot.infra.configuration.properties.LivekitProperties;
import com.bogo.boot.infra.configuration.properties.TranslateProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({
        FileUploadProperties.class,
        FileLocalProperties.class,
        FileOssProperties.class,
        LivekitProperties.class,
        TranslateProperties.class
        })
public class AppConfiguration {
}