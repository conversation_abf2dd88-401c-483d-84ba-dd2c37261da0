
package com.bogo.boot.infra.configuration.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "bogo.translate")
public class TranslateProperties {


    private boolean enable;
    private String appId;
    private String secretKey;

}
