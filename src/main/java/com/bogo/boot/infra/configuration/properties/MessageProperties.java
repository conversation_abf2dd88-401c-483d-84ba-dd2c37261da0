
package com.bogo.boot.infra.configuration.properties;

import java.time.Duration;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = "bogo.message")
public class MessageProperties {

    /**
     * 触发敏感字是否拦截消息，使其无法发送
     */
    private Patch patch = new Patch();

    /**
     * 触发敏感字配置
     */
    private Blackword blackword = new Blackword();

    /**
     * 是否允许HTML内容
     */
    private Html html = new Html();

    public boolean isBlackwordIntercept() {
        return blackword.intercept;
    }

    @Setter
    @Getter
    public static class Patch{
        /**
         * 是否启用多端同步补偿
         */
        private boolean enable;

        /**
         * 多端同步补偿数据存储时间，过期将会清理，默认30天
         */
        private Duration lifecycle = Duration.ofDays(30);


    }

    @Setter
    @Getter
    public static class Blackword{

        /**
         * 是否启用敏感词检查
         */
        private boolean enable;

        /**
         * 触发敏感字是否拦截消息，使其无法发送
         */
        private boolean intercept;

        /**
         * 触发敏感字是否通知其他应用
         */
        private String webhook;

    }

    @Setter
    @Getter
    public static class Html{
        /**
         * 是否可包含html内容
         */
        private boolean allowed;

    }
}
