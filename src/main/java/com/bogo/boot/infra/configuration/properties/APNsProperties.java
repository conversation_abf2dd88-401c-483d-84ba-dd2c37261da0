
package com.bogo.boot.infra.configuration.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@RefreshScope
@ConfigurationProperties(prefix = "bogo.apns")
@Getter
@Setter
public class APNsProperties {

    private boolean enable;
    private boolean debug;
    private String p12File;
    private String p12Password;
    private String appId;

}
