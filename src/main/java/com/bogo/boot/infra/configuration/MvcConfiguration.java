
package com.bogo.boot.infra.configuration;

import com.bogo.boot.infra.mvc.resolver.OriginArgumentResolver;
import com.bogo.boot.infra.mvc.resolver.TokenArgumentResolver;
import com.bogo.boot.infra.mvc.resolver.UidArgumentResolver;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

@Configuration
public class MvcConfiguration extends OncePerRequestFilter implements WebMvcConfigurer {

    @Resource
    private HandlerInterceptor tokenInterceptor;

    @Resource
    private HandlerInterceptor originInterceptor;

    @Resource
    private HandlerInterceptor fileInterceptor;

    private final List<String> apiPathList = new ArrayList<>();

    public MvcConfiguration(){
        apiPathList.add("/organization/**");
        apiPathList.add("/comment/**");
        apiPathList.add("/micro/**");
        apiPathList.add("/moment/**");
        apiPathList.add("/group/**");
        apiPathList.add("/message/**");
        apiPathList.add("/config/**");
        apiPathList.add("/code/**");
        apiPathList.add("/friend/**");
        apiPathList.add("/apns/**");
        apiPathList.add("/tag/**");
        apiPathList.add("/file/**");
        apiPathList.add("/user/**");
        apiPathList.add("/webrtc/**");
        apiPathList.add("/account/**");
        apiPathList.add("/emoticon/**");
        apiPathList.add("/note/**");
        apiPathList.add("/notification/**");
        apiPathList.add("/livekit/meeting/**");
        apiPathList.add("/livekit/p2p/**");
        apiPathList.add("/feedback/**");
    }
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(originInterceptor)
                .order(0)
                .addPathPatterns("/**");

        registry.addInterceptor(tokenInterceptor)
                .order(1)
                .addPathPatterns(apiPathList);

        registry.addInterceptor(fileInterceptor).order(0)
                .addPathPatterns("/file/**","/inner/file/**");

    }


    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new TokenArgumentResolver());
        argumentResolvers.add(new UidArgumentResolver());
        argumentResolvers.add(new OriginArgumentResolver());
    }

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer.favorParameter(false);
        configurer.defaultContentType(MediaType.APPLICATION_JSON);
        configurer.ignoreAcceptHeader(true);
    }


    @Bean
    public CorsFilter corsFilter() {

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.addAllowedOriginPattern("*");
        configuration.addAllowedHeader("*");
        configuration.addAllowedMethod("*");
        configuration.setAllowCredentials(true);
        configuration.addExposedHeader(HttpHeaders.CONTENT_DISPOSITION);
        source.registerCorsConfiguration("/**", configuration);

        return new CorsFilter(source);
    }

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest servletRequest, @NotNull HttpServletResponse servletResponse, FilterChain filterChain) throws ServletException, IOException {
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(servletResponse);
        try {
            filterChain.doFilter(new ContentCachingRequestWrapper(servletRequest), responseWrapper);
        }finally {
            /*
             务必不要忘记这一行
             */
            responseWrapper.copyBodyToResponse();
        }
    }
}