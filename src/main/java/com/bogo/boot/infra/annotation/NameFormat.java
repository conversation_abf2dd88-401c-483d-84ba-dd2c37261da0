
package com.bogo.boot.infra.annotation;


import com.bogo.boot.infra.validator.NameFormatValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Constraint(validatedBy = NameFormatValidator.class)
public @interface NameFormat {

    String message();

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
