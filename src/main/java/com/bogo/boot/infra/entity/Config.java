
package com.bogo.boot.infra.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * 系统配置
 */
@Entity
@Table(name = "t_bogo_config")
@Getter
@Setter
public class Config implements Serializable {

	private static final  long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "name", length = 32,nullable = false)
	private String name;

	@Column(name = "value", length = 2000,nullable = false)
	private String value;

	@Column(name = "domain", length = 32,nullable = false)
	private String domain;

	@Column(name = "description", length = 200)
	private String description;

	@Override
	public String toString() {
        String buffer = "{" +
                "id:" + "'" + (id == null ? "" : id) + "'" +
                "," + "domain:" + "'" + (domain == null ? "" : domain) + "'" +
                "," + "name:" + "'" + (name == null ? "" : name) + "'" +
                "," + "value:" + "'" + (value == null ? "" : value.replaceAll("\n", "")) +
                "'" +
                "," + "description:" + "'" +
                (description == null ? "" : description.replaceAll("\n", "")) + "'" +
                "}";
		return buffer;
	}

}
