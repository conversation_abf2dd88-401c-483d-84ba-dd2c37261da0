
package com.bogo.boot.infra.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 敏感词
 */
@Entity
@Table(name = "t_bogo_blackword")
@Getter
@Setter
public class BlackWord implements Serializable {

 	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "content")
	private String content;

	@Column(name = "description")
	private String description;

	@Column(name = "create_time")
	protected Date createTime;
}
