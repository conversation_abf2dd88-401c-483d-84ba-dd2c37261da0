package com.bogo.boot.infra.service;

import com.bogo.boot.infra.configuration.GeWeApiProperties;
import com.bogo.boot.infra.model.GeWeApiResponse;
import com.bogo.boot.infra.model.GeWeFriendInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GeWe API客户端服务
 */
@Service
@Slf4j
public class GeWeApiService {
    
    @Resource
    private RestTemplate restTemplate;
    
    @Resource
    private GeWeApiProperties geWeApiProperties;
    
    @Resource
    private ObjectMapper objectMapper;
    
    /**
     * 获取通讯录列表
     */
    public List<GeWeFriendInfo> fetchContactsList() {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/contacts/fetchContactsList";
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appId", geWeApiProperties.getAppId());
        
        HttpHeaders headers = createHeaders();
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, request, String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<List<GeWeFriendInfo>> apiResponse = objectMapper.readValue(
                    response.getBody(), 
                    new TypeReference<GeWeApiResponse<List<GeWeFriendInfo>>>() {}
                );
                
                if (apiResponse.isSuccess()) {
                    return apiResponse.getData();
                } else {
                    log.error("获取通讯录列表失败: {}", apiResponse.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用GeWe API获取通讯录列表异常", e);
        }
        
        return List.of();
    }
    
    /**
     * 批量获取好友简要信息
     */
    public List<GeWeFriendInfo> getBriefInfo(List<String> wxids) {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/contacts/getBriefInfo";
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appId", geWeApiProperties.getAppId());
        requestBody.put("wxids", wxids);
        
        HttpHeaders headers = createHeaders();
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, request, String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<List<GeWeFriendInfo>> apiResponse = objectMapper.readValue(
                    response.getBody(), 
                    new TypeReference<GeWeApiResponse<List<GeWeFriendInfo>>>() {}
                );
                
                if (apiResponse.isSuccess()) {
                    return apiResponse.getData();
                } else {
                    log.error("获取好友简要信息失败: {}", apiResponse.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用GeWe API获取好友简要信息异常", e);
        }
        
        return List.of();
    }
    
    /**
     * 发送文字消息
     */
    public boolean sendTextMessage(String toWxid, String content, String ats) {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/message/postText";
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appId", geWeApiProperties.getAppId());
        requestBody.put("toWxid", toWxid);
        requestBody.put("content", content);
        if (ats != null && !ats.isEmpty()) {
            requestBody.put("ats", ats);
        }
        
        HttpHeaders headers = createHeaders();
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, request, String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<Object> apiResponse = objectMapper.readValue(
                    response.getBody(), 
                    new TypeReference<GeWeApiResponse<Object>>() {}
                );
                
                if (apiResponse.isSuccess()) {
                    log.info("发送消息成功: toWxid={}, content={}", toWxid, content);
                    return true;
                } else {
                    log.error("发送消息失败: {}", apiResponse.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用GeWe API发送消息异常", e);
        }
        
        return false;
    }
    
    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-GEWE-TOKEN", geWeApiProperties.getToken());
        headers.set("Content-Type", "application/json");
        return headers;
    }
}