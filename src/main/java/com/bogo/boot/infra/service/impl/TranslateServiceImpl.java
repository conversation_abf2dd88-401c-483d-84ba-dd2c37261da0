
package com.bogo.boot.infra.service.impl;

import com.bogo.boot.infra.configuration.properties.TranslateProperties;
import com.bogo.boot.infra.feign.ITranslateService;
import com.bogo.boot.infra.feign.request.TextTranslateRequest;
import com.bogo.boot.infra.feign.response.BaiduTranslateResponse;
import com.bogo.boot.infra.service.TranslateService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

@Service
public class TranslateServiceImpl implements TranslateService {

	@Resource
	private ITranslateService translateService;

	@Resource
	private TranslateProperties translateProperties;

	@Override
	public String translate(TextTranslateRequest request) {

		if (!translateProperties.isEnable()){
			return request.getText();
		}

		String salt = String.valueOf(System.currentTimeMillis());
		String sign = DigestUtils.md5DigestAsHex((translateProperties.getAppId() + request.getText() + salt + translateProperties.getSecretKey()).getBytes());
		BaiduTranslateResponse result = translateService.translate(request.getText(),request.getLanguage(),translateProperties.getAppId(),salt,sign);

		return result.getText() == null ? request.getText() : result.getText();
	}
}
