
package com.bogo.boot.infra.service.impl;

import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.constant.ValidationCodeAction;
import com.bogo.boot.infra.redis.ValidationCodeRedisTemplate;
import com.bogo.boot.infra.service.ValidationCodeService;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Random;
import org.springframework.stereotype.Service;

@Service
public class ValidationCodeServiceImpl implements ValidationCodeService {

	@Resource
	private ValidationCodeRedisTemplate validationCodeRedisTemplate;

	private final Random random = new Random();

	@Override
	public void send(String telephone, ValidationCodeAction action) {

		StringBuilder builder = new StringBuilder();
		for (int i = 0; i < Common.VALIDATION_CODE_LENGTH; i ++){
			builder.append(random.nextInt(10));
		}

		String code = builder.toString();

		validationCodeRedisTemplate.save(telephone,action,code);

		/*
		 请在这里对接短信服务，自行实现发送短信验证码
		 */
	}

	@Override
	public boolean check(String telephone, String code, ValidationCodeAction action) {

		return Objects.equals(code,validationCodeRedisTemplate.get(telephone,action));
	}

}
