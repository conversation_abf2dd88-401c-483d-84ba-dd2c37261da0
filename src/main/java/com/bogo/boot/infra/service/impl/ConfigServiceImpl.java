
package com.bogo.boot.infra.service.impl;

import com.bogo.boot.infra.entity.Config;
import com.bogo.boot.infra.repository.ConfigRepository;
import com.bogo.boot.infra.service.ConfigService;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
public class ConfigServiceImpl implements ConfigService {

	@Resource
	private ConfigRepository configRepository;

	@Override
	public Map<String, String> queryMap(String domain) {
		List<Config> dataList = configRepository.queryListByDomain(domain);
		HashMap<String, String> map = new HashMap<>(dataList.size());
		for (Config config : dataList) {
			map.put(config.getName(), config.getValue());
		}
		return map;
	}

	@Override
	public List<Config> queryList() {
		return configRepository.findAll();
	}

}
