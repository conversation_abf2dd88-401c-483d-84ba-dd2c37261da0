package com.bogo.boot.infra.validator;

import com.bogo.boot.infra.annotation.NameFormat;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class NameFormatValidator implements ConstraintValidator<NameFormat, String> {

    private static final String[] ILLEGAL_CHAR =  new String[]{"\"","'","@","<",">","$","%","*","&","-","+","{","}","\\"};

    @Override
    public boolean isValid(String text, ConstraintValidatorContext context) {

        if (StringUtils.isBlank(text)) {
            return true;
        }

        return !StringUtils.containsAny(text,ILLEGAL_CHAR);
    }
}
