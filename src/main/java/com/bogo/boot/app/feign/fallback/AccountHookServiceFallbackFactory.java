package com.bogo.boot.app.feign.fallback;


import com.bogo.boot.app.feign.IAccountHookService;
import com.bogo.boot.app.feign.request.AccountHookRequest;
import java.net.URI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class AccountHookServiceFallbackFactory implements FallbackFactory<IAccountHookService>, IAccountHookService {

	private final Logger logger = LoggerFactory.getLogger(AccountHookServiceFallbackFactory.class);

	@Override
	public IAccountHookService create(Throwable throwable) {
		logger.warn("account-hook-service接口熔断",throwable);
		return this;
	}

	@Override
	public void notify(URI uri, AccountHookRequest event) {

	}
}
