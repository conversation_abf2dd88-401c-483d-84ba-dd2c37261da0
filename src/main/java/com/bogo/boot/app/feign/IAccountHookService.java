
package com.bogo.boot.app.feign;

import com.bogo.boot.app.feign.fallback.AccountHookServiceFallbackFactory;
import com.bogo.boot.app.feign.request.AccountHookRequest;
import java.net.URI;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "account-hook-service",url = "*",fallbackFactory = AccountHookServiceFallbackFactory.class)
public interface IAccountHookService {

	/**
	 * 当有用户关注或者取消关注时，调用公众号服务端接口，因为是异步放法，需要在外部调用，所以分离开来
	 *
	 * @param event   关注 或者 取消关注
	 */
	@PostMapping(produces = "application/json")
	void notify(URI uri, @RequestBody AccountHookRequest event);
}
