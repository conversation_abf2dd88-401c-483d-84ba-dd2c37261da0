
package com.bogo.boot.app.repository.specification;


import com.bogo.boot.app.entity.MicroServer;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.domain.Specification;

public class MicroServerSpecification implements Specification<MicroServer> {


    private final String name;

    private final long index;

    private MicroServerSpecification(long index ,String name) {
        this.name = name;
        this.index = index;
    }


    @Override
    public Predicate toPredicate(Root<MicroServer> root, @NotNull CriteriaQuery<?> query, CriteriaBuilder builder) {

        List<Predicate> predicatesList = new ArrayList<>();

        predicatesList.add(builder.gt(root.get("id").as(Long.class), index));

        if (StringUtils.isNotBlank(name)){
            predicatesList.add(builder.like(root.get("name").as(String.class),  "%" + name + "%"));
        }

        query.where(predicatesList.toArray(new Predicate[0]));

        query.orderBy(builder.desc(root.get("id").as(Long.class)));

        return query.getRestriction();
    }


    public static MicroServerSpecification search(long index ,String name) {
        return new MicroServerSpecification(index,name);
    }


}
