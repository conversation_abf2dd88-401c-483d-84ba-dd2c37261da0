
package com.bogo.boot.app.repository;

import com.bogo.boot.app.entity.MicroServerMenu;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MicroServerMenuRepository extends JpaRepository<MicroServerMenu, Long> {
	@Override
	@Modifying
	@Query("delete from MicroServerMenu  where  id  = ?1 or parentId = ?1 ")
	void deleteById(@NotNull Long id);

	@Query("from MicroServerMenu where serverId =?1 order by sort")
	List<MicroServerMenu> getList(long serverId);
}
