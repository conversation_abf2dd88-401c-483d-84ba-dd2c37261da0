
package com.bogo.boot.app.repository;

import com.bogo.boot.app.entity.Subscriber;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface SubscriberRepository extends JpaRepository<Subscriber, Long> {

	@Modifying
	@Query("delete from Subscriber where uid = ?1 and serverId= ?2")
	void delete(long uid, long serverId);

	@Query("select uid from Subscriber where serverId = ?1")
	List<Long> findUidList(long serverId);

	@Query("select count(*) from Subscriber where uid = ?1 and serverId = ?2 ")
	long count(long uid, long serverId);

}
