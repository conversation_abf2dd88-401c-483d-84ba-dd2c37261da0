
package com.bogo.boot.app.repository;

import com.bogo.boot.app.entity.MicroServer;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface MicroServerRepository extends JpaRepository<MicroServer, Long> {

	Page<MicroServer> findAll(Specification<MicroServer> sp, Pageable pageable);

	@Query("select a  from MicroServer as a left join Subscriber as s on a.id = s.serverId where  s.uid = ?1")
	List<MicroServer> queryListByUid(long uid);

	@Query("from MicroServer where account = ?1")
	MicroServer findOne(String account);

	@Query("select webhook from MicroServer where id = ?1")
	String getWebhook(Long id);

	@Query("select name from MicroServer where id = ?1")
	String findName(long id);
}
