package com.bogo.boot.app.observer;

import com.bogo.boot.app.event.SubscriberEvent;
import com.bogo.boot.app.feign.IAccountHookService;
import com.bogo.boot.app.feign.request.AccountHookRequest;
import com.bogo.boot.app.repository.MicroServerRepository;
import jakarta.annotation.Resource;
import java.net.URI;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class AccountHookObserver implements ApplicationListener<SubscriberEvent> {

    @Resource
    private IAccountHookService accountHookService;

    @Resource
    private MicroServerRepository microServerRepository;

    @Override
    @Async("feignTaskExecutor")
    public void onApplicationEvent(SubscriberEvent event) {

        String webhook = microServerRepository.getWebhook(event.getServerId());

        if (StringUtils.isBlank(webhook)) {
            return;
        }

        AccountHookRequest request = new AccountHookRequest();
        request.setUid(event.getUid());
        request.setAction(event.getAction());
        accountHookService.notify(URI.create(webhook),request);
    }

}
