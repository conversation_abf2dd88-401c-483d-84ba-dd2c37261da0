
package com.bogo.boot.app.api.vo;

import com.bogo.boot.app.entity.MicroServerMenu;
import com.bogo.boot.infra.model.Sortable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "公众号菜单信息")
@Getter
@Setter
public class MicroServerMenuVO implements Sortable {

	@Schema(title = "ID")
	private Long id;

	@Schema(title = "上级菜单ID")
	private Long parentId;

	@Schema(title = "名称")
	private String name;

	@Schema(title = "编号")
	private String code;

	@Schema(title = "类型，0:显示菜单 1:调用webhook 2:打开web地址 3:显示文案")
	private Byte type;

	@Schema(title = "内容,Web地址或者显示文案")
	private String content;

	@Schema(title = "显示排序,升序")
	private int sort;

	public static MicroServerMenuVO of(MicroServerMenu menu){
		MicroServerMenuVO vo = new MicroServerMenuVO();
		vo.id = menu.getId();
		vo.parentId = menu.getParentId();
		vo.name = menu.getName();
		vo.code = menu.getCode();
		vo.type = menu.getType();
		vo.content = menu.getContent();
		vo.sort = menu.getSort();
		return vo;
	}
}
