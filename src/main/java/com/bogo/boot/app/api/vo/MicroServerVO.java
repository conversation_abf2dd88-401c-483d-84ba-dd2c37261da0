
package com.bogo.boot.app.api.vo;

import com.bogo.boot.app.entity.MicroServer;
import com.bogo.boot.infra.model.Sortable;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "公众号信息")
@Getter
@Setter
public class MicroServerVO implements Sortable {

	@Schema(title = "ID")
	private Long id;

	@Schema(title = "公众号账号")
	private String account;

	@Schema(title = "简介")
	private String description;

	@Schema(title = "名称")
	private String name;

	@Schema(title = "主页地址")
	private String website;

	@Schema(title = "关注后欢迎语")
	private String greet;

	@Schema(title = "事件请求接口地址")
	private String webhook;

	@Schema(title = "菜单列表")
	private List<MicroServerMenuVO> menuList;

	public static MicroServerVO of(MicroServer server){
		if (server == null){
			return null;
		}
		MicroServerVO vo = new MicroServerVO();
		vo.account = server.getAccount();
		vo.name = server.getName();
		vo.id = server.getId();
		vo.webhook = server.getWebhook();
		vo.website = server.getWebsite();
		vo.description = server.getDescription();
		vo.greet = server.getGreet();
		return vo;
	}
}
