
package com.bogo.boot.app.api;

import com.bogo.boot.app.api.vo.MicroServerMenuVO;
import com.bogo.boot.app.api.vo.MicroServerVO;
import com.bogo.boot.app.entity.Subscriber;
import com.bogo.boot.app.service.MicroServerMenuService;
import com.bogo.boot.app.service.MicroServerService;
import com.bogo.boot.app.service.SubscriberService;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.model.PaginationEntity;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.util.PageX;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/micro/server")
@Tag(name = "公众号相关接口" )
public class MicroServerController {

	@Resource
	private MicroServerService microServerService;

	@Resource
	private SubscriberService subscriberService;

	@Resource
	private MicroServerMenuService microServerMenuService;

	@Operation( summary = "获取公众号详情")
	@Parameter(name = "id", description = "公众号ID", in = ParameterIn.PATH,example = "0",required = true)
	@GetMapping(value = "/{id}")
	public ResponseEntity<MicroServerVO> get(@PathVariable Long id) {
		return ResponseEntity.ok(microServerService.findOne(id));
	}


	@Operation( summary = "搜索公众号")
	@Parameters({
			@Parameter(name = "index", description = "当前页最大ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "currentPage", description = "当前页数(初始0)", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "name", description = "关键词", in = ParameterIn.QUERY),
	})
	@GetMapping(value = "/search/{index}/{currentPage}")
	public PaginationEntity<MicroServerVO> search(@PathVariable long index,
                                                  @PathVariable int currentPage,
                                                  @RequestParam(required = false) String name) {
		Pageable pageable = PageRequest.of(currentPage, Common.API_PAGE_SIZE);

		Page<MicroServerVO> page = microServerService.search(index,name,pageable);

		return PaginationEntity.make(page);
	}

	@Operation( summary = "订阅公众号")
	@Parameter(name = "id", description = "公众号ID", in = ParameterIn.PATH,example = "0",required = true)
	@PostMapping(value = "/subscribe/{id}")
	public ResponseEntity<Void> subscribe(@PathVariable Long id, @Parameter(hidden = true) @UID Long uid) {

		Subscriber subscriber = new Subscriber();
		subscriber.setServerId(id);
		subscriber.setUid(uid);
		subscriberService.add(subscriber);

		return ResponseEntity.ok();
	}

	@Operation( summary = "取消订阅公众号")
	@Parameter(name = "id", description = "公众号ID", in = ParameterIn.PATH,example = "0",required = true)
	@DeleteMapping(value = "/subscribe/{id}")
	public ResponseEntity<Void> unsubscribe(@PathVariable Long id, @Parameter(hidden = true) @UID Long uid) {

		Subscriber subscriber = new Subscriber();
		subscriber.setServerId(id);
		subscriber.setUid(uid);
		subscriberService.delete(subscriber);

		return ResponseEntity.ok();
	}

	@Operation( summary = "获取公众号菜单列表")
	@Parameter(name = "id", description = "公众号ID", in = ParameterIn.PATH,example = "0",required = true)
	@GetMapping(value = "/menu/list/{id}")
	public ResponseEntity<List<MicroServerMenuVO>> list(@PathVariable Long id) {
		return ResponseEntity.ok(PageX.map(MicroServerMenuVO::of,microServerMenuService.queryList(id)));
	}
}
