package com.bogo.boot.app.event;

import com.bogo.boot.app.entity.Subscriber;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class SubscriberEvent extends ApplicationEvent {

    private final String action;

    public SubscriberEvent(Subscriber subscriber,String action) {
        super(subscriber);
        this.action = action;
    }

    public long getUid() {
        return ((Subscriber) source).getUid();
    }

    public long getServerId() {
        return ((Subscriber) source).getServerId();
    }
}
