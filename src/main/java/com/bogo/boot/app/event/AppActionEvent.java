package com.bogo.boot.app.event;

import com.bogo.boot.message.entity.EventMessage;
import com.bogo.boot.message.entity.Message;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;


@Getter
public class AppActionEvent extends ApplicationEvent {

    private final Message message = new EventMessage();

    public AppActionEvent() {
        super("");
    }

    public void setAction(String action) {
        message.setAction(action);
    }

    public void setContent(String content) {
        message.setContent(content);
    }

    public void setExtra(String extra) {
        message.setExtra(extra);
    }

    public void setSender(Long sender) {
        message.setSender(sender);
    }
}
