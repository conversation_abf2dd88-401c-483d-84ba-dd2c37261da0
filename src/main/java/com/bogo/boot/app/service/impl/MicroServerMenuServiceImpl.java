
package com.bogo.boot.app.service.impl;

import com.bogo.boot.app.entity.MicroServerMenu;
import com.bogo.boot.app.repository.MicroServerMenuRepository;
import com.bogo.boot.app.service.MicroServerMenuService;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class MicroServerMenuServiceImpl implements MicroServerMenuService {

	@Resource
	private MicroServerMenuRepository microServerMenuRepository;

	@Override
	public List<MicroServerMenu> queryList(long serverId) {
		return microServerMenuRepository.getList(serverId);
	}

}
