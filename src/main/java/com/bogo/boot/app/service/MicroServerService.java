
package com.bogo.boot.app.service;

import com.bogo.boot.app.api.vo.MicroServerVO;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface MicroServerService {

	String getName(long id);

	MicroServerVO findOne(Long id);

	String getWebhook(Long id);

	MicroServerVO findOne(String account);

	List<MicroServerVO> findList(Long uid);

	Page<MicroServerVO> search(long index, String name, Pageable page);
}
