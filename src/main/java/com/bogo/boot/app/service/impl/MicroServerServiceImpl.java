
package com.bogo.boot.app.service.impl;

import com.bogo.boot.app.api.vo.MicroServerMenuVO;
import com.bogo.boot.app.api.vo.MicroServerVO;
import com.bogo.boot.app.entity.MicroServer;
import com.bogo.boot.app.redis.MicroServerRedisTemplate;
import com.bogo.boot.app.repository.MicroServerMenuRepository;
import com.bogo.boot.app.repository.MicroServerRepository;
import com.bogo.boot.app.repository.specification.MicroServerSpecification;
import com.bogo.boot.app.service.MicroServerService;
import com.bogo.boot.infra.util.PageX;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class MicroServerServiceImpl implements MicroServerService {

	@Resource
	private MicroServerRepository microServerRepository;

	@Resource
	private MicroServerMenuRepository microServerMenuRepository;

	@Resource
	private MicroServerRedisTemplate microServerRedisTemplate;

	private final LoadingCache<Long, Optional<String>> nameCache = CacheBuilder.newBuilder()
			.maximumSize(100)
			.expireAfterAccess(10, TimeUnit.MINUTES)
			.build(CacheLoader.from(id -> Optional.ofNullable(microServerRepository.findName(id))));

	@Override
	public String getName(long id) {
		return nameCache.getUnchecked(id).orElse(null);
	}

	@Override
	public MicroServerVO findOne(Long id) {

		MicroServer server = microServerRedisTemplate.get(id);

		if (server != null){
			return MicroServerVO.of(server);
		}

		MicroServer newServer = microServerRepository.findById(id).orElse(null);

		if (newServer != null) {
			microServerRedisTemplate.save(newServer);
		}

		return MicroServerVO.of(newServer);
	}

	@Override
	public String getWebhook(Long id) {
		return microServerRepository.getWebhook(id);
	}


	@Override
	public List<MicroServerVO> findList(Long uid) {

		List<MicroServerVO> voList = PageX.map(MicroServerVO::of,microServerRepository.queryListByUid(uid));

		voList.forEach(server -> server.setMenuList(PageX.map(MicroServerMenuVO::of,microServerMenuRepository.getList(server.getId()))));

		return voList;
	}

	@Override
	public MicroServerVO findOne(String account) {
		return MicroServerVO.of(microServerRepository.findOne(account));
	}

	@Override
	public Page<MicroServerVO> search(long index , String name, Pageable pageable) {
		Specification<MicroServer> condition = MicroServerSpecification.search(index,name);
		Page<MicroServer> page =  microServerRepository.findAll(condition,pageable);

		return PageX.map(MicroServerVO::of,page);
	}

}
