
package com.bogo.boot.app.service.impl;

import com.bogo.boot.app.entity.Subscriber;
import com.bogo.boot.app.event.SubscriberEvent;
import com.bogo.boot.app.feign.request.AccountHookRequest;
import com.bogo.boot.app.repository.SubscriberRepository;
import com.bogo.boot.app.service.SubscriberService;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class SubscriberServiceImpl implements SubscriberService {

	@Resource
	private SubscriberRepository subscriberRepository;

	@Resource
	private ApplicationEventProducer applicationEventProducer;


	@Override
	public void add(Subscriber subscriber) {

		boolean isSubscribed = subscriberRepository.count(subscriber.getUid(),subscriber.getServerId()) > 0;
		if (isSubscribed) {
			return;
		}
		subscriber.setCreateTime(new Date());
		subscriberRepository.saveAndFlush(subscriber);
		applicationEventProducer.publish(new SubscriberEvent(subscriber, AccountHookRequest.ACTION_SUBSCRIBE));
	}


	@Override
	public void delete(Subscriber subscriber) {
		subscriberRepository.delete(subscriber.getUid(), subscriber.getServerId());
		applicationEventProducer.publish(new SubscriberEvent(subscriber, AccountHookRequest.ACTION_UNSUBSCRIBE));
	}

	@Override
	public List<Long> findUidList(long id) {
		return subscriberRepository.findUidList(id);
	}

}
