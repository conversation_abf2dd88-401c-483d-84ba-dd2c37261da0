
package com.bogo.boot.app.redis;

import com.bogo.boot.app.entity.MicroServer;
import com.bogo.boot.infra.util.JSON;
import java.nio.charset.StandardCharsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

@Component
public class MicroServerRedisTemplate extends RedisTemplate<String, MicroServer>
		implements RedisSerializer<MicroServer> {

	private static final String CACHE_PREFIX = "ACCOUNT_";

	@Autowired
	public MicroServerRedisTemplate(RedisConnectionFactory connectionFactory) {
		StringRedisSerializer stringSerializer = new StringRedisSerializer();

		setKeySerializer(stringSerializer);
		setHashKeySerializer(stringSerializer);

		setValueSerializer(this);
		setHashValueSerializer(this);
		setConnectionFactory(connectionFactory);
		afterPropertiesSet();
	}

	public void save(MicroServer server) {
		String key = CACHE_PREFIX + server.getId();
		super.boundValueOps(key).set(server);
	}

	public MicroServer get(long id) {
		String key = CACHE_PREFIX + id;
		return super.boundValueOps(key).get();
	}

	public void remove(long id) {
		String key = CACHE_PREFIX + id;
		super.delete(key);
	}

	@Override
	public byte[] serialize(MicroServer server) throws SerializationException {
		return JSON.toJSONString(server).getBytes(StandardCharsets.UTF_8);
	}

	@Override
	public MicroServer deserialize(byte[] bytes) throws SerializationException {
		 return JSON.parseNullable(bytes,MicroServer.class);
	}

}
