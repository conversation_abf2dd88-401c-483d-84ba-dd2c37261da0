
package com.bogo.boot.app.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 公众账号
 */
@Entity
@Table(name = "t_bogo_micro_server")
@Getter
@Setter
public class MicroServer{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "account", length = 32,unique = true,nullable = false)
    private String account;

    @Column(name = "description", length = 320)
    private String description;

    @Column(name = "name", length = 20,nullable = false)
    private String name;

    @Column(name = "website", length = 320)
    private String website;

    @Column(name = "greet", length = 320)
    private String greet;

    @Column(name = "webhook", length = 320)
    private String webhook;

    @Column(name = "create_time",nullable = false)
    private Date createTime;

}
