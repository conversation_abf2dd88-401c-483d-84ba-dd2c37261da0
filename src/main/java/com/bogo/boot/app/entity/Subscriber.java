
package com.bogo.boot.app.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 公众账号订阅者
 */
@Entity
@Table(name = "t_bogo_subscriber")
@Getter
@Setter
public class Subscriber{

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	/**
	 * 用户id
	 */
	@Column(name = "uid",nullable = false)
	private Long uid;

	/**
	 *公众号ID
	 */
	@Column(name = "server_id",nullable = false)
	private Long serverId;

	@Column(name = "create_time", nullable = false)
	private Date createTime;

}
