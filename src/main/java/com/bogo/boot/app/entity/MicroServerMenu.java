
package com.bogo.boot.app.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

/**
 * 公众账号菜单
 */
@Entity
@Table(name = "t_bogo_micro_server_menu")
@Getter
@Setter
public class MicroServerMenu{

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "parent_id")
	private Long parentId;

	@Column(name = "server_id",nullable = false)
	private Long serverId;

	@Column(name = "name", length = 20,nullable = false)
	private String name;

	@Column(name = "code", length = 32,nullable = false)
	private String code;

    /**
     * 菜单类型
     *
     * @see com.bogo.boot.app.constant.MenuType
     */
	@Column(name = "type",nullable = false)
	private Byte type;

    /**
     * 菜单内容 根据type不同的含义
	 */
	@Column(name = "content", length = 1024)
	private String content;

    /**
     * 显示顺序，倒序
	 */
	@Column(name = "sort")
	private int sort;

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

	@Override
	public boolean equals(Object o) {
        if (o instanceof MicroServerMenu target) {
            return Objects.equals(target.id, id);
		}
		return false;
	}

}
