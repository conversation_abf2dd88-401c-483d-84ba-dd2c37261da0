
package com.bogo.boot;

import lombok.Getter;

/**
 * 业务线包名说明
 */
@Getter
public enum Package {

    /*
     * 当前账号相关业务
     */
    ACCOUNT("com.bogo.boot.account"),

    /*
     *表情包业务
     */
    EMOJI("com.bogo.boot.emoji"),

    /*
     *联系人相关业务
     */
    CONTACT("com.bogo.boot.contact"),

    /*
     *基础的业务
     */
    INFRA("com.bogo.boot.infra"),

    /*
     *群组相关业务
     */
    GROUP("com.bogo.boot.group"),

    /*
     *文件存储相关业务
     */
    FILE("com.bogo.boot.file"),

    /*
     *消息和聊天业务业务
     */
    MESSAGE("com.bogo.boot.message"),

    /*
     *公众号相关业务
     */
    APP("com.bogo.boot.app"),

    /*
     *朋友圈相关业务
     */
    MOMENT("com.bogo.boot.moment"),

    /*
     *音视频通话相关业务
     */
    WEBRTC("com.bogo.boot.account");

    private final String packageName;

    Package(String packageName) {
        this.packageName = packageName;
    }

}
