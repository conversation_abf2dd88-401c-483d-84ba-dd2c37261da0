package com.bogo.boot.group.redis;

import com.bogo.boot.group.entity.GroupMember;
import com.bogo.boot.infra.redis.LongRedisTemplate;
import java.util.List;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

@Component
public class GroupMemberRedisTemplate extends LongRedisTemplate {

	private static final String CACHE_PREFIX = "GROUP_MEMBER_";

	@Autowired
	public GroupMemberRedisTemplate(RedisConnectionFactory connectionFactory) {
		super(connectionFactory);
	}

	public void add(GroupMember member) {
		String key = CACHE_PREFIX + member.getGroupId();
		super.add(key,member.getUid());
	}

	public boolean isMember(Long groupId , long uid) {
		String key = CACHE_PREFIX + groupId;
		return BooleanUtils.isTrue(super.boundSetOps(key).isMember(uid));
	}

	public void add(Long groupId , List<Long> idList) {
		String key = CACHE_PREFIX + groupId;
		super.add(key,idList);
	}

	public long size(long groupId) {
		String key = CACHE_PREFIX + groupId;
		return super.size(key);
	}

	public List<Long> findUidList(long groupId) {
		String key = CACHE_PREFIX + groupId;
		return super.findList(key);
	}

	public void remove(long groupId) {
		String key = CACHE_PREFIX + groupId;
		super.delete(key);
	}

	public void remove(Long groupId , List<Long> idList) {
		String key = CACHE_PREFIX + groupId;
		super.boundSetOps(key).remove(idList.toArray());
	}

	public void remove(GroupMember member) {
		String key = CACHE_PREFIX + member.getGroupId();
		super.remove(key,member.getUid());
	}
}
