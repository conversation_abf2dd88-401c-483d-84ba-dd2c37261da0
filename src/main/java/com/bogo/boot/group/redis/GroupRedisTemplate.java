package com.bogo.boot.group.redis;

import com.bogo.boot.group.entity.Group;
import com.bogo.boot.infra.util.JSON;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

@Component
public class GroupRedisTemplate extends RedisTemplate<String, Group> implements RedisSerializer<Group> {

	private static final String CACHE_PREFIX = "GROUP_";

	@Autowired
	public GroupRedisTemplate(RedisConnectionFactory connectionFactory) {

		StringRedisSerializer stringSerializer = new StringRedisSerializer();

		setKeySerializer(stringSerializer);
		setHashKeySerializer(stringSerializer);

		setValueSerializer(this);
		setHashValueSerializer(this);

		setConnectionFactory(connectionFactory);
		afterPropertiesSet();
	}

	public void save(Group group) {
		if (group == null) {
			return;
		}
		String key = CACHE_PREFIX + group.getId();
		super.boundValueOps(key).set(group);
	}

	public Group get(long id) {
		String key = CACHE_PREFIX + id;
		return super.boundValueOps(key).get();
	}

	public void remove(long id) {
		String key = CACHE_PREFIX + id;
		super.delete(key);
	}



	@Override
	public byte[] serialize(Group group) throws SerializationException {
		return JSON.toJSONString(group).getBytes(StandardCharsets.UTF_8);
	}

	@Override
	public Group deserialize(byte[] bytes) throws SerializationException {
		return JSON.parseNullable(bytes,Group.class);
	}

    public void save(Optional<Group> group) {
        group.ifPresent(this::save);
    }
}
