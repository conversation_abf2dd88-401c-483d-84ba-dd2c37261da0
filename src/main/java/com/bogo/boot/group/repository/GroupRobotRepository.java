
package com.bogo.boot.group.repository;

import com.bogo.boot.group.entity.GroupRobot;
import com.bogo.boot.infra.constant.CommonState;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface GroupRobotRepository extends JpaRepository<GroupRobot, Long> {

	@Query("from GroupRobot   where groupId = ?1 order by id desc")
	List<GroupRobot> findList(Long groupId);

	@Query("from GroupRobot   where groupId in(?1) order by id desc")
	List<GroupRobot> findList(List<Long> idList);

	@Query("select id from GroupRobot where groupId = :groupId and uid in(:uidList)")
	List<Long> findIdList(Long groupId, List<Long> uidList);

	@Modifying
	@Query("update GroupRobot set uid = :uid where  id in(:idList)")
	void transfer(List<Long> idList, Long uid);

	@Modifying
	@Query("delete from GroupRobot where groupId = ?1")
	void removeAll(Long groupId);

	@Modifying
	@Query("update GroupRobot set state = "+ CommonState.DISABLED+" where  id=?1")
	void disable(Long id);

	@Modifying
	@Query("update GroupRobot set state = "+ CommonState.ENABLE+" where  id=?1")
	void enable(Long id);

	@Query("from GroupRobot where uuid = ?1")
	GroupRobot findByUuid(String uuid);
	@Query("select groupId from GroupRobot where id = :id")
	Long findGroupId(long id);
}
