
package com.bogo.boot.group.repository;

import com.bogo.boot.group.entity.Group;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface GroupRepository extends JpaRepository<Group, Long> {

	@Query("select groupId from GroupMember where uid = ?1")
	List<Long> findIdList(Long uid);

	@Query("from Group where id in(?1)")
	List<Group> findList(List<Long> idList);

	@Query("select uid from Group where id = ?1")
	Long findUid(Long id);

	@Modifying
	@Query("UPDATE Group set state = ?2 where id = ?1")
	void updateState(Long id, byte state);

	@Modifying
	@Query("UPDATE Group set uid = ?2 where id = ?1")
	void updateUid(Long id, long uid);

	@Modifying
	@Query("UPDATE Group set name = ?2 where id = ?1")
	void updateName(Long id, String name);

	@Modifying
	@Query("UPDATE Group set notice = ?2 where id = ?1")
	void updateNotice(Long id, String notice);

	@Query("select name from Group where id = ?1")
	String findName(Long id);
}
