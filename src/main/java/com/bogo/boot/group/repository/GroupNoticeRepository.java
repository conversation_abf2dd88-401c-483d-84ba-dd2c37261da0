
package com.bogo.boot.group.repository;

import com.bogo.boot.group.entity.GroupNotice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


@Repository
@Transactional(rollbackFor = Exception.class)
public interface GroupNoticeRepository extends JpaRepository<GroupNotice, Long> {

	Page<GroupNotice> findAll(Specification<GroupNotice> sp, Pageable pageable);

}
