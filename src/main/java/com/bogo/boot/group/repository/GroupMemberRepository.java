
package com.bogo.boot.group.repository;

import com.bogo.boot.group.entity.GroupMember;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface GroupMemberRepository extends JpaRepository<GroupMember, Long> {

	@Query("from GroupMember where groupId in (?1) order by type desc")
	List<GroupMember> findList(List<Long> groupIdList);

	@Modifying
	@Query("delete FROM GroupMember where  groupId=?1 and uid=?2")
	void remove(Long groupId, Long uid);

	@Modifying
	@Query("delete FROM GroupMember where groupId= ?1")
	void removeAll(Long groupId);

	@Query("select distinct uid from  GroupMember  where groupId = ?1")
	List<Long> findUidList(Long groupId);

	@Modifying
	@Query("delete FROM GroupMember where  groupId=?1 and uid in (?2)")
	void remove(Long groupId, List<Long> idList);

	@Query("select count(*) from GroupMember where groupId = ?1 and uid = ?2 ")
	long count(Long groupId,long uid);

	@Query("from GroupMember where groupId = ?1 and uid = ?2")
	GroupMember findOne(Long groupId ,Long uid);

	@Modifying
	@Query("update GroupMember set type = ?3 where groupId = ?1 and uid = ?2")
	void updateType(Long groupId,long uid,byte type);

	@Modifying
	@Query("update GroupMember set alias = :alias where groupId = :groupId and uid = :uid")
	void updateAlias(Long groupId ,Long uid,String alias);

	@Query("select alias from GroupMember where groupId = ?1 and uid = ?2")
	String findAlias(Long groupId ,Long uid);

}
