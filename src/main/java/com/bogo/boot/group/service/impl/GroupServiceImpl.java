
package com.bogo.boot.group.service.impl;

import com.bogo.boot.account.service.UserService;
import com.bogo.boot.group.api.request.GroupCreateRequest;
import com.bogo.boot.group.api.request.GroupInviteRequest;
import com.bogo.boot.group.bo.GroupBO;
import com.bogo.boot.group.bo.GroupMemberBO;
import com.bogo.boot.group.constant.GroupState;
import com.bogo.boot.group.constant.MemberType;
import com.bogo.boot.group.entity.Group;
import com.bogo.boot.group.entity.GroupMember;
import com.bogo.boot.group.entity.GroupRobot;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.group.event.GroupDeleteEvent;
import com.bogo.boot.group.redis.GroupRedisTemplate;
import com.bogo.boot.group.repository.GroupRepository;
import com.bogo.boot.group.service.GroupMemberService;
import com.bogo.boot.group.service.GroupRobotService;
import com.bogo.boot.group.service.GroupService;
import com.bogo.boot.group.util.GroupMemberX;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.constant.MessageActionGroup;
import com.bogo.boot.message.repository.MessageEventRepository;
import com.bogo.boot.message.repository.MessageGroupRepository;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class GroupServiceImpl implements GroupService{

	@Resource
	private GroupRepository groupRepository;

	@Resource
	private MessageEventRepository messageEventRepository;

	@Resource
	private GroupMemberService groupMemberService;

	@Resource
	private GroupRobotService groupRobotService;

	@Resource
	private MessageGroupRepository messageGroupRepository;

	@Resource
	private GroupRedisTemplate groupRedisTemplate;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private UserService userService;

	private final LoadingCache<Long, Optional<String>> nameCache;

	public GroupServiceImpl(){
		nameCache = CacheBuilder.newBuilder()
				.maximumSize(200)
				.expireAfterAccess(1, TimeUnit.HOURS)
				.build(CacheLoader.from(id -> Optional.ofNullable(groupRepository.findName(id))));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Group create(GroupCreateRequest request) {

		long makerUid = UidHolder.getUid();

		Group group = new Group();
		group.setUid(makerUid);
		group.setName(request.getName());
		group.setNotice(request.getNotice());
		group.setCreateTime(new Date());
		group.setState(GroupState.NORMAL.getValue());
		groupRepository.saveAndFlush(group);
		groupRedisTemplate.save(group);

		/* 保存群成员 */
		request.getIdList().remove(makerUid);
		List<GroupMember> members = GroupMemberX.create(group.getId(),request.getIdList(),MemberType.MEMBER.getValue());
		members.add(0,GroupMemberX.create(group.getId(),makerUid,MemberType.MASTER.getValue()));

		groupMemberService.add(members);

		/* 通知建群事件 */
		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setMakerUid(makerUid);
		groupEvent.setAction(MessageAction.ACTION_300);
		groupEvent.setSender(group.getId());
		groupEvent.setExtra(String.valueOf(makerUid));
		groupEvent.setUidList(request.getIdList());
		groupEvent.addUid(makerUid);

		applicationEventProducer.publishAsync(groupEvent);

		return group;
	}


	@Override
	public List<GroupBO> findList(Long uid) {

		List<Long> idList = groupRepository.findIdList(uid);

		if (CollectionUtils.isEmpty(idList)){
			return Collections.emptyList();
		}

		List<Group> groups = groupRepository.findList(idList);

		Map<Long,List<GroupRobot>> robots = groupRobotService.findList(idList).stream().collect(Collectors.groupingBy(GroupRobot::getGroupId));

		Map<Long,List<GroupMemberBO>> members = groupMemberService.findList(idList).stream().collect(Collectors.groupingBy(GroupMemberBO::getGroupId));

		return groups.stream().map(group -> {
            GroupBO bo = new GroupBO(group);
            bo.setMembers(members.getOrDefault(group.getId(),Collections.emptyList()));
            bo.setRobots(robots.getOrDefault(group.getId(),Collections.emptyList()));
            return bo;
        }).collect(Collectors.toList());

	}

	@Override
	public GroupBO findOne(long id, boolean loadMembers) {
		AtomicReference<Group> groupRef = new AtomicReference<>();

		Group cache = groupRedisTemplate.get(id);
		groupRef.set(cache);

		if (groupRef.get() == null) {
			groupRef.set(groupRepository.findById(id).orElse(null));
			groupRedisTemplate.save(groupRef.get());
		}

		if (groupRef.get() == null) {
			return null;
		}

		GroupBO group = new GroupBO(groupRef.get());
		if (loadMembers) {
			group.setMembers(groupMemberService.findList(id));
			group.setRobots(groupRobotService.findList(id));
		}

		return group;
	}



	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(long id) {

		Group group = groupRepository.findById(id).orElse(null);
		if (group == null) {
			return;
		}

		long makerUid = UidHolder.getUid() == null ? group.getUid() : UidHolder.getUid();
		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setMakerUid(makerUid);
		groupEvent.setSender(id);
		groupEvent.setAction(MessageAction.ACTION_303);
		groupEvent.setExtra(String.valueOf(makerUid));
		// 如果是管理后台操作，也通知群主
		groupEvent.addIgnored(UidHolder.getUid());
		groupEvent.setUidList(groupMemberService.findUidList(id));
		applicationEventProducer.publish(groupEvent);

		groupRepository.deleteById(id);
		groupMemberService.removeAll(id);
		groupRobotService.removeAll(id);

		nameCache.invalidate(id);

		messageGroupRepository.deleteAll(id);

		/*
		 * 删除群相关的事件消息
		 */
		messageEventRepository.deleteAll(id, MessageActionGroup.GROUP_EVENT);

		groupRedisTemplate.remove(id);

		applicationEventProducer.publishAsync(new GroupDeleteEvent(id));

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateState(Long id, GroupState state) {
		Group group = groupRepository.findById(id).orElse(null);
		if (group == null) {
			return;
		}

		groupRepository.updateState(id,state.getValue());
		groupRedisTemplate.remove(id);

		long markerUid = UidHolder.getUid() == null ? group.getUid() : UidHolder.getUid();
		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setMakerUid(markerUid);
		groupEvent.setSender(id);
		groupEvent.setExtra(String.valueOf(markerUid));
		if (state == GroupState.BLOCK) {
			groupEvent.setAction(MessageAction.ACTION_308);
		}
		if (state == GroupState.NORMAL) {
			groupEvent.setAction(MessageAction.ACTION_309);
		}
		applicationEventProducer.publish(groupEvent);
	}

	@Override
	public void invite(GroupInviteRequest request) {

		long makerUid = UidHolder.getUid();
		long groupId = request.getGroupId();

		/*
		 * 如果都被加入过了就忽略处理
		 */
		List<GroupMember> members = GroupMemberX.create(groupId,request.getIdList(),MemberType.MEMBER.getValue());

		List<Long> addedUidList = groupMemberService.add(members);
		if (addedUidList.isEmpty()){
			return;
		}

		GroupActionEvent groupEvent300 = new GroupActionEvent();
		groupEvent300.setAction(MessageAction.ACTION_300);
		groupEvent300.setSender(groupId);
		groupEvent300.setExtra(String.valueOf(makerUid));
		groupEvent300.setUidList(addedUidList);
		groupEvent300.setReplaceable(false);
		applicationEventProducer.publish(groupEvent300);

		Map<Long,String> nameMap = userService.findName(addedUidList);

		GroupActionEvent groupEvent311 = new GroupActionEvent();
		groupEvent311.setMakerUid(makerUid);
		groupEvent311.setAction(MessageAction.ACTION_311);
		groupEvent311.setSender(groupId);
		groupEvent311.setContent(JSON.toJSONString(nameMap));
		groupEvent311.setExtra(String.valueOf(makerUid));
		groupEvent311.addIgnored(addedUidList);
		groupEvent311.addIgnored(makerUid);
		groupEvent311.setReplaceable(false);
		applicationEventProducer.publish(groupEvent311);

	}


	@Override
	public String getName(long id) {
		return nameCache.getUnchecked(id).orElse(null);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateName(long id, String name) {

		groupRepository.updateName(id,name);
		groupRedisTemplate.remove(id);

		nameCache.invalidate(id);

		long uid = UidHolder.getUid();

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(id);
		groupEvent.setExtra(String.valueOf(uid));
		groupEvent.setAction(MessageAction.ACTION_304);
		groupEvent.setContent(name);

		applicationEventProducer.publish(groupEvent);

	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void transfer(long id,long uid) {

		Long createUid = groupRepository.findUid(id);

		groupRepository.updateUid(id,uid);

		groupRedisTemplate.remove(id);

		groupMemberService.updateType(id,createUid,MemberType.MEMBER.getValue());

		groupMemberService.updateType(id,uid,MemberType.MASTER.getValue());

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(id);
		groupEvent.setAction(MessageAction.ACTION_307);
		groupEvent.setContent(String.valueOf(uid));
		groupEvent.addIgnored(UidHolder.getUid());

		applicationEventProducer.publishAsync(groupEvent);

	}
}
