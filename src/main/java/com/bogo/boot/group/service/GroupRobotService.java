
package com.bogo.boot.group.service;

import com.bogo.boot.group.api.request.RobotMessageRequest;
import com.bogo.boot.group.api.vo.GroupRobotVO;
import com.bogo.boot.group.entity.GroupRobot;
import com.bogo.boot.infra.model.ResponseEntity;
import java.util.List;

public interface GroupRobotService {

	void add(GroupRobot robot);

	GroupRobotVO findOne(long id);

	void disable(long id,long uid);

	void enable(long id,long uid);

	/**
	 * 转让机器人给群主
	 * @param uidList 哪些人的机器人
	 * @param groupId 群ID
	 */
	void transfer(List<Long> uidList,long groupId);

	void removeAll(long groupId);

	List<GroupRobot> findList(long groupId);

	List<GroupRobot> findList(List<Long> groupIdList);

	ResponseEntity<?> send(String uuid, RobotMessageRequest request);

	ResponseEntity<Void> delete(long id,long uid);

	void update(GroupRobot robot);
}
