
package com.bogo.boot.group.service.impl;

import com.bogo.boot.account.service.UserService;
import com.bogo.boot.group.api.request.GroupAlisaRequest;
import com.bogo.boot.group.bo.GroupMemberBO;
import com.bogo.boot.group.entity.GroupMember;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.group.event.RobotTransferEvent;
import com.bogo.boot.group.redis.GroupMemberRedisTemplate;
import com.bogo.boot.group.repository.GroupMemberRepository;
import com.bogo.boot.group.service.GroupMemberService;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.redis.KeyValueRedisTemplate;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.PageX;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.repository.MessageGroupRepository;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


@Service
public class GroupMemberServiceImpl implements GroupMemberService{

	private static final String NAME_REDIS_KEY = "NAME_OF_GROUP_MEMBER_%d_%d";

	@Resource
	private GroupMemberRepository groupMemberRepository;

	@Resource
	private MessageGroupRepository messageGroupRepository;

	@Resource
	private GroupMemberRedisTemplate memberRedisTemplate;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private UserService userService;

	@Resource
	private KeyValueRedisTemplate keyValueRedisTemplate;

	@Override
	public List<GroupMemberBO> findList(long id) {
		return this.findList(Collections.singletonList(id));
	}


	@Override
	public List<GroupMemberBO> findList(List<Long> idList) {

		List<GroupMember> members = groupMemberRepository.findList(idList);

		Map<Long,String> nameMap = userService.findName(members.stream().map(GroupMember::getUid).distinct().collect(Collectors.toList()));

		Function<GroupMember, GroupMemberBO> mapper = member -> new GroupMemberBO(member,nameMap.get(member.getUid()));

		return PageX.map(mapper,members);
	}

	@Override
	public GroupMemberBO findOne(Long id, Long uid) {
		String name = userService.findName(uid);
		GroupMember member = groupMemberRepository.findOne(id,uid);
		return new GroupMemberBO(member,name);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void removeAll(long groupId) {
		groupMemberRepository.removeAll(groupId);
		memberRedisTemplate.remove(groupId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void remove(GroupMember member) {
		long uid = member.getUid();
		long groupId = member.getGroupId();
		List<Long> memberUidList = findUidList(groupId);

		memberRedisTemplate.remove(member);

		groupMemberRepository.remove(groupId, uid);

		messageGroupRepository.deleteAll(groupId, uid,MessageAction.ACTION_3);

		/*
		 * 转让机器人给群主
		 */
		applicationEventProducer.publish(new RobotTransferEvent(groupId,Collections.singletonList(uid)));

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(groupId);
		groupEvent.setAction(MessageAction.ACTION_302);
		groupEvent.setContent(String.valueOf(uid));
		groupEvent.setUidList(memberUidList);

		applicationEventProducer.publish(groupEvent);

	}
 
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void remove(List<Long> idList, long groupId) {

		/*
		 *删除和他们相关的消息
		 */
		messageGroupRepository.deleteAll(groupId, idList.toArray(new Long[0]),MessageAction.ACTION_3);

		memberRedisTemplate.remove(groupId,idList);

		groupMemberRepository.remove(groupId, idList);

		/*
		 * 转让机器人给群主
		 */
		applicationEventProducer.publish(new RobotTransferEvent(groupId,idList));

		long uid = UidHolder.getUid();


		GroupActionEvent groupEvent301 = new GroupActionEvent();
		groupEvent301.setAction(MessageAction.ACTION_301);
		groupEvent301.setSender(groupId);
		groupEvent301.setUidList(idList);
		applicationEventProducer.publish(groupEvent301);

		GroupActionEvent groupEvent312 = new GroupActionEvent();
		groupEvent312.setAction(MessageAction.ACTION_312);
		groupEvent312.setSender(groupId);
		groupEvent312.setContent(StringUtils.join(idList,","));
		groupEvent312.setExtra(String.valueOf(uid));
		groupEvent312.addIgnored(uid);
		applicationEventProducer.publish(groupEvent312);

	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<Long> add(List<GroupMember> members) {

		/*
		 去除已经存在的成员
		 */
		members.removeIf(member -> groupMemberRepository.count(member.getGroupId(),member.getUid()) > 0);

		if (members.isEmpty()){
			return Collections.emptyList();
		}

		groupMemberRepository.saveAllAndFlush(members);

		List<Long> addedUidList = members.stream().map(GroupMember::getUid).collect(Collectors.toList());

		long groupId = members.stream().findFirst().map(GroupMember::getGroupId).orElse(0L);

		/* 缓存中有数据，添加成员才追加，否则可能缓存被删除，该操作不能追加，否则缓存和数据数据不一致 */
		if (memberRedisTemplate.size(groupId) > 0){
			memberRedisTemplate.add(groupId,addedUidList);
		}

		return addedUidList;
	}


	@Override
	public List<Long> findUidList(long id) {

		List<Long> idList = memberRedisTemplate.findUidList(id);

		if (CollectionUtils.isEmpty(idList)){
			idList.addAll(groupMemberRepository.findUidList(id));
			memberRedisTemplate.add(id,idList);
		}

		return idList;
	}

	@Override
	public void updateType(long groupId, Long uid, byte type) {
		groupMemberRepository.updateType(groupId,uid,type);
	}

	@Override
	public void setAlias(Long uid, GroupAlisaRequest request) {

		groupMemberRepository.updateAlias(request.getGroupId(), uid, request.getAlias());

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setAction(MessageAction.ACTION_318);
		groupEvent.setSender(request.getGroupId());
		groupEvent.setContent(uid.toString());
		groupEvent.setExtra(request.getAlias());
		groupEvent.addIgnored(uid);
		groupEvent.setReplaceable(false);

		applicationEventProducer.publish(groupEvent);

		keyValueRedisTemplate.delete(String.format(NAME_REDIS_KEY,request.getGroupId(),uid));

	}

	@Override
	public String findName(long groupId, long uid) {
		String key = String.format(NAME_REDIS_KEY,groupId,uid);
		String name = keyValueRedisTemplate.get(key);
		if (name != null){
			return name;
		}

		String newName;

		String alias  = groupMemberRepository.findAlias(groupId,uid);
		if (alias != null){
			newName = alias;
		}else {
			newName = userService.findName(uid);
		}

		keyValueRedisTemplate.set(key,newName, Duration.ofMinutes(10));

		return newName;
	}

	@Override
	public boolean isMember(long groupId, Long uid) {
		if (memberRedisTemplate.isMember(groupId,uid)){
			return true;
		}

		return groupMemberRepository.count(groupId,uid) > 0;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void delete(long id) {
		groupMemberRepository.findById(id).ifPresent(this::remove);
    }

}
