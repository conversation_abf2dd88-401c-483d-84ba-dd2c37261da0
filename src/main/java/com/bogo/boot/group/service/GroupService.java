
package com.bogo.boot.group.service;

import com.bogo.boot.group.api.request.GroupCreateRequest;
import com.bogo.boot.group.api.request.GroupInviteRequest;
import com.bogo.boot.group.bo.GroupBO;
import com.bogo.boot.group.constant.GroupState;
import com.bogo.boot.group.entity.Group;
import java.util.List;

public interface GroupService {

	Group create(GroupCreateRequest request);

	void updateName(long id, String name);

	void transfer(long id,long uid);

	void delete(long id);

	List<GroupBO> findList(Long uid);

	GroupBO findOne(long id,boolean loadMembers);

	void updateState(Long id, GroupState state);

	void invite(GroupInviteRequest request);

	String getName(long id);
}
