
package com.bogo.boot.group.service.impl;

import com.bogo.boot.group.api.request.RobotMessageRequest;
import com.bogo.boot.group.api.vo.GroupRobotVO;
import com.bogo.boot.group.entity.GroupRobot;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.group.event.RobotTransferEvent;
import com.bogo.boot.group.repository.GroupRepository;
import com.bogo.boot.group.repository.GroupRobotRepository;
import com.bogo.boot.group.service.GroupRobotService;
import com.bogo.boot.infra.constant.CommonState;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.model.ResponseEntity;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageAction;
import com.bogo.boot.message.entity.GroupMessage;
import com.bogo.boot.message.entity.Message;
import com.bogo.boot.message.model.MessageTitle;
import com.bogo.boot.message.pusher.MessagePusherProxy;
import com.bogo.boot.message.repository.MessageGroupRepository;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class GroupRobotServiceImpl implements GroupRobotService {

	@Resource
	private GroupRobotRepository groupRobotRepository;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Resource
	private MessagePusherProxy messagePusherProxy;

	@Resource
	private MessageGroupRepository messageGroupRepository;

	@Resource
	private GroupRepository groupRepository;

	@Override
	public void add(GroupRobot robot) {

		robot.setState(CommonState.ENABLE);
		robot.setCreateTime(new Date());
		String uuid = UUID.randomUUID().toString().replace("-","");
		robot.setUuid(uuid.toUpperCase());
		groupRobotRepository.saveAndFlush(robot);

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(robot.getGroupId());
		groupEvent.setAction(MessageAction.ACTION_313);
		groupEvent.setExtra(String.valueOf(robot.getUid()));
		groupEvent.setContent(String.format("{\"id\":%d,\"name\":\"%s\"}", robot.getId(), robot.getName()));
		groupEvent.setMakerUid(robot.getUid());
		groupEvent.addIgnored(robot.getUid());

		applicationEventProducer.publish(groupEvent);

	}


	@Override
	public void update(GroupRobot robot) {
		GroupRobot target = groupRobotRepository.findById(robot.getId()).orElse(null);
		if (target == null){
			return;
		}

		target.setName(robot.getName());
		target.setWebhook(robot.getWebhook());

		groupRobotRepository.saveAndFlush(target);

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(target.getGroupId());
		groupEvent.setAction(MessageAction.ACTION_314);
		groupEvent.setContent(String.valueOf(robot.getId()));
		groupEvent.setReplaceable(false);
		groupEvent.setExtra(String.valueOf(UidHolder.getUid()));
		applicationEventProducer.publish(groupEvent);
	}

	@Override
	public GroupRobotVO findOne(long id) {
		return GroupRobotVO.of(groupRobotRepository.findById(id).orElse(null));
	}


	@Override
	public void disable(long id,long uid) {

		groupRobotRepository.disable(id);

		Long groupId = groupRobotRepository.findGroupId(id);

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(groupId);
		groupEvent.setAction(MessageAction.ACTION_317);
		groupEvent.setContent(String.valueOf(id));
		groupEvent.setExtra(String.valueOf(CommonState.DISABLED));
		groupEvent.addIgnored(uid);
		applicationEventProducer.publish(groupEvent);
	}

	@Override
	public void enable(long id,long uid) {

		groupRobotRepository.enable(id);

		Long groupId = groupRobotRepository.findGroupId(id);

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(groupId);
		groupEvent.setAction(MessageAction.ACTION_317);
		groupEvent.setContent(String.valueOf(id));
		groupEvent.setExtra(String.valueOf(CommonState.ENABLE));
		groupEvent.addIgnored(uid);
		applicationEventProducer.publish(groupEvent);
	}

	@Override
	public void transfer(List<Long> uidList, long groupId) {

		List<Long> idList = groupRobotRepository.findIdList(groupId,uidList);

		if (idList.isEmpty()){
			return;
		}

		/*
		 * 这些人名下机器人转让给群主
		 */
		long uid = groupRepository.findUid(groupId);

		groupRobotRepository.transfer(idList,uid);

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(groupId);
		groupEvent.setAction(MessageAction.ACTION_319);
		groupEvent.setContent(StringUtils.join(idList,","));
		groupEvent.setExtra(String.valueOf(uid));
		applicationEventProducer.publish(groupEvent);
	}

	@EventListener(classes = RobotTransferEvent.class)
	public void onTransferEvent(RobotTransferEvent event){
		this.transfer(event.getUidList(),event.getGroupId());
	}

	@Override
	public void removeAll(long groupId) {
		groupRobotRepository.removeAll(groupId);
	}

	@Override
	public List<GroupRobot> findList(long groupId) {
		return groupRobotRepository.findList(groupId);
	}

	@Override
	public List<GroupRobot> findList(List<Long> groupIdList) {
		return groupRobotRepository.findList(groupIdList);
	}

	@Override
	public ResponseEntity<?> send(String uuid, RobotMessageRequest request) {

		GroupRobot robot = groupRobotRepository.findByUuid(uuid);

		boolean isPushable = robot != null && robot.getState() == CommonState.ENABLE;

		if (!isPushable){
			return ResponseEntity.make(HttpStatus.BAD_REQUEST,"机器人不存在或已经禁用");
		}

		Message message = new GroupMessage();
		message.setReceiver(robot.getGroupId());
		message.setAction(MessageAction.ACTION_4);
		message.setSender(robot.getId());
		message.setContent(request.getContent());
		message.setFormat(request.getFormat());

		MessageTitle title = new MessageTitle();
		title.setQt(request.getMessageId());
		title.setName(robot.getName());
		message.setTitle(JSON.toJSONString(title));

		return messagePusherProxy.push(message);
	}

	@Override
	public ResponseEntity<Void> delete(long id,long uid) {
		GroupRobot robot = groupRobotRepository.findById(id).orElse(null);
		if (robot == null || robot.getUid() != uid){
			return ResponseEntity.make(HttpStatus.BAD_REQUEST,"你没有权限删除该机器人");
		}

		groupRobotRepository.deleteById(id);

		messageGroupRepository.deleteAll(robot.getGroupId(),robot.getId(),MessageAction.ACTION_4);

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(robot.getGroupId());
		groupEvent.setAction(MessageAction.ACTION_316);
		groupEvent.setContent(String.valueOf(id));
		groupEvent.setExtra(String.valueOf(uid));
		groupEvent.setReplaceable(false);
		applicationEventProducer.publish(groupEvent);

		return ResponseEntity.ok();
	}

}
