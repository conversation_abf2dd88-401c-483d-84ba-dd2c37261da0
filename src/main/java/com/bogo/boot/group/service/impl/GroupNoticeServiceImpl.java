
package com.bogo.boot.group.service.impl;

import com.bogo.boot.group.api.vo.GroupNoticeVO;
import com.bogo.boot.group.entity.Group;
import com.bogo.boot.group.entity.GroupNotice;
import com.bogo.boot.group.event.GroupActionEvent;
import com.bogo.boot.group.redis.GroupRedisTemplate;
import com.bogo.boot.group.repository.GroupNoticeRepository;
import com.bogo.boot.group.repository.GroupRepository;
import com.bogo.boot.group.service.GroupNoticeService;
import com.bogo.boot.infra.constant.Common;
import com.bogo.boot.infra.holder.UidHolder;
import com.bogo.boot.infra.util.ApplicationEventProducer;
import com.bogo.boot.infra.util.PageX;
import com.bogo.boot.message.constant.MessageAction;
import jakarta.annotation.Resource;
import java.util.Date;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class GroupNoticeServiceImpl implements GroupNoticeService {

	@Resource
	private GroupNoticeRepository groupNoticeRepository;

	@Resource
	private GroupRedisTemplate groupRedisTemplate;

	@Resource
	private GroupRepository groupRepository;

	@Resource
	private ApplicationEventProducer applicationEventProducer;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(long groupId, String notice) {

		Group group = groupRepository.findById(groupId).orElse(null);
		if (group == null) {
			return;
		}

		groupRepository.updateNotice(groupId,notice);
		groupRedisTemplate.remove(groupId);

		long uid = UidHolder.getUid() == null ? group.getUid() : UidHolder.getUid();

		GroupNotice groupNotice = new GroupNotice();
		groupNotice.setGroupId(groupId);
		groupNotice.setText(notice);
		groupNotice.setUid(uid);
		groupNotice.setCreateTime(new Date());
		groupNoticeRepository.saveAndFlush(groupNotice);

		GroupActionEvent groupEvent = new GroupActionEvent();
		groupEvent.setSender(groupId);
		groupEvent.setAction(MessageAction.ACTION_305);
		groupEvent.setContent(notice);
		groupEvent.setExtra(String.valueOf(uid));

		applicationEventProducer.publish(groupEvent);

	}

	@Override
	public Page<GroupNoticeVO> queryPage(long groupId, int currentPage) {
		Pageable pageable = PageRequest.of(currentPage, Common.API_PAGE_SIZE, Sort.by(Sort.Order.desc("id")));
		Page<GroupNotice> page = groupNoticeRepository.findAll((Specification<GroupNotice>) (root, query, builder) -> {
			query.where(builder.equal(root.get("groupId").as(Long.class), groupId));
			return query.getRestriction();
		},pageable);
		return PageX.map(GroupNoticeVO::of,page);
	}
}
