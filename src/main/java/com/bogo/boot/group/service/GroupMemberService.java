
package com.bogo.boot.group.service;

import com.bogo.boot.group.api.request.GroupAlisaRequest;
import com.bogo.boot.group.bo.GroupMemberBO;
import com.bogo.boot.group.entity.GroupMember;
import java.util.List;

public interface GroupMemberService {

	List<GroupMemberBO> findList(long groupId);

	List<GroupMemberBO> findList(List<Long> groupIdList);

	GroupMemberBO findOne(Long id, Long uid);

	void removeAll(long groupId);

	void remove(GroupMember groupMember);

	void remove(List<Long> idList, long groupId);

	/**
	 * 添加群成员
	 * @return 添加成功的UID列表
	 */
	List<Long> add(List<GroupMember> members);


	List<Long> findUidList(long groupId);

	void updateType(long groupId, Long uid, byte type);

	void setAlias(Long uid, GroupAlisaRequest request);

	String findName(long groupId,long uid);

	boolean isMember(long groupId, Long uid);

	void delete(long id);
}
