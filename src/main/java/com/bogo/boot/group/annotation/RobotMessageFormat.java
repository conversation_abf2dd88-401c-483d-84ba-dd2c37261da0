
package com.bogo.boot.group.annotation;


import com.bogo.boot.group.api.validator.RobotMessageValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Constraint(validatedBy = RobotMessageValidator.class)
public @interface RobotMessageFormat {

    String message();

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
