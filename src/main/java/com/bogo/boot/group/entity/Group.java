
package com.bogo.boot.group.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 群组
 */
@Entity
@Table(name = "t_bogo_group")
@Getter
@Setter
public class Group {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "uid",nullable = false)
	private Long uid;

	@Column(name = "name", length = 16,nullable = false)
	private String name;

	@Column(name = "notice", length = 2048)
	private String notice;

	/**
	 * 状态
	 * @see com.bogo.boot.group.constant.GroupState
	 */
	@Column(name = "state", length = 1,nullable = false)
	private Byte state;

	@Column(name = "create_time", nullable = false)
	private Date createTime;

}
