
package com.bogo.boot.group.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 群公告记录
 */
@Entity
@Table(name = "t_bogo_group_notice")
@Getter
@Setter
public class GroupNotice {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "group_id",nullable = false)
	private Long groupId;
	
	@Column(name = "uid",nullable = false)
	private Long uid;

	@Column(name = "text",nullable = false)
	private String text;

	@Column(name = "create_time", nullable = false)
	private Date createTime;
}
