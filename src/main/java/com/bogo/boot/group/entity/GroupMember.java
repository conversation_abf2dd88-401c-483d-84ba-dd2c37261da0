
package com.bogo.boot.group.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 群组成员
 */
@Entity
@Table(name = "t_bogo_group_member")
@Getter
@Setter
public class GroupMember{

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "group_id",nullable = false)
	private Long groupId;
	
	@Column(name = "uid",nullable = false)
	private Long uid;

	/**
	 * 身份类型
	 * @see com.bogo.boot.group.constant.MemberType
	 */
	@Column(name = "type",nullable = false)
	private Byte type;

	@Column(name = "alias")
	private String alias;

	@Column(name = "create_time", nullable = false)
	private Date createTime;
}
