
package com.bogo.boot.group.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 群机器人配置
 */
@Entity
@Table(name = "t_bogo_group_robot")
@Getter
@Setter
public class GroupRobot {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "uid",nullable = false)
	private Long uid;

	@Column(name = "group_id",nullable = false)
	private Long groupId;

	@Column(name = "uuid",nullable = false)
	private String uuid;

	@Column(name = "name",nullable = false)
	private String name;

	@Column(name = "webhook")
	private String webhook;

	/**
	 * 状态
	 * @see com.bogo.boot.infra.constant.CommonState
	 */
	@Column(name = "state", length = 2,nullable = false)
	private Byte state;

	@Column(name = "create_time", nullable = false)
	private Date createTime;
}
