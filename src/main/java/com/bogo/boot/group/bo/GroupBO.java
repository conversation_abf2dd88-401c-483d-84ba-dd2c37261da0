package com.bogo.boot.group.bo;

import com.bogo.boot.group.api.vo.GroupRobotVO;
import com.bogo.boot.group.api.vo.GroupVO;
import com.bogo.boot.group.entity.Group;
import com.bogo.boot.group.entity.GroupRobot;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GroupBO {

    private final Group group;
    private List<GroupMemberBO> members;
    private List<GroupRobot> robots;

    public GroupBO(Group group) {
        this.group = group;
    }

    public GroupVO toVO() {
        GroupVO vo =  GroupVO.of(group);
        vo.setMemberList(members.stream().map(GroupMemberBO::toVo).toList());
        vo.setRobotList(robots.stream().map(GroupRobotVO::of).toList());
        return vo;
    }

    public Long getUid() {
        return group.getUid();
    }

    public byte getState() {
        return group.getState();
    }
}
