package com.bogo.boot.group.bo;

import com.bogo.boot.group.api.vo.GroupMemberVO;
import com.bogo.boot.group.entity.GroupMember;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GroupMemberBO {
    private final GroupMember member;
    private final String name;

    public GroupMemberBO(GroupMember member, String name) {
        this.member = member;
        this.name = name;
    }

    public GroupMemberVO toVo() {
        return GroupMemberVO.of(member, name);
    }

    public Long getGroupId() {
        return member.getGroupId();
    }
}
