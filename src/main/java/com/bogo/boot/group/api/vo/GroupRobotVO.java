package com.bogo.boot.group.api.vo;

import com.bogo.boot.group.entity.GroupRobot;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "群机器人")
@Getter
@Setter
public class GroupRobotVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "创建者UID")
    private Long uid;

    @Schema(title = "唯一编号")
    private String uuid;

    @Schema(title = "名称")
    private String name;

    @Schema(title = "@响应接口地址")
    private String webhook;

    @Schema(title = "状态 0:禁用 1:正常")
    private Byte state;

    @Schema(title = "创建时间")
    private Date createTime;

    public static GroupRobotVO of(GroupRobot robot){

        if (robot == null){
            return null;
        }

        GroupRobotVO vo = new GroupRobotVO();
        vo.setState(robot.getState());
        vo.setUid(robot.getUid());
        vo.setName(robot.getName());
        vo.setId(robot.getId());
        vo.setWebhook(robot.getWebhook());
        vo.setUuid(robot.getUuid());
        vo.setCreateTime(robot.getCreateTime());
        return vo;
    }
}
