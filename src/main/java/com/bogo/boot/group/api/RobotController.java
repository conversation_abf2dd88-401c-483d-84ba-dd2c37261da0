
package com.bogo.boot.group.api;

import com.bogo.boot.group.api.request.AddRobotRequest;
import com.bogo.boot.group.api.request.UpdateRobotRequest;
import com.bogo.boot.group.api.vo.GroupRobotVO;
import com.bogo.boot.group.entity.GroupRobot;
import com.bogo.boot.group.service.GroupRobotService;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.annotation.UpdateAction;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/group/robot")
@Tag(name = "群机器人相关接口" )
public class RobotController {

	@Resource
	private GroupRobotService groupRobotService;

	@Operation( summary = "创建机器人")
	@PostMapping(value = "")
	public ResponseEntity<GroupRobot> add(@Validated(CreateAction.class) @RequestBody AddRobotRequest request, @Parameter(hidden = true) @UID Long uid) {
		GroupRobot robot = new GroupRobot();
		robot.setUid(uid);
		robot.setGroupId(request.getGroupId());
		robot.setName(request.getName());
		robot.setWebhook(request.getWebhook());
		groupRobotService.add(robot);
		return ResponseEntity.ok(robot);
	}

	@Operation( summary = "修改机器人")
	@PostMapping(value = "/update")
	public ResponseEntity<Void> add(@Validated(UpdateAction.class) @RequestBody UpdateRobotRequest request) {
		GroupRobot robot = new GroupRobot();
		robot.setId(request.getId());
		robot.setName(request.getName());
		robot.setWebhook(request.getWebhook());
		groupRobotService.update(robot);
		return ResponseEntity.ok();
	}

	@Operation( summary = "获取机器人详情")
	@Parameter(name = "id", description = "机器人ID", in = ParameterIn.PATH,example = "0")
	@GetMapping(value = "/{id}")
	public ResponseEntity<GroupRobotVO> get(@PathVariable long id) {
		return ResponseEntity.ok(groupRobotService.findOne(id));
	}

	@Operation( summary = "删除机器人")
	@Parameter(name = "id", description = "机器人ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> delete(@PathVariable long id, @Parameter(hidden = true) @UID Long uid) {
		return groupRobotService.delete(id,uid);
	}


	@Operation( summary = "关闭机器人")
	@Parameter(name = "id", description = "机器人ID", in = ParameterIn.PATH,example = "0")
	@PostMapping(value = "/disable/{id}")
	public ResponseEntity<Void> disable(@PathVariable long id, @Parameter(hidden = true) @UID Long uid) {

		groupRobotService.disable(id,uid);

		return ResponseEntity.ok();
	}

	@Operation( summary = "启用机器人")
	@Parameter(name = "id", description = "机器人ID", in = ParameterIn.PATH,example = "0")
	@PostMapping(value = "/enable/{id}")
	public ResponseEntity<Void> enable(@PathVariable long id, @Parameter(hidden = true) @UID Long uid) {

		groupRobotService.enable(id,uid);

		return ResponseEntity.ok();
	}

}
