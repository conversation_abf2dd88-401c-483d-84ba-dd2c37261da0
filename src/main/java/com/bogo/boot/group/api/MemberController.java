
package com.bogo.boot.group.api;

import com.bogo.boot.group.api.request.DeleteMemberRequest;
import com.bogo.boot.group.api.request.GroupAlisaRequest;
import com.bogo.boot.group.api.vo.GroupMemberVO;
import com.bogo.boot.group.bo.GroupMemberBO;
import com.bogo.boot.group.entity.GroupMember;
import com.bogo.boot.group.service.GroupMemberService;
import com.bogo.boot.infra.annotation.DeleteAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.annotation.UpdateAction;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/group/member")
@Tag(name = "群成员相关接口" )
public class MemberController {

	@Resource
	private GroupMemberService groupMemberService;

	@Operation( summary = "获取群组成员列表")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@GetMapping(value = "/list/{id}")
	public ResponseEntity<List<GroupMemberVO>> list(@PathVariable long id) {
		return ResponseEntity.ok(groupMemberService.findList(id).stream().map(GroupMemberBO::toVo).toList());
	}

	@Operation( summary = "退出群组")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/quit/{id}")
	public ResponseEntity<Void> quit(@PathVariable long id, @Parameter(hidden = true) @UID Long uid) {

		GroupMember member = new GroupMember();
		member.setGroupId(id);
		member.setUid(uid);
		groupMemberService.remove(member);

		return ResponseEntity.ok();
	}

	@Operation( summary = "移除群成员")
	@PostMapping(value = "/delete")
	public ResponseEntity<Void> delete(@Validated(DeleteAction.class) @RequestBody DeleteMemberRequest request) {

		groupMemberService.remove(request.getIdList(), request.getGroupId());

		return ResponseEntity.ok();
	}

	@Operation( summary = "设置群昵称")
	@PostMapping(value = "/alias")
	public ResponseEntity<Void> setAlias(@Parameter(hidden = true) @UID Long uid,@Validated(UpdateAction.class) @RequestBody GroupAlisaRequest request) {
		groupMemberService.setAlias(uid,request);
		return ResponseEntity.ok();
	}
}
