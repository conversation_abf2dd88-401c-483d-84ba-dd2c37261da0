
package com.bogo.boot.group.api;

import com.bogo.boot.group.api.request.GroupCreateRequest;
import com.bogo.boot.group.api.request.GroupInviteRequest;
import com.bogo.boot.group.api.vo.GroupVO;
import com.bogo.boot.group.bo.GroupBO;
import com.bogo.boot.group.constant.GroupState;
import com.bogo.boot.group.entity.Group;
import com.bogo.boot.group.service.GroupService;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UID;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.hibernate.validator.constraints.Length;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/group")
@Tag(name = "群组相关接口" )
public class GroupController {

	@Resource
	private GroupService groupService;

	@Operation( summary = "获取我的群列表")
	@GetMapping(value = "/list")
	public ResponseEntity<List<GroupVO>> list(@Parameter(hidden = true) @UID Long uid) {
		return ResponseEntity.ok(groupService.findList(uid).stream().map(GroupBO::toVO).toList());
	}

	@Operation( summary = "获取群组详情")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@GetMapping(value = "/{id}")
	public ResponseEntity<GroupVO> get(@PathVariable long id) {
		GroupBO group = groupService.findOne(id,true);
		if (group == null) {
			return ResponseEntity.make(HttpStatus.BAD_REQUEST,"群ID不存在");
		}
		return ResponseEntity.ok(group.toVO());
	}

	@Operation( summary = "创建群组")
	@PostMapping(value = "")
	public ResponseEntity<Long> create(@Validated(CreateAction.class) @RequestBody GroupCreateRequest request) {
		Group group = groupService.create(request);
		return ResponseEntity.ok(group.getId());
	}

	@Operation( summary = "修改群组名称")
	@Parameters({
			@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "name", description = "名称", in = ParameterIn.QUERY),
	})
	@PatchMapping(value = "/{id}/name")
	public ResponseEntity<Void> updateName(@PathVariable Long id,
										   @Length(max = 32,message = "群名称不能超过32个字符") @RequestParam String  name) {
		groupService.updateName(id, name);
		return ResponseEntity.ok();
	}


	@Operation( summary = "解散群组")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<Void> disband(@PathVariable long id) {
		groupService.delete(id);
		return ResponseEntity.ok();
	}

	@Operation( summary = "群组禁言")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@PostMapping(value = "/block/{id}")
	public ResponseEntity<Void> block(@PathVariable Long id) {
		groupService.updateState(id, GroupState.BLOCK);
		return ResponseEntity.ok();
	}
	@Operation( summary = "解除群组禁言")
	@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0")
	@DeleteMapping(value = "/block/{id}")
	public ResponseEntity<Void> unblock(@PathVariable Long id) {
		groupService.updateState(id, GroupState.NORMAL);
		return ResponseEntity.ok();
	}

	@Operation( summary = "转让群组")
	@Parameters({
			@Parameter(name = "id", description = "群组ID", in = ParameterIn.PATH,example = "0"),
			@Parameter(name = "uid", description = "新群主ID", in = ParameterIn.QUERY,example = "0")
	})
	@PostMapping(value = "/transfer/{id}")
	public ResponseEntity<Void> transfer(@PathVariable Long id , @RequestParam long uid) {
		groupService.transfer(id,uid);
		return ResponseEntity.ok();
	}

	@Operation( summary = "邀请用户入群")
	@PostMapping(value = "/invite")
	public ResponseEntity<Void> invite(@Validated(CreateAction.class) @RequestBody GroupInviteRequest request) {
		groupService.invite(request);
		return ResponseEntity.ok();
	}

}
