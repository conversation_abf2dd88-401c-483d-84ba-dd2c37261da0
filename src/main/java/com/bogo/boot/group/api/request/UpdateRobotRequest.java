
package com.bogo.boot.group.api.request;

import com.bogo.boot.infra.annotation.NameFormat;
import com.bogo.boot.infra.annotation.UpdateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
public class UpdateRobotRequest implements Serializable {

	@Schema(title = "id")
	@NotNull(message = "id不能为空",groups = UpdateAction.class)
	private Long id;

	@Schema(title = "名称")
	@NotBlank(message = "名称不能为空",groups = UpdateAction.class)
	@Length(max = 32,message = "名称不能超过32个字符",groups = UpdateAction.class)
	@NameFormat(message = "名称不能存在特殊字符", groups = UpdateAction.class)
	private String name;

	@Schema(title = "webhook")
	private String webhook;

}
