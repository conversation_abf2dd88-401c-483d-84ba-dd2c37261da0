package com.bogo.boot.group.api.vo;

import com.bogo.boot.group.entity.GroupNotice;
import com.bogo.boot.infra.model.Sortable;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "群公告")
@Getter
@Setter
public class GroupNoticeVO implements Sortable {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "创建者UID")
    private Long uid;

    @Schema(title = "内容")
    private String text;

    @Schema(title = "发布时间")
    private Date createTime;

    public static GroupNoticeVO of(GroupNotice notice){
        GroupNoticeVO vo = new GroupNoticeVO();
        vo.uid = notice.getUid();
        vo.text = notice.getText();
        vo.id = notice.getId();
        vo.createTime = notice.getCreateTime();
        return vo;
    }
}
