
package com.bogo.boot.group.api.request;


import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;

@Schema
@Getter
@Setter
public class GroupInviteRequest implements Serializable {

	@Schema(title = "用户ID列表")
	@Size(min = 1, message = "用户ID列表不能为空", groups = {CreateAction.class})
	private List<Long> idList;

	@Schema(title = "群ID")
	@NotNull(message = "groupId不能为空",groups = CreateAction.class)
	private Long groupId;

	public List<Long> getIdList() {
		return idList.stream().distinct().collect(Collectors.toList());
	}
}
