
package com.bogo.boot.group.api.request;


import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.DeleteAction;
import com.bogo.boot.infra.annotation.NameFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
public class GroupCreateRequest implements Serializable {

	/*
	群名称不能超过32个字符
	 */
	private static final int NAME_MAX_LENGTH = 32;

	@Schema(title = "用户ID列表,不包含自己")
	@Size(min = 1, message = "用户ID列表不能为空", groups = DeleteAction.class)
	private List<Long> idList;

	@Schema(title = "群名称")
	@NotBlank(message = "群名称不能为空",groups = CreateAction.class)
	@NameFormat(message = "群名称不能存在特殊字符", groups = CreateAction.class)
	private String name;

	@Schema(title = "公告")
	@Length(max = 2048,message = "公告长度不可超过2048个字符",groups = CreateAction.class)
	private String notice;

	/**
	 * 名称超长截断
	 * @return
	 */
	public String getName() {
		if (name.length() <= NAME_MAX_LENGTH){
			return name;
		}
		return name.substring(0,NAME_MAX_LENGTH - 3) + "...";
	}

	public List<Long> getIdList() {
		return idList.stream().distinct().collect(Collectors.toList());
	}
}
