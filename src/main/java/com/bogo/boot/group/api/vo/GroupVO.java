package com.bogo.boot.group.api.vo;


import com.bogo.boot.group.entity.Group;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Schema(title = "群信息")
@Getter
@Setter
public class GroupVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "创建者UID")
    private Long uid;

    @Schema(title = "名称")
    private String name;

    @Schema(title = "公告")
    private String notice;

    @Schema(title = "状态0:正常 1:禁言")
    private Byte state;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "成员列表")
    private List<GroupMemberVO> memberList;

    @Schema(title = "机器人列表")
    private List<GroupRobotVO> robotList;

    public static GroupVO of(Group group){
        if (group == null){
            return null;
        }
        GroupVO vo = new GroupVO();
        vo.uid = group.getUid();
        vo.state = group.getState();
        vo.id = group.getId();
        vo.name = group.getName();
        vo.notice = group.getNotice();
        vo.createTime = group.getCreateTime();
        return vo;
    }
}
