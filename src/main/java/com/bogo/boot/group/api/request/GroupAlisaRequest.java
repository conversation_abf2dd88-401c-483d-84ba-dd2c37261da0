
package com.bogo.boot.group.api.request;

import com.bogo.boot.infra.annotation.NameFormat;
import com.bogo.boot.infra.annotation.UpdateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
public class GroupAlisaRequest implements Serializable {

	@Schema(title = "群昵称")
	@Length(max = 16,message = "群昵称不能超过16个字符",groups = UpdateAction.class)
    @NameFormat(message = "群昵称不能存在特殊字符", groups = UpdateAction.class)
	private String alias;

	@Schema(title = "群id")
	@NotNull(message = "群id不能为空",groups = UpdateAction.class)
	private Long groupId;

}
