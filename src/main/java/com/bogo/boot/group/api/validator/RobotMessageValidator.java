package com.bogo.boot.group.api.validator;

import com.bogo.boot.group.annotation.RobotMessageFormat;
import com.bogo.boot.group.api.request.RobotMessageRequest;
import com.bogo.boot.infra.util.JSON;
import com.bogo.boot.message.constant.MessageFormat;
import com.bogo.boot.message.model.ChatLink;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class RobotMessageValidator implements ConstraintValidator<RobotMessageFormat, RobotMessageRequest> {

    @Override
    public boolean isValid(RobotMessageRequest request, ConstraintValidatorContext context) {

        if (request.getFormat() == MessageFormat.TEXT){
            return true;
        }

        if (request.getFormat() == MessageFormat.LINK){
            ChatLink link = JSON.parseNullable(request.getContent(), ChatLink.class);
            return link != null && StringUtils.isNotBlank(link.title) && StringUtils.isNotBlank(link.url);
        }

        return false;
    }

}
