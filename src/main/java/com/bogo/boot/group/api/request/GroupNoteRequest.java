
package com.bogo.boot.group.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
public class GroupNoteRequest implements Serializable {

	@Schema(title = "公告内容")
	@NotBlank(message = "公告内容不能为空",groups = CreateAction.class)
	@Length(max = 2000,message = "公告内容不能超过2000个字符")
	private String text;

	@Schema(title = "群ID")
	@NotNull(message = "群ID不能为空",groups = CreateAction.class)
	private Long groupId;
}
