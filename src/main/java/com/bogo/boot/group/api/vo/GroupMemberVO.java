package com.bogo.boot.group.api.vo;

import com.bogo.boot.group.entity.GroupMember;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;


@Schema(title = "群成员")
@Getter
@Setter
public class GroupMemberVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "UID")
    private Long uid;

    @Schema(title = "身份 0:成员 1:群主")
    private Byte type;

    @Schema(title = "用户名称")
    private String name;

    @Schema(title = "群昵称")
    private String alias;

    @Schema(title = "入群时间")
    private Date createTime;

    public static GroupMemberVO of(GroupMember member, String name){
        GroupMemberVO vo = new GroupMemberVO();
        vo.setType(member.getType());
        vo.setUid(member.getUid());
        vo.setName(name);
        vo.setAlias(member.getAlias());
        vo.setId(member.getId());
        vo.setCreateTime(member.getCreateTime());
        return vo;
    }
}
