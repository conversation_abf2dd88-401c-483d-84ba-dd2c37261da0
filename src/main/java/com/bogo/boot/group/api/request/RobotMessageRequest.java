
package com.bogo.boot.group.api.request;

import com.bogo.boot.group.annotation.RobotMessageFormat;
import com.bogo.boot.infra.annotation.CreateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
@RobotMessageFormat(message = "消息格式不正确",groups = CreateAction.class)
public class RobotMessageRequest{

	@Schema(title = "消息内容")
	@NotBlank(message = "消息内容不能为空",groups = CreateAction.class)
	@Length(max = 4096,message = "消息内容不能超过4096个字符",groups = CreateAction.class)
	private String content;

	@Schema(title = "消息格式，0:文本 6:链接")
	@NotNull(message = "消息格式不能为空",groups = CreateAction.class)
	private Byte format;

	@Schema(title = "回复的消息ID")
	private Long messageId;

}
