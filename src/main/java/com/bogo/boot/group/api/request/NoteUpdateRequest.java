
package com.bogo.boot.group.api.request;

import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.annotation.UpdateAction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Schema
@Getter
@Setter
public class NoteUpdateRequest implements Serializable {

	@Schema(title = "ID")
	@NotNull(message = "ID不能为空",groups = UpdateAction.class)
	private Long id;

	@Schema(title = "标题")
	@NotBlank(message = "标题不能为空",groups = CreateAction.class)
	@Length(max = 100,message = "标题不能超过100个字符",groups = CreateAction.class)
	private String title;

	@Schema(title = "附件、多媒体、位置等内容")
	private String text;

	@Schema(title = "内容")
	private String content;

	@Schema(title = "内容格式")
	@NotNull(message = "内容格式不能为空",groups = UpdateAction.class)
	private Byte format;

	@Schema(title = "扩展内容")
	private String extra;

}
