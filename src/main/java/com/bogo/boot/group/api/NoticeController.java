
package com.bogo.boot.group.api;

import com.bogo.boot.group.api.request.GroupNoteRequest;
import com.bogo.boot.group.api.vo.GroupNoticeVO;
import com.bogo.boot.group.service.GroupNoticeService;
import com.bogo.boot.infra.annotation.CreateAction;
import com.bogo.boot.infra.model.PaginationEntity;
import com.bogo.boot.infra.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/group/notice")
@Tag(name = "群公告相关接口" )
public class NoticeController {

	@Resource
	private GroupNoticeService groupNoticeService;


	@Operation( summary = "发布公告")
	@PostMapping(value = "/add")
	public ResponseEntity<Void> add(@Validated(CreateAction.class) @RequestBody GroupNoteRequest request) {
		groupNoticeService.update(request.getGroupId(), request.getText());
		return ResponseEntity.ok();
	}

	@Operation( summary = "查询群公告历史")
	@Parameters({
			@Parameter(name = "groupId", description = "群组ID", in = ParameterIn.QUERY,example = "0"),
			@Parameter(name = "currentPage", description = "当前页数，起始0", in = ParameterIn.QUERY),
	})
	@GetMapping(value = "/list")
	public PaginationEntity<GroupNoticeVO> page(@RequestParam Long groupId, @RequestParam int currentPage) {
		Page<GroupNoticeVO> page =  groupNoticeService.queryPage(groupId, currentPage);
		return PaginationEntity.make(page);
	}
}
