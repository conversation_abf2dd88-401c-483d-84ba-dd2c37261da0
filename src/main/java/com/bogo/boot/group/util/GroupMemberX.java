
package com.bogo.boot.group.util;

import com.bogo.boot.group.entity.GroupMember;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class GroupMemberX {

	public static List<GroupMember> create(long groupId,List<Long> idList, byte type){

		List<GroupMember> members = new ArrayList<>();

		for (Long memberUid : idList){
			members.add(create(groupId,memberUid,type));
		}

		return members;
	}

	public static GroupMember create(long groupId,long uid, byte type){

		GroupMember member = new GroupMember();
		member.setUid(uid);
		member.setGroupId(groupId);
		member.setType(type);
		member.setCreateTime(new Date());

		return member;
	}
}
