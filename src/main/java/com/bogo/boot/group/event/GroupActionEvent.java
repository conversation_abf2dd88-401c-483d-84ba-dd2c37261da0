package com.bogo.boot.group.event;

import com.bogo.boot.message.entity.EventMessage;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
public class GroupActionEvent extends ApplicationEvent {

    private final EventMessage message = new EventMessage();

    // 消息产生来源UID
    private Long makerUid;

    // 事件消息是可替换的，也就是只保留最新的一条，如群修改名称、公告等
    private boolean replaceable = true;

    private final List<Long> uidList = new LinkedList<>();

    private final List<Long> ignoreUidList = new LinkedList<>();

    public GroupActionEvent() {
        super("");
    }

    public void setUidList(List<Long> uidList) {
        this.uidList.addAll(uidList);
    }

    public void addUid(Long uid) {
        if (uid == null) {
            return;
        }
        this.uidList.add(uid);
    }

    public void addIgnored(Long uid) {
        if (uid == null) {
            return;
        }
        this.ignoreUidList.add(uid);
    }

    public void addIgnored(Collection<Long> uidList) {
        this.ignoreUidList.addAll(uidList);
    }

    public List<Long> getUidList() {

        uidList.removeAll(ignoreUidList);

        return uidList;
    }

    public void setAction(String action) {
        message.setAction(action);
    }

    public void setContent(String content) {
        message.setContent(content);
    }

    public void setExtra(String extra) {
        message.setExtra(extra);
    }

    public void setSender(Long sender) {
        message.setSender(sender);
    }

}
