spring.profiles.active=dev
server.port=8080
spring.application.name=bogo-boot-server
server.compression.enabled=true
server.compression.mime-types=application/json
spring.jackson.default-property-inclusion=non_empty
spring.jackson.serialization.write-dates-as-timestamps=true
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1
server.tomcat.threads.max=200
springdoc.swagger-ui.path=/swagger-ui.html
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.shutdown.access=unrestricted
management.endpoints.web.base-path=/management
##################################################################
#                         JDBC Config                            #
##################################################################
spring.datasource.url=*******************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=BOGO_HIKARI_POOL
spring.datasource.hikari.max-lifetime=120000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=600000
##################################################################
#                         JPA Config                             #
##################################################################
spring.jpa.database=MYSQL
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
#\u5F53\u9047\u5230\u542F\u52A8\u670D\u52A1\u5361\u4F4F\u65F6\uFF0C\u53EF\u5C1D\u8BD5\u6CE8\u91CA\u4E0B\u9762\u914D\u7F6E\u518D\u8BD5
spring.jpa.hibernate.ddl-auto=none
spring.jpa.open-in-view=false
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
##################################################################
#                         Redis Config                           #
##################################################################
spring.data.redis.repositories.enabled=false
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
spring.data.redis.lettuce.pool.max-active=10
spring.data.redis.lettuce.pool.max-wait=10s
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=3
spring.data.redis.database=10
spring.data.redis.lettuce.cluster.refresh.period=60s
spring.data.redis.lettuce.cluster.refresh.adaptive=true
spring.data.redis.timeout=10s
##################################################################
#                         APP Config                             #
##################################################################
messaging.app.port=8090
messaging.app.enable=true
messaging.app.write-idle=45s
messaging.app.read-idle=60s
messaging.app.max-pong-timeout=3
messaging.websocket.port=8070
messaging.websocket.enable=true
messaging.websocket.path=/
messaging.websocket.protocol=PROTOBUF
messaging.websocket.write-idle=45s
messaging.websocket.read-idle=60s
messaging.websocket.max-pong-timeout=3
bogo.apns.enable=false
bogo.apns.debug=false
bogo.apns.p12-file=/apns/your-apns.p12
bogo.apns.p12-password=your pwd
bogo.apns.app-id=your app id
bogo.uni.push.enable=false
bogo.uni.push.app-id=your app id
bogo.uni.push.app-key=your app key
bogo.uni.push.master-secret=your app secret
bogo.uni.push.domain=https://restapi.getui.com/v2/
##################################################################
#                         File Store Config                      #
##################################################################
#\u6700\u5927\u5141\u8BB8\u4E0A\u4F20\u6587\u4EF6\u5927\u5C0F
bogo.upload.file.max-size=50MB
#boot\u670D\u52A1\u7684\u8BBF\u95EE\u5730\u5740
#\u7528\u5904\u5982\u4E0B\uFF1A\u672C\u5730\u5B58\u50A8\u6A21\u5F0F:\u5BA2\u6237\u7AEF\u76F4\u4F20\u5230\u8FD9\u4E2A\u670D\u52A1  OSS\u6A21\u5F0F:OSS\u4FA7\u4E0A\u4F20\u6210\u529F\u901A\u77E5\u56DE\u8C03\u5230\u8FD9\u4E2A\u670D\u52A1\u5730\u5740
bogo.upload.file.endpoint=http://192.168.1.100:8080
bogo.file.local.enable=true
bogo.file.local.buckets[0]=group-icon
bogo.file.local.buckets[1]=user-icon
bogo.file.local.buckets[2]=user-banner
bogo.file.local.buckets[3]=chat-space
bogo.file.local.buckets[4]=moment-space
bogo.file.local.buckets[5]=chat-wallpaper
bogo.file.local.buckets[6]=moment-wallpaper
bogo.file.local.buckets[7]=micro-app-icon
bogo.file.local.buckets[8]=micro-server-icon
bogo.file.local.buckets[9]=emoticon-logo
bogo.file.local.buckets[10]=emoticon-item
bogo.file.local.buckets[11]=robot-logo
bogo.file.local.buckets[12]=note
bogo.file.local.buckets[13]=meeting
bogo.file.oss.enable=false
bogo.file.oss.endpoint=your endpoint
bogo.file.oss.access-id=your access-id
bogo.file.oss.access-key=your access-key
bogo.file.oss.bucket=your bucket
##################################################################
#                         Feign Config                           #
##################################################################
spring.cloud.openfeign.client.config.default.read-timeout=1000
spring.cloud.openfeign.client.config.default.connect-timeout=1000
spring.cloud.openfeign.circuitbreaker.enabled=true
spring.cloud.openfeign.compression.request.enabled=true
spring.cloud.openfeign.compression.request.mime-types=text/xml,application/xml,application/json
spring.cloud.openfeign.compression.request.min-request-size=2048
spring.cloud.openfeign.compression.response.enabled=true
logging.level.com.bogo.boot.app.feign.IAccountHookService=debug
logging.level.com.bogo.boot.infra.feign.ITranslateService=debug
logging.level.com.bogo.boot.message.feign.IBlackwordHookService=debug
##################################################################
#                      Message config                            #
##################################################################
# true:send failed false:send success
bogo.message.blackword.enable=false
bogo.message.blackword.intercept=false
#敏感词触发通知webhook
#bogo.message.blackword.webhook=https://yourhook.com/hook
# 启用多端同步补偿
bogo.message.patch.enable=true
# 多端同步补偿保存时长，过期清理
bogo.message.patch.lifecycle=7d
# 是否运行发送的消息包含html标签
bogo.message.html.allowed=false
# 消息存储策略，同步存储(true) 异步存储(false)
bogo.message.storage.instant=true
##################################################################
#                         Livekit Config                         #
##################################################################
bogo.livekit.app-id=your app id
bogo.livekit.secret=your secret
bogo.livekit.uri=http://127.0.0.1:7080
##################################################################
#                           翻译 Config                           #
##################################################################
bogo.translate.enable=false
bogo.translate.app-id=your app id
bogo.translate.secret-key=your secret
##################################################################
#                         GeWe API Config                        #
##################################################################
gewe.api.base-url=http://api.geweapi.com
gewe.api.app-id=your app id
gewe.api.token=your token
gewe.api.timeout=30000
