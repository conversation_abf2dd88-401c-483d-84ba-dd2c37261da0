-- GeWe好友信息表
CREATE TABLE IF NOT EXISTS `gewe_friend` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wx_id` VARCHAR(100) NOT NULL COMMENT '微信ID',
  `nick_name` VARCHAR(200) DEFAULT NULL COMMENT '昵称',
  `py_initial` VARCHAR(200) DEFAULT NULL COMMENT '拼音首字母',
  `quan_pin` VARCHAR(200) DEFAULT NULL COMMENT '全拼',
  `sex` INT(11) DEFAULT NULL COMMENT '性别 1-男 2-女',
  `remark` VARCHAR(200) DEFAULT NULL COMMENT '备注',
  `remark_py_initial` VARCHAR(200) DEFAULT NULL COMMENT '备注拼音首字母',
  `remark_quan_pin` VARCHAR(200) DEFAULT NULL COMMENT '备注全拼',
  `signature` VARCHAR(1000) DEFAULT NULL COMMENT '个性签名',
  `alias` VARCHAR(100) DEFAULT NULL COMMENT '微信号',
  `sns_bg_img` VARCHAR(500) DEFAULT NULL COMMENT '朋友圈背景图',
  `country` VARCHAR(100) DEFAULT NULL COMMENT '国家',
  `province` VARCHAR(100) DEFAULT NULL COMMENT '省份',
  `city` VARCHAR(100) DEFAULT NULL COMMENT '城市',
  `big_head_img_url` VARCHAR(500) DEFAULT NULL COMMENT '大头像URL',
  `small_head_img_url` VARCHAR(500) DEFAULT NULL COMMENT '小头像URL',
  `is_group` TINYINT(1) DEFAULT 0 COMMENT '是否为群聊 0-否 1-是',
  `synced` TINYINT(1) DEFAULT 1 COMMENT '是否已同步 0-否 1-是',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_wx_id` (`wx_id`),
  KEY `idx_nick_name` (`nick_name`),
  KEY `idx_remark` (`remark`),
  KEY `idx_is_group` (`is_group`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='GeWe好友信息表';