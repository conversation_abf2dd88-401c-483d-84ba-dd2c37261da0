# GeWe好友管理系统

本系统集成了GeWe开放平台的API，实现了微信好友信息的同步和消息发送功能。

## 功能特性

- 🔄 **好友同步**: 从GeWe API同步微信好友和群聊信息到本地数据库
- 📋 **好友管理**: 查看好友列表、搜索好友、查看好友详情
- 💬 **消息发送**: 向好友或群聊发送文字消息，支持@功能
- 🌐 **Web界面**: 提供友好的Web管理界面

## 快速开始

### 1. 配置GeWe API

在 `application.properties` 中配置您的GeWe API信息：

```properties
# GeWe API配置
gewe.api.base-url=http://api.geweapi.com
gewe.api.app-id=your_app_id
gewe.api.token=your_token
gewe.api.timeout=30000
```

### 2. 创建数据库表

执行以下SQL脚本创建必要的数据库表：

```sql
-- 运行 docs/sql/gewe_friend.sql 中的建表语句
```

### 3. 启动应用

```bash
# 方式一：使用Maven
mvn spring-boot:run

# 方式二：打包后运行
mvn clean package
java -jar target/bogo-boot-server.jar
```

### 4. 访问Web界面

启动成功后，在浏览器中访问：
- 好友管理界面: http://localhost:8080/gewe-friends.html
- API文档: http://localhost:8080/swagger-ui/index.html

## API接口

### 好友管理接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/gewe/friend/sync` | POST | 同步好友信息 |
| `/gewe/friend/list` | GET | 获取好友列表 |
| `/gewe/friend/groups` | GET | 获取群聊列表 |
| `/gewe/friend/search?keyword=` | GET | 搜索好友 |
| `/gewe/friend/{wxId}` | GET | 获取好友详情 |
| `/gewe/friend/{wxId}/refresh` | POST | 刷新好友信息 |
| `/gewe/friend/message/send` | POST | 发送消息 |

### 消息发送示例

```json
// 发送好友消息
{
    "toWxId": "wxid_xxx",
    "content": "Hello!",
    "messageType": "friend"
}

// 发送群聊消息并@某人
{
    "toWxId": "123456@chatroom",
    "content": "大家好!",
    "messageType": "group",
    "atWxIds": "wxid_xxx,wxid_yyy"
}
```

## Web界面使用说明

### 1. 同步好友
- 点击左上角"同步好友"按钮
- 系统会从GeWe API获取最新的好友和群聊信息
- 同步完成后会显示同步的好友数量

### 2. 查看好友
- 左侧显示所有好友和群聊列表
- 使用搜索框可以快速查找好友
- 群聊会标注"群聊"标识

### 3. 发送消息
- 点击左侧好友列表中的任意好友
- 在右侧消息输入区域输入消息内容
- 选择消息类型（好友消息/群聊消息）
- 群聊消息可以填写要@的用户微信ID
- 点击"发送"按钮发送消息

## 项目结构

```
src/main/java/com/bogo/boot/
├── infra/
│   ├── configuration/
│   │   ├── GeWeApiConfiguration.java     # GeWe API配置
│   │   └── GeWeApiProperties.java        # 配置属性
│   ├── model/
│   │   ├── GeWeApiResponse.java          # API响应模型
│   │   └── GeWeFriendInfo.java          # 好友信息模型
│   └── service/
│       └── GeWeApiService.java           # GeWe API客户端
└── contact/
    ├── entity/
    │   └── GeWeFriend.java               # 好友实体类
    ├── repository/
    │   └── GeWeFriendRepository.java     # 好友数据访问层
    ├── service/
    │   ├── GeWeFriendSyncService.java    # 好友同步服务接口
    │   ├── GeWeMessageService.java       # 消息发送服务接口
    │   └── impl/
    │       ├── GeWeFriendSyncServiceImpl.java # 好友同步服务实现
    │       └── GeWeMessageServiceImpl.java    # 消息发送服务实现
    └── api/
        ├── GeWeFriendController.java     # Web控制器
        ├── vo/
        │   └── GeWeFriendVO.java         # 好友VO
        └── request/
            ├── SendMessageRequest.java   # 发送消息请求
            └── SyncFriendsRequest.java   # 同步好友请求
```

## 注意事项

1. **API配置**: 确保GeWe API的appId和token配置正确
2. **数据库**: 需要先创建gewe_friend表
3. **网络**: 确保服务器能够访问GeWe API（api.geweapi.com）
4. **频率限制**: 注意GeWe API的调用频率限制，避免请求过于频繁
5. **权限**: 确保GeWe账号有足够的权限访问通讯录和发送消息

## 故障排除

### 常见问题

1. **同步好友失败**
   - 检查GeWe API配置是否正确
   - 确认网络连接正常
   - 查看应用日志了解具体错误信息

2. **发送消息失败**
   - 确认目标好友存在且有效
   - 检查消息内容是否符合要求
   - 确认GeWe账号有发送消息权限

3. **页面无法访问**
   - 确认应用已正常启动
   - 检查端口8080是否被占用
   - 查看应用日志是否有启动错误

### 日志查看

应用日志会记录详细的操作信息，包括：
- API调用情况
- 好友同步过程
- 消息发送结果
- 错误信息等

## 技术支持

如需技术支持，请查看：
- [GeWe开放平台文档](http://doc.geweapi.com/)
- 项目issue或联系开发团队